/**
 * Health Tracker - Service Worker
 * خدمة العامل للتطبيق الصحي
 */

const CACHE_NAME = 'health-tracker-v1.0.0';
const STATIC_CACHE = 'health-static-v1';
const DYNAMIC_CACHE = 'health-dynamic-v1';

// الملفات المطلوب تخزينها مؤقتاً
const STATIC_FILES = [
    '/',
    '/index.html',
    '/styles/main.css',
    '/styles/animations.css',
    '/styles/components.css',
    '/js/utils.js',
    '/js/animations.js',
    '/js/main.js',
    '/assets/favicon.svg',
    '/manifest.json',
    // External resources
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// أحداث Service Worker

// التثبيت
self.addEventListener('install', (event) => {
    console.log('🔧 Health Tracker SW: تثبيت...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('📦 Health Tracker SW: تخزين الملفات الثابتة...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('✅ Health Tracker SW: تم التثبيت بنجاح');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Health Tracker SW: خطأ في التثبيت:', error);
            })
    );
});

// التفعيل
self.addEventListener('activate', (event) => {
    console.log('🚀 Health Tracker SW: تفعيل...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        // حذف التخزين المؤقت القديم
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Health Tracker SW: حذف تخزين قديم:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Health Tracker SW: تم التفعيل بنجاح');
                return self.clients.claim();
            })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', (event) => {
    const request = event.request;
    const url = new URL(request.url);
    
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // تجاهل طلبات POST (إرسال البيانات)
    if (request.method !== 'GET') {
        return;
    }
    
    event.respondWith(
        handleRequest(request)
    );
});

/**
 * معالجة الطلبات مع استراتيجية التخزين المؤقت
 */
async function handleRequest(request) {
    const url = new URL(request.url);
    
    try {
        // للملفات الثابتة: Cache First
        if (isStaticFile(request)) {
            return await cacheFirst(request);
        }
        
        // للموارد الخارجية: Stale While Revalidate
        if (isExternalResource(request)) {
            return await staleWhileRevalidate(request);
        }
        
        // للطلبات الأخرى: Network First
        return await networkFirst(request);
        
    } catch (error) {
        console.error('❌ Health Tracker SW: خطأ في معالجة الطلب:', error);
        
        // إرجاع صفحة خطأ مخصصة
        if (request.destination === 'document') {
            return await getOfflinePage();
        }
        
        // للموارد الأخرى، إرجاع استجابة فارغة
        return new Response('', { status: 408, statusText: 'Request Timeout' });
    }
}

/**
 * استراتيجية Cache First
 */
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
        const cache = await caches.open(STATIC_CACHE);
        cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
}

/**
 * استراتيجية Network First
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

/**
 * استراتيجية Stale While Revalidate
 */
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);
    
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            const cache = caches.open(DYNAMIC_CACHE);
            cache.then(c => c.put(request, networkResponse.clone()));
        }
        return networkResponse;
    });
    
    return cachedResponse || fetchPromise;
}

/**
 * التحقق من كون الملف ثابت
 */
function isStaticFile(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    return pathname.endsWith('.css') ||
           pathname.endsWith('.js') ||
           pathname.endsWith('.svg') ||
           pathname.endsWith('.png') ||
           pathname.endsWith('.jpg') ||
           pathname.endsWith('.jpeg') ||
           pathname.endsWith('.webp') ||
           pathname === '/' ||
           pathname === '/index.html' ||
           pathname === '/manifest.json';
}

/**
 * التحقق من كون المورد خارجي
 */
function isExternalResource(request) {
    const url = new URL(request.url);
    return url.origin !== self.location.origin;
}

/**
 * الحصول على صفحة عدم الاتصال
 */
async function getOfflinePage() {
    const offlineHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>صحتي - غير متصل</title>
            <style>
                body {
                    font-family: 'Cairo', sans-serif;
                    background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
                    color: white;
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    direction: rtl;
                }
                .offline-content {
                    max-width: 500px;
                    padding: 2rem;
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }
                h1 {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }
                p {
                    font-size: 1.1rem;
                    margin-bottom: 2rem;
                    opacity: 0.9;
                    line-height: 1.6;
                }
                .btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: 2px solid white;
                    padding: 12px 24px;
                    border-radius: 25px;
                    text-decoration: none;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    display: inline-block;
                }
                .btn:hover {
                    background: white;
                    color: #10b981;
                }
                .health-tips {
                    margin-top: 2rem;
                    padding: 1rem;
                    background: rgba(255,255,255,0.1);
                    border-radius: 15px;
                    backdrop-filter: blur(10px);
                }
                .tip {
                    margin: 0.5rem 0;
                    font-size: 0.9rem;
                }
            </style>
        </head>
        <body>
            <div class="offline-content">
                <div class="offline-icon">💚</div>
                <h1>لا يوجد اتصال بالإنترنت</h1>
                <p>
                    لا تقلق! تطبيق صحتي يعمل محلياً على جهازك.
                    يمكنك الاستمرار في تتبع عاداتك الصحية حتى بدون إنترنت.
                </p>
                <a href="/" class="btn">العودة للتطبيق</a>
                
                <div class="health-tips">
                    <h3>💡 نصائح صحية سريعة:</h3>
                    <div class="tip">🚰 اشرب كوب ماء كل ساعة</div>
                    <div class="tip">🚶‍♂️ امشِ 10,000 خطوة يومياً</div>
                    <div class="tip">😴 احصل على 7-8 ساعات نوم</div>
                    <div class="tip">🥗 تناول 5 حصص من الخضار والفواكه</div>
                </div>
            </div>
        </body>
        </html>
    `;
    
    return new Response(offlineHTML, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
}

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache().then(() => {
                event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
            });
            break;
            
        case 'SYNC_HEALTH_DATA':
            // مزامنة البيانات الصحية في الخلفية
            syncHealthData(data);
            break;
            
        default:
            console.log('Health Tracker SW: رسالة غير معروفة:', type);
    }
});

/**
 * حساب حجم التخزين المؤقت
 */
async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
    }
    
    return totalSize;
}

/**
 * مسح التخزين المؤقت
 */
async function clearCache() {
    const cacheNames = await caches.keys();
    
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    
    console.log('🗑️ Health Tracker SW: تم مسح جميع التخزين المؤقت');
}

/**
 * مزامنة البيانات الصحية
 */
async function syncHealthData(data) {
    try {
        // حفظ البيانات محلياً أولاً
        console.log('💾 Health Tracker SW: حفظ البيانات الصحية محلياً');
        
        // محاولة إرسال البيانات للخادم عند توفر الاتصال
        if (navigator.onLine) {
            console.log('🌐 Health Tracker SW: مزامنة البيانات مع الخادم');
            // سيتم تنفيذ المزامنة مع الخادم لاحقاً
        }
    } catch (error) {
        console.error('❌ Health Tracker SW: خطأ في مزامنة البيانات:', error);
    }
}

// تسجيل معلومات Service Worker
console.log('🔧 Health Tracker SW: تم تحميل الملف');
console.log('📦 Cache Name:', CACHE_NAME);
console.log('📁 Static Files:', STATIC_FILES.length, 'files');
