<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المصادقة - صحتي</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .test-btn.danger {
            background: #ef4444;
        }

        .test-btn.danger:hover {
            background: #dc2626;
        }

        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 500;
        }

        .result.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .result.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .result.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .status-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
        }

        .status-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .test-form {
            display: grid;
            gap: 1rem;
            margin-top: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: #374151;
        }

        .form-group input {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-family: inherit;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .link-btn {
            color: #10b981;
            text-decoration: none;
            font-weight: 500;
            margin: 0 0.5rem;
        }

        .link-btn:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔐 اختبار نظام المصادقة</h1>
            <p>اختبار شامل لجميع وظائف نظام تسجيل الدخول والمصادقة</p>
        </div>

        <!-- حالة النظام -->
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <button class="test-btn" onclick="checkSystemStatus()">فحص حالة النظام</button>
            <div id="systemStatusResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار تسجيل مستخدم جديد -->
        <div class="test-section">
            <h3>👤 اختبار التسجيل</h3>
            <div class="test-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>الاسم الكامل</label>
                        <input type="text" id="testName" placeholder="أحمد محمد" value="أحمد محمد">
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" id="testEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" id="testPassword" placeholder="123456" value="123456">
                    </div>
                    <div class="form-group">
                        <label>تأكيد كلمة المرور</label>
                        <input type="password" id="testConfirmPassword" placeholder="123456" value="123456">
                    </div>
                </div>
            </div>
            <button class="test-btn" onclick="testRegistration()">تسجيل مستخدم تجريبي</button>
            <div id="registrationResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3>🔑 اختبار تسجيل الدخول</h3>
            <div class="test-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" id="loginEmail" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" id="loginPassword" placeholder="123456" value="123456">
                    </div>
                </div>
            </div>
            <button class="test-btn" onclick="testLogin()">تسجيل الدخول</button>
            <button class="test-btn" onclick="testLoginWithRemember()">تسجيل الدخول مع التذكر</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- إدارة الجلسة -->
        <div class="test-section">
            <h3>⏰ إدارة الجلسة</h3>
            <button class="test-btn" onclick="checkSession()">فحص الجلسة الحالية</button>
            <button class="test-btn" onclick="getCurrentUser()">الحصول على المستخدم الحالي</button>
            <button class="test-btn danger" onclick="testLogout()">تسجيل الخروج</button>
            <div id="sessionResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار البيانات -->
        <div class="test-section">
            <h3>💾 اختبار البيانات</h3>
            <button class="test-btn" onclick="testUserData()">اختبار حفظ البيانات</button>
            <button class="test-btn" onclick="testDataSeparation()">اختبار فصل البيانات</button>
            <button class="test-btn" onclick="viewAllUsers()">عرض جميع المستخدمين</button>
            <button class="test-btn danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="dataResult" class="result" style="display: none;"></div>
        </div>

        <!-- الروابط السريعة -->
        <div class="test-section">
            <h3>🔗 روابط سريعة</h3>
            <a href="auth.html" class="link-btn">صفحة تسجيل الدخول</a>
            <a href="index.html" class="link-btn">التطبيق الرئيسي</a>
            <a href="test-functionality.html" class="link-btn">اختبار الوظائف</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/utils.js"></script>
    <script src="./js/storage.js"></script>
    <script src="./js/auth.js"></script>
    <script>
        let testAuth = null;

        // تهيئة نظام الاختبار
        document.addEventListener('DOMContentLoaded', function() {
            testAuth = new HealthAuth();
            console.log('🧪 تم تهيئة نظام اختبار المصادقة');
        });

        // عرض النتيجة
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // فحص حالة النظام
        function checkSystemStatus() {
            const status = {
                authLoaded: typeof HealthAuth !== 'undefined',
                storageLoaded: typeof HealthStorage !== 'undefined',
                utilsLoaded: typeof formatArabicDate !== 'undefined',
                currentUser: testAuth ? testAuth.getCurrentUser() : null,
                isLoggedIn: testAuth ? testAuth.isLoggedIn() : false,
                totalUsers: HealthStorage ? HealthStorage.getAllUsers().length : 0
            };

            const statusHtml = `
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">${status.authLoaded ? '✅' : '❌'}</div>
                        <div class="status-label">نظام المصادقة</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">${status.storageLoaded ? '✅' : '❌'}</div>
                        <div class="status-label">نظام التخزين</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">${status.isLoggedIn ? '✅' : '❌'}</div>
                        <div class="status-label">حالة تسجيل الدخول</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">${status.totalUsers}</div>
                        <div class="status-label">عدد المستخدمين</div>
                    </div>
                </div>
                ${status.currentUser ? `<p><strong>المستخدم الحالي:</strong> ${status.currentUser.name} (${status.currentUser.email})</p>` : ''}
            `;

            showResult('systemStatusResult', statusHtml, 'success');
        }

        // اختبار التسجيل
        async function testRegistration() {
            const userData = {
                name: document.getElementById('testName').value,
                email: document.getElementById('testEmail').value,
                password: document.getElementById('testPassword').value,
                confirmPassword: document.getElementById('testConfirmPassword').value,
                age: 25,
                gender: 'male',
                weight: 70,
                height: 175
            };

            try {
                const result = await testAuth.register(userData);
                
                if (result.success) {
                    showResult('registrationResult', `✅ تم التسجيل بنجاح!<br>المستخدم: ${result.user.name}<br>البريد: ${result.user.email}`, 'success');
                } else {
                    showResult('registrationResult', `❌ فشل التسجيل: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('registrationResult', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        // اختبار تسجيل الدخول
        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const result = await testAuth.login(email, password, false);
                
                if (result.success) {
                    showResult('loginResult', `✅ تم تسجيل الدخول بنجاح!<br>المستخدم: ${result.user.name}`, 'success');
                } else {
                    showResult('loginResult', `❌ فشل تسجيل الدخول: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        // اختبار تسجيل الدخول مع التذكر
        async function testLoginWithRemember() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const result = await testAuth.login(email, password, true);
                
                if (result.success) {
                    showResult('loginResult', `✅ تم تسجيل الدخول مع التذكر!<br>المستخدم: ${result.user.name}`, 'success');
                } else {
                    showResult('loginResult', `❌ فشل تسجيل الدخول: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        // فحص الجلسة
        function checkSession() {
            const session = localStorage.getItem('health_app_session');
            const remember = localStorage.getItem('health_app_remember');
            
            let sessionInfo = '<h4>معلومات الجلسة:</h4>';
            
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    const isValid = new Date().getTime() < sessionData.expiresAt;
                    sessionInfo += `<p>✅ جلسة نشطة: ${isValid ? 'صالحة' : 'منتهية الصلاحية'}</p>`;
                    sessionInfo += `<p>المستخدم: ${sessionData.user.name}</p>`;
                    sessionInfo += `<p>تنتهي في: ${new Date(sessionData.expiresAt).toLocaleString('ar-SA')}</p>`;
                } catch (error) {
                    sessionInfo += '<p>❌ خطأ في قراءة الجلسة</p>';
                }
            } else {
                sessionInfo += '<p>❌ لا توجد جلسة نشطة</p>';
            }
            
            if (remember) {
                sessionInfo += '<p>✅ تم تفعيل "تذكرني"</p>';
            }

            showResult('sessionResult', sessionInfo, 'info');
        }

        // الحصول على المستخدم الحالي
        function getCurrentUser() {
            const user = testAuth.getCurrentUser();
            
            if (user) {
                const userInfo = `
                    <h4>المستخدم الحالي:</h4>
                    <p><strong>الاسم:</strong> ${user.name}</p>
                    <p><strong>البريد:</strong> ${user.email}</p>
                    <p><strong>تاريخ الانضمام:</strong> ${new Date(user.joinDate).toLocaleDateString('ar-SA')}</p>
                `;
                showResult('sessionResult', userInfo, 'success');
            } else {
                showResult('sessionResult', '❌ لا يوجد مستخدم مسجل دخول', 'error');
            }
        }

        // تسجيل الخروج
        function testLogout() {
            testAuth.logout();
            showResult('sessionResult', '✅ تم تسجيل الخروج بنجاح', 'success');
        }

        // اختبار بيانات المستخدم
        function testUserData() {
            if (!testAuth.isLoggedIn()) {
                showResult('dataResult', '❌ يجب تسجيل الدخول أولاً', 'error');
                return;
            }

            // إضافة بيانات تجريبية
            HealthStorage.addWaterEntry({
                id: Date.now(),
                amount: 250,
                timestamp: new Date().toISOString()
            });

            HealthStorage.addSleepEntry({
                id: Date.now(),
                duration: 8,
                quality: 'good',
                timestamp: new Date().toISOString()
            });

            const waterData = HealthStorage.getWaterData();
            const sleepData = HealthStorage.getSleepData();

            showResult('dataResult', `✅ تم حفظ البيانات بنجاح<br>الماء: ${waterData.length} إدخال<br>النوم: ${sleepData.length} إدخال`, 'success');
        }

        // اختبار فصل البيانات
        function testDataSeparation() {
            const users = HealthStorage.getAllUsers();
            let separationInfo = '<h4>فصل البيانات بين المستخدمين:</h4>';
            
            users.forEach(user => {
                const userId = user.id;
                const waterKey = `health_app_user_${userId}_water`;
                const sleepKey = `health_app_user_${userId}_sleep`;
                
                const waterData = JSON.parse(localStorage.getItem(waterKey) || '[]');
                const sleepData = JSON.parse(localStorage.getItem(sleepKey) || '[]');
                
                separationInfo += `<p><strong>${user.name}:</strong> ${waterData.length} ماء، ${sleepData.length} نوم</p>`;
            });

            showResult('dataResult', separationInfo, 'info');
        }

        // عرض جميع المستخدمين
        function viewAllUsers() {
            const users = HealthStorage.getAllUsers();
            
            if (users.length === 0) {
                showResult('dataResult', '❌ لا يوجد مستخدمين مسجلين', 'error');
                return;
            }

            let usersInfo = '<h4>جميع المستخدمين:</h4>';
            users.forEach((user, index) => {
                usersInfo += `
                    <p><strong>${index + 1}. ${user.name}</strong></p>
                    <p>البريد: ${user.email}</p>
                    <p>تاريخ الانضمام: ${new Date(user.joinDate).toLocaleDateString('ar-SA')}</p>
                    <p>آخر تسجيل دخول: ${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}</p>
                    <hr>
                `;
            });

            showResult('dataResult', usersInfo, 'info');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.clear();
                showResult('dataResult', '✅ تم مسح جميع البيانات', 'success');
                
                // إعادة تهيئة النظام
                testAuth = new HealthAuth();
            }
        }
    </script>
</body>
</html>
