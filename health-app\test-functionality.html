<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وظائف التطبيق الصحي</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            padding: 20px;
            background: #f8fafc;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
        }
        .test-section h3 {
            color: #10b981;
            margin-bottom: 15px;
        }
        .test-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
        .success { background: #f0fdf4; border-left-color: #22c55e; }
        .error { background: #fef2f2; border-left-color: #ef4444; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار وظائف التطبيق الصحي "صحتي"</h1>
        <p>هذه الصفحة لاختبار جميع وظائف التطبيق والتأكد من عملها بشكل صحيح</p>

        <!-- اختبار تحميل المكتبات -->
        <div class="test-section">
            <h3>📚 اختبار تحميل المكتبات</h3>
            <button class="test-btn" onclick="testLibraries()">اختبار المكتبات</button>
            <div id="librariesResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار التخزين المحلي -->
        <div class="test-section">
            <h3>💾 اختبار التخزين المحلي</h3>
            <button class="test-btn" onclick="testStorage()">اختبار التخزين</button>
            <div id="storageResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار إضافة بيانات الماء -->
        <div class="test-section">
            <h3>💧 اختبار تتبع الماء</h3>
            <button class="test-btn" onclick="testWaterTracking()">إضافة كوب ماء</button>
            <button class="test-btn" onclick="clearWaterData()">مسح بيانات الماء</button>
            <div id="waterResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار النوم -->
        <div class="test-section">
            <h3>🌙 اختبار تتبع النوم</h3>
            <button class="test-btn" onclick="testSleepTracking()">إضافة بيانات نوم</button>
            <button class="test-btn" onclick="clearSleepData()">مسح بيانات النوم</button>
            <div id="sleepResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار التنقل -->
        <div class="test-section">
            <h3>🧭 اختبار التنقل</h3>
            <button class="test-btn" onclick="testNavigation('water')">الانتقال لصفحة الماء</button>
            <button class="test-btn" onclick="testNavigation('sleep')">الانتقال لصفحة النوم</button>
            <button class="test-btn" onclick="testNavigation('dashboard')">العودة للوحة التحكم</button>
            <div id="navigationResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار الإجراءات السريعة -->
        <div class="test-section">
            <h3>⚡ اختبار الإجراءات السريعة</h3>
            <button class="test-btn" onclick="testQuickActions()">اختبار الإجراءات السريعة</button>
            <div id="quickActionsResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض حالة التطبيق -->
        <div class="test-section">
            <h3>📊 حالة التطبيق</h3>
            <button class="test-btn" onclick="showAppStatus()">عرض حالة التطبيق</button>
            <div id="appStatusResult" class="result" style="display: none;"></div>
        </div>

        <!-- رابط للعودة للتطبيق -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="background: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 10px; font-weight: bold;">
                🏠 العودة للتطبيق الرئيسي
            </a>
        </div>
    </div>

    <!-- تحميل مكتبات التطبيق -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="./js/utils.js"></script>
    <script src="./js/storage.js"></script>
    <script src="./js/charts.js"></script>
    <script src="./js/navigation.js"></script>
    <script src="./js/dashboard.js"></script>
    <script src="./js/sleep.js"></script>
    <script src="./js/water.js"></script>
    <script src="./js/exercise.js"></script>
    <script src="./js/nutrition.js"></script>
    <script src="./js/reports.js"></script>
    <script src="./js/goals.js"></script>
    <script src="./js/reminders.js"></script>
    <script src="./js/profile.js"></script>
    <script src="./js/settings.js"></script>
    <script src="./js/app.js"></script>

    <script>
        // متغير للتطبيق
        let testApp = null;

        // تهيئة التطبيق للاختبار
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof HealthApp !== 'undefined') {
                    testApp = new HealthApp();
                    console.log('✅ تم تهيئة التطبيق للاختبار');
                }
            }, 1000);
        });

        function showResult(elementId, message, type = 'result') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        function testLibraries() {
            let results = [];

            // اختبار Chart.js
            results.push(`Chart.js: ${typeof Chart !== 'undefined' ? '✅ محمل' : '❌ غير محمل'}`);

            // اختبار فئات التطبيق الأساسية
            const coreClasses = ['HealthApp', 'HealthStorage', 'HealthCharts'];
            coreClasses.forEach(className => {
                const isLoaded = typeof window[className] !== 'undefined';
                results.push(`${className}: ${isLoaded ? '✅ محمل' : '❌ غير محمل'}`);
            });

            // اختبار متتبعات الصحة
            const trackers = ['WaterTracker', 'SleepTracker', 'ExerciseTracker', 'NutritionTracker'];
            trackers.forEach(className => {
                const isLoaded = typeof window[className] !== 'undefined';
                results.push(`${className}: ${isLoaded ? '✅ محمل' : '❌ غير محمل'}`);
            });

            // اختبار المدراء
            const managers = ['ReportsManager', 'GoalsManager', 'RemindersManager', 'ProfileManager', 'SettingsManager'];
            managers.forEach(className => {
                const isLoaded = typeof window[className] !== 'undefined';
                results.push(`${className}: ${isLoaded ? '✅ محمل' : '❌ غير محمل'}`);
            });

            // اختبار الدوال المساعدة
            const utils = ['formatArabicDate', 'calculateBMI', 'debounce'];
            utils.forEach(funcName => {
                const isLoaded = typeof window[funcName] !== 'undefined';
                results.push(`${funcName}: ${isLoaded ? '✅ محمل' : '❌ غير محمل'}`);
            });

            showResult('librariesResult', results.join('<br>'), 'success');
        }

        function testStorage() {
            try {
                // اختبار حفظ واسترجاع البيانات
                const testData = { test: 'data', timestamp: new Date().toISOString() };
                localStorage.setItem('healthapp_test', JSON.stringify(testData));
                
                const retrieved = JSON.parse(localStorage.getItem('healthapp_test'));
                
                if (retrieved && retrieved.test === 'data') {
                    showResult('storageResult', '✅ التخزين المحلي يعمل بشكل صحيح', 'success');
                    localStorage.removeItem('healthapp_test');
                } else {
                    showResult('storageResult', '❌ مشكلة في التخزين المحلي', 'error');
                }
            } catch (error) {
                showResult('storageResult', `❌ خطأ في التخزين: ${error.message}`, 'error');
            }
        }

        function testWaterTracking() {
            try {
                // اختبار إضافة الماء مباشرة عبر HealthStorage
                const waterEntry = {
                    id: Date.now(),
                    amount: 250,
                    timestamp: new Date().toISOString(),
                    type: 'glass'
                };

                HealthStorage.addWaterEntry(waterEntry);

                // التحقق من الحفظ
                const waterData = HealthStorage.getWaterData();
                const todayIntake = HealthStorage.getTodayWaterIntake();

                showResult('waterResult', `✅ تم إضافة كوب ماء (250ml) بنجاح<br>إجمالي اليوم: ${todayIntake}ml<br>عدد الإدخالات: ${waterData.length}`, 'success');

                // تحديث لوحة التحكم إذا كانت متاحة
                if (testApp && testApp.dashboard) {
                    testApp.dashboard.updateStats();
                }

            } catch (error) {
                showResult('waterResult', `❌ خطأ في إضافة الماء: ${error.message}`, 'error');
            }
        }

        function clearWaterData() {
            try {
                HealthStorage.saveWaterData([]);
                showResult('waterResult', '✅ تم مسح بيانات الماء', 'success');

                // تحديث لوحة التحكم
                if (testApp && testApp.dashboard) {
                    testApp.dashboard.updateStats();
                }
            } catch (error) {
                showResult('waterResult', `❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function testSleepTracking() {
            try {
                // إضافة بيانات نوم تجريبية
                const sleepEntry = {
                    id: Date.now(),
                    duration: 7.5,
                    quality: 'good',
                    bedTime: '23:00',
                    wakeTime: '06:30',
                    timestamp: new Date().toISOString(),
                    date: new Date().toISOString().split('T')[0]
                };

                HealthStorage.addSleepEntry(sleepEntry);

                // التحقق من الحفظ
                const sleepData = HealthStorage.getSleepData();
                const lastSleep = HealthStorage.getLastSleepDuration();

                showResult('sleepResult', `✅ تم إضافة بيانات نوم (7.5 ساعة) بنجاح<br>آخر نوم: ${lastSleep} ساعة<br>عدد الإدخالات: ${sleepData.length}`, 'success');

                // تحديث لوحة التحكم إذا كانت متاحة
                if (testApp && testApp.dashboard) {
                    testApp.dashboard.updateStats();
                }

            } catch (error) {
                showResult('sleepResult', `❌ خطأ في إضافة النوم: ${error.message}`, 'error');
            }
        }

        function clearSleepData() {
            try {
                HealthStorage.saveSleepData([]);
                showResult('sleepResult', '✅ تم مسح بيانات النوم', 'success');

                // تحديث لوحة التحكم
                if (testApp && testApp.dashboard) {
                    testApp.dashboard.updateStats();
                }
            } catch (error) {
                showResult('sleepResult', `❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function testNavigation(page) {
            if (!testApp) {
                showResult('navigationResult', '❌ التطبيق غير متاح', 'error');
                return;
            }

            try {
                testApp.loadPage(page);
                showResult('navigationResult', `✅ تم الانتقال لصفحة ${page} بنجاح`, 'success');
            } catch (error) {
                showResult('navigationResult', `❌ خطأ في التنقل: ${error.message}`, 'error');
            }
        }

        function testQuickActions() {
            if (!testApp) {
                showResult('quickActionsResult', '❌ التطبيق غير متاح', 'error');
                return;
            }

            try {
                const actions = ['add-water', 'log-sleep', 'start-workout', 'add-meal'];
                let results = [];
                
                actions.forEach(action => {
                    try {
                        testApp.handleQuickAction(action);
                        results.push(`✅ ${action}: نجح`);
                    } catch (error) {
                        results.push(`❌ ${action}: فشل`);
                    }
                });

                showResult('quickActionsResult', results.join('<br>'), 'success');
            } catch (error) {
                showResult('quickActionsResult', `❌ خطأ في الإجراءات السريعة: ${error.message}`, 'error');
            }
        }

        function showAppStatus() {
            try {
                // جمع البيانات من التخزين المحلي
                const user = HealthStorage.getUser() || { name: 'غير محدد', goals: {} };
                const waterData = HealthStorage.getWaterData() || [];
                const sleepData = HealthStorage.getSleepData() || [];
                const exerciseData = HealthStorage.getExerciseData() || [];
                const nutritionData = HealthStorage.getNutritionData() || [];

                // حساب الإحصائيات اليومية
                const todayWater = HealthStorage.getTodayWaterIntake();
                const lastSleep = HealthStorage.getLastSleepDuration();
                const todayExercise = HealthStorage.getTodayExerciseMinutes();
                const todayCalories = HealthStorage.getTodayCalories();

                const status = [
                    `📱 حالة التطبيق: ${testApp ? '✅ يعمل' : '❌ غير متاح'}`,
                    `📄 الصفحة الحالية: ${testApp ? testApp.currentPage : 'غير محدد'}`,
                    `👤 اسم المستخدم: ${user.name}`,
                    `🎯 هدف الماء: ${user.goals.water || 2.5} لتر`,
                    `🎯 هدف النوم: ${user.goals.sleep || 8} ساعة`,
                    `💧 عدد إدخالات الماء: ${waterData.length}`,
                    `💧 استهلاك الماء اليوم: ${todayWater}ml`,
                    `🌙 عدد إدخالات النوم: ${sleepData.length}`,
                    `🌙 آخر نوم: ${lastSleep} ساعة`,
                    `🏃‍♂️ عدد إدخالات التمارين: ${exerciseData.length}`,
                    `🏃‍♂️ دقائق التمرين اليوم: ${todayExercise}`,
                    `🍎 عدد إدخالات التغذية: ${nutritionData.length}`,
                    `🍎 السعرات اليوم: ${todayCalories}`,
                    `💾 حجم البيانات: ${(HealthStorage.getStorageSize() / 1024).toFixed(2)} KB`
                ];

                showResult('appStatusResult', status.join('<br>'), 'success');
            } catch (error) {
                showResult('appStatusResult', `❌ خطأ في عرض الحالة: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
