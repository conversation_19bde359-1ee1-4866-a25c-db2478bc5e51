/* ===== HEALTH APP - MAIN STYLES ===== */

/* CSS Reset & Variables */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Health Colors */
    --primary-color: #10b981;
    --primary-dark: #059669;
    --primary-light: #34d399;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    
    /* Health Theme Colors */
    --sleep-color: #6366f1;
    --water-color: #06b6d4;
    --exercise-color: #f59e0b;
    --nutrition-color: #10b981;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-sleep: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    --gradient-water: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-exercise: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-nutrition: linear-gradient(135deg, #10b981 0%, #059669 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Typography */
    --font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-600);
    line-height: 1.7;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-slow);
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--spacing-xl);
}

.loading-logo i {
    font-size: var(--font-size-4xl);
    animation: heartbeat 1.5s ease-in-out infinite;
}

.loading-progress {
    width: 300px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-fill {
    height: 100%;
    background: var(--white);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    width: 0%;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
    background: var(--gray-50);
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--white);
    border-left: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: var(--z-fixed);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
}

.logo i {
    font-size: var(--font-size-2xl);
    animation: heartbeat 2s ease-in-out infinite;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.nav-link.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
}

.nav-link i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.user-details {
    flex: 1;
}

.user-name {
    display: block;
    font-weight: 600;
    color: var(--gray-800);
    font-size: var(--font-size-sm);
}

.user-status {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.header {
    height: var(--header-height);
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-xl);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-sm);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.header-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: var(--accent-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
}

.date-display {
    background: var(--gray-100);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

/* Page Content */
.page-content {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    margin-bottom: var(--spacing-2xl);
}

.page-header h2 {
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    margin: 0;
}

/* Animations */
@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: var(--spacing-lg);
    }
    
    .header {
        padding: 0 var(--spacing-lg);
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
    
    .date-display {
        display: none;
    }
}

/* ===== USER MENU ===== */
.user-menu {
    position: relative;
    margin-left: var(--spacing-md);
}

.user-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: rgba(16, 185, 129, 0.1);
    border: none;
    border-radius: var(--radius-full);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--primary-color);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-family: inherit;
    font-weight: 500;
}

.user-btn:hover {
    background: rgba(16, 185, 129, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.user-name {
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.user-btn i {
    font-size: var(--font-size-xs);
    transition: transform var(--transition-normal);
}

.user-menu.active .user-btn i {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: var(--z-dropdown);
    margin-top: var(--spacing-sm);
    border: 1px solid var(--gray-200);
}

.user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: var(--radius-lg);
    margin: var(--spacing-xs);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
    transform: translateX(-2px);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    font-size: var(--font-size-sm);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-sm) 0;
}

.logout-btn {
    color: #ef4444 !important;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    color: #dc2626 !important;
}

/* User menu responsive */
@media (max-width: 768px) {
    .user-name {
        display: none;
    }

    .user-dropdown {
        right: -50px;
        min-width: 180px;
    }

    .user-menu {
        margin-left: var(--spacing-sm);
    }
}
