/**
 * Health App - Smart Notifications System
 * نظام الإشعارات الذكية
 */

class SmartNotifications {
    constructor() {
        this.notifications = [];
        this.settings = {
            enabled: true,
            waterReminders: true,
            sleepReminders: true,
            exerciseReminders: true,
            nutritionReminders: true,
            achievementNotifications: true,
            smartTiming: true
        };
        this.intervals = {};
        this.userHabits = {};
        
        this.init();
    }

    /**
     * تهيئة نظام الإشعارات
     */
    init() {
        console.log('🔔 تهيئة نظام الإشعارات الذكية');
        
        // طلب إذن الإشعارات
        this.requestPermission();
        
        // تحميل الإعدادات
        this.loadSettings();
        
        // تحليل عادات المستخدم
        this.analyzeUserHabits();
        
        // بدء الإشعارات الذكية
        this.startSmartReminders();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
    }

    /**
     * طلب إذن الإشعارات
     */
    async requestPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                const permission = await Notification.requestPermission();
                console.log(`إذن الإشعارات: ${permission}`);
            }
        } else {
            console.warn('المتصفح لا يدعم الإشعارات');
        }
    }

    /**
     * تحميل الإعدادات
     */
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem('health_app_notification_settings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            }
        } catch (error) {
            console.error('خطأ في تحميل إعدادات الإشعارات:', error);
        }
    }

    /**
     * حفظ الإعدادات
     */
    saveSettings() {
        try {
            localStorage.setItem('health_app_notification_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('خطأ في حفظ إعدادات الإشعارات:', error);
        }
    }

    /**
     * تحليل عادات المستخدم
     */
    analyzeUserHabits() {
        try {
            // تحليل عادات شرب الماء
            const waterData = HealthStorage.getWaterData() || [];
            this.userHabits.water = this.analyzeWaterHabits(waterData);
            
            // تحليل عادات النوم
            const sleepData = HealthStorage.getSleepData() || [];
            this.userHabits.sleep = this.analyzeSleepHabits(sleepData);
            
            // تحليل عادات التمارين
            const exerciseData = HealthStorage.getExerciseData() || [];
            this.userHabits.exercise = this.analyzeExerciseHabits(exerciseData);
            
            console.log('📊 تم تحليل عادات المستخدم:', this.userHabits);
            
        } catch (error) {
            console.error('خطأ في تحليل عادات المستخدم:', error);
        }
    }

    /**
     * تحليل عادات شرب الماء
     */
    analyzeWaterHabits(waterData) {
        const habits = {
            averageDaily: 0,
            peakHours: [],
            frequency: 0,
            lastDrink: null
        };

        if (waterData.length === 0) return habits;

        // حساب المتوسط اليومي
        const dailyTotals = {};
        waterData.forEach(entry => {
            const date = new Date(entry.timestamp).toDateString();
            dailyTotals[date] = (dailyTotals[date] || 0) + entry.amount;
        });

        const totalDays = Object.keys(dailyTotals).length;
        const totalAmount = Object.values(dailyTotals).reduce((sum, amount) => sum + amount, 0);
        habits.averageDaily = totalAmount / totalDays;

        // تحليل الساعات الذروة
        const hourCounts = {};
        waterData.forEach(entry => {
            const hour = new Date(entry.timestamp).getHours();
            hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });

        habits.peakHours = Object.entries(hourCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([hour]) => parseInt(hour));

        // حساب التكرار (مرات في اليوم)
        habits.frequency = waterData.length / totalDays;

        // آخر مرة شرب ماء
        if (waterData.length > 0) {
            habits.lastDrink = new Date(waterData[waterData.length - 1].timestamp);
        }

        return habits;
    }

    /**
     * تحليل عادات النوم
     */
    analyzeSleepHabits(sleepData) {
        const habits = {
            averageDuration: 0,
            averageBedTime: null,
            averageWakeTime: null,
            sleepQuality: 'unknown'
        };

        if (sleepData.length === 0) return habits;

        // حساب متوسط مدة النوم
        const totalDuration = sleepData.reduce((sum, entry) => sum + (entry.duration || 0), 0);
        habits.averageDuration = totalDuration / sleepData.length;

        // حساب متوسط أوقات النوم والاستيقاظ
        const bedTimes = sleepData.filter(entry => entry.bedTime).map(entry => entry.bedTime);
        const wakeTimes = sleepData.filter(entry => entry.wakeTime).map(entry => entry.wakeTime);

        if (bedTimes.length > 0) {
            habits.averageBedTime = this.calculateAverageTime(bedTimes);
        }

        if (wakeTimes.length > 0) {
            habits.averageWakeTime = this.calculateAverageTime(wakeTimes);
        }

        // تحليل جودة النوم
        const qualityEntries = sleepData.filter(entry => entry.quality);
        if (qualityEntries.length > 0) {
            const qualityScores = qualityEntries.map(entry => {
                switch (entry.quality) {
                    case 'excellent': return 5;
                    case 'good': return 4;
                    case 'fair': return 3;
                    case 'poor': return 2;
                    case 'very_poor': return 1;
                    default: return 3;
                }
            });
            const averageQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
            
            if (averageQuality >= 4.5) habits.sleepQuality = 'excellent';
            else if (averageQuality >= 3.5) habits.sleepQuality = 'good';
            else if (averageQuality >= 2.5) habits.sleepQuality = 'fair';
            else habits.sleepQuality = 'poor';
        }

        return habits;
    }

    /**
     * تحليل عادات التمارين
     */
    analyzeExerciseHabits(exerciseData) {
        const habits = {
            averageDaily: 0,
            preferredTimes: [],
            frequency: 0,
            favoriteTypes: []
        };

        if (exerciseData.length === 0) return habits;

        // حساب المتوسط اليومي
        const dailyTotals = {};
        exerciseData.forEach(entry => {
            const date = new Date(entry.timestamp).toDateString();
            dailyTotals[date] = (dailyTotals[date] || 0) + (entry.duration || 0);
        });

        const totalDays = Object.keys(dailyTotals).length;
        const totalMinutes = Object.values(dailyTotals).reduce((sum, minutes) => sum + minutes, 0);
        habits.averageDaily = totalMinutes / totalDays;

        // تحليل الأوقات المفضلة
        const hourCounts = {};
        exerciseData.forEach(entry => {
            const hour = new Date(entry.timestamp).getHours();
            hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });

        habits.preferredTimes = Object.entries(hourCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 2)
            .map(([hour]) => parseInt(hour));

        // حساب التكرار
        habits.frequency = exerciseData.length / totalDays;

        // تحليل الأنواع المفضلة
        const typeCounts = {};
        exerciseData.forEach(entry => {
            if (entry.type) {
                typeCounts[entry.type] = (typeCounts[entry.type] || 0) + 1;
            }
        });

        habits.favoriteTypes = Object.entries(typeCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([type]) => type);

        return habits;
    }

    /**
     * حساب متوسط الوقت
     */
    calculateAverageTime(times) {
        const totalMinutes = times.reduce((sum, time) => {
            const [hours, minutes] = time.split(':').map(Number);
            return sum + (hours * 60) + minutes;
        }, 0);

        const averageMinutes = totalMinutes / times.length;
        const hours = Math.floor(averageMinutes / 60);
        const minutes = Math.round(averageMinutes % 60);

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    /**
     * بدء الإشعارات الذكية
     */
    startSmartReminders() {
        if (!this.settings.enabled) return;

        // إشعارات الماء الذكية
        if (this.settings.waterReminders) {
            this.startWaterReminders();
        }

        // إشعارات النوم الذكية
        if (this.settings.sleepReminders) {
            this.startSleepReminders();
        }

        // إشعارات التمارين الذكية
        if (this.settings.exerciseReminders) {
            this.startExerciseReminders();
        }

        console.log('🔔 تم بدء الإشعارات الذكية');
    }

    /**
     * بدء إشعارات الماء الذكية
     */
    startWaterReminders() {
        // إشعار كل ساعة إذا لم يشرب المستخدم ماء
        this.intervals.water = setInterval(() => {
            const lastDrink = this.userHabits.water?.lastDrink;
            const now = new Date();
            
            if (!lastDrink || (now - lastDrink) > 60 * 60 * 1000) { // ساعة واحدة
                this.sendNotification({
                    title: '💧 وقت شرب الماء!',
                    body: 'لم تشرب ماء منذ ساعة. حافظ على ترطيب جسمك!',
                    type: 'water',
                    action: 'addWater'
                });
            }
        }, 60 * 60 * 1000); // كل ساعة
    }

    /**
     * بدء إشعارات النوم الذكية
     */
    startSleepReminders() {
        // إشعار قبل وقت النوم المعتاد بـ 30 دقيقة
        const bedTime = this.userHabits.sleep?.averageBedTime;
        if (bedTime) {
            const [hours, minutes] = bedTime.split(':').map(Number);
            const reminderTime = new Date();
            reminderTime.setHours(hours, minutes - 30, 0, 0);
            
            const now = new Date();
            const timeUntilReminder = reminderTime - now;
            
            if (timeUntilReminder > 0) {
                setTimeout(() => {
                    this.sendNotification({
                        title: '🌙 وقت الاستعداد للنوم',
                        body: 'حان وقت الاستعداد للنوم للحصول على راحة جيدة!',
                        type: 'sleep',
                        action: 'goToSleep'
                    });
                }, timeUntilReminder);
            }
        }
    }

    /**
     * بدء إشعارات التمارين الذكية
     */
    startExerciseReminders() {
        // إشعار يومي في الوقت المفضل للتمارين
        const preferredTimes = this.userHabits.exercise?.preferredTimes || [18]; // 6 مساءً افتراضي
        
        preferredTimes.forEach(hour => {
            const reminderTime = new Date();
            reminderTime.setHours(hour, 0, 0, 0);
            
            const now = new Date();
            const timeUntilReminder = reminderTime - now;
            
            if (timeUntilReminder > 0) {
                setTimeout(() => {
                    this.sendNotification({
                        title: '🏃‍♂️ وقت التمرين!',
                        body: 'حان وقت ممارسة الرياضة. جسمك يحتاج للحركة!',
                        type: 'exercise',
                        action: 'startExercise'
                    });
                }, timeUntilReminder);
            }
        });
    }

    /**
     * إرسال إشعار
     */
    sendNotification(notification) {
        try {
            // إشعار المتصفح
            if (Notification.permission === 'granted') {
                const browserNotification = new Notification(notification.title, {
                    body: notification.body,
                    icon: '/favicon.ico',
                    badge: '/favicon.ico',
                    tag: notification.type,
                    requireInteraction: false,
                    silent: false
                });

                // إغلاق الإشعار بعد 5 ثوان
                setTimeout(() => {
                    browserNotification.close();
                }, 5000);

                // معالجة النقر على الإشعار
                browserNotification.onclick = () => {
                    window.focus();
                    this.handleNotificationAction(notification.action);
                    browserNotification.close();
                };
            }

            // إشعار داخل التطبيق
            this.showInAppNotification(notification);

            // حفظ الإشعار في السجل
            this.notifications.push({
                ...notification,
                timestamp: new Date().toISOString(),
                read: false
            });

            // الاحتفاظ بآخر 50 إشعار فقط
            if (this.notifications.length > 50) {
                this.notifications = this.notifications.slice(-50);
            }

            this.saveNotifications();

        } catch (error) {
            console.error('خطأ في إرسال الإشعار:', error);
        }
    }

    /**
     * عرض إشعار داخل التطبيق
     */
    showInAppNotification(notification) {
        const notificationElement = document.createElement('div');
        notificationElement.className = `in-app-notification ${notification.type}`;
        notificationElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    ${this.getNotificationIcon(notification.type)}
                </div>
                <div class="notification-text">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-body">${notification.body}</div>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // إضافة الأنماط
        notificationElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left: 4px solid ${this.getNotificationColor(notification.type)};
            padding: 1rem;
            max-width: 350px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        `;

        document.body.appendChild(notificationElement);

        // إظهار الإشعار
        setTimeout(() => {
            notificationElement.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار تلقائياً
        setTimeout(() => {
            notificationElement.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notificationElement.parentNode) {
                    notificationElement.parentNode.removeChild(notificationElement);
                }
            }, 300);
        }, 5000);

        // معالجة النقر على الإشعار
        notificationElement.addEventListener('click', () => {
            this.handleNotificationAction(notification.action);
            notificationElement.remove();
        });
    }

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon(type) {
        const icons = {
            water: '<i class="fas fa-tint"></i>',
            sleep: '<i class="fas fa-moon"></i>',
            exercise: '<i class="fas fa-dumbbell"></i>',
            nutrition: '<i class="fas fa-apple-alt"></i>',
            achievement: '<i class="fas fa-trophy"></i>',
            reminder: '<i class="fas fa-bell"></i>'
        };
        return icons[type] || '<i class="fas fa-info-circle"></i>';
    }

    /**
     * الحصول على لون الإشعار
     */
    getNotificationColor(type) {
        const colors = {
            water: '#06b6d4',
            sleep: '#8b5cf6',
            exercise: '#f59e0b',
            nutrition: '#10b981',
            achievement: '#eab308',
            reminder: '#3b82f6'
        };
        return colors[type] || '#6b7280';
    }

    /**
     * معالجة إجراء الإشعار
     */
    handleNotificationAction(action) {
        switch (action) {
            case 'addWater':
                this.addWaterQuick();
                break;
            case 'goToSleep':
                this.navigateToSleep();
                break;
            case 'startExercise':
                this.navigateToExercise();
                break;
            case 'addMeal':
                this.navigateToNutrition();
                break;
            default:
                console.log('إجراء غير معروف:', action);
        }
    }

    /**
     * إضافة ماء سريع
     */
    addWaterQuick() {
        try {
            const waterEntry = {
                id: Date.now(),
                amount: 250,
                timestamp: new Date().toISOString(),
                type: 'glass'
            };

            HealthStorage.addWaterEntry(waterEntry);

            // تحديث عادات المستخدم
            this.analyzeUserHabits();

            // عرض إشعار نجاح
            this.showInAppNotification({
                title: '✅ تم إضافة الماء',
                body: 'تم إضافة كوب ماء (250ml) بنجاح!',
                type: 'water'
            });

        } catch (error) {
            console.error('خطأ في إضافة الماء:', error);
        }
    }

    /**
     * الانتقال لصفحة النوم
     */
    navigateToSleep() {
        if (window.healthApp && window.healthApp.loadPage) {
            window.healthApp.loadPage('sleep');
        } else {
            window.location.hash = '#sleep';
        }
    }

    /**
     * الانتقال لصفحة التمارين
     */
    navigateToExercise() {
        if (window.healthApp && window.healthApp.loadPage) {
            window.healthApp.loadPage('exercise');
        } else {
            window.location.hash = '#exercise';
        }
    }

    /**
     * الانتقال لصفحة التغذية
     */
    navigateToNutrition() {
        if (window.healthApp && window.healthApp.loadPage) {
            window.healthApp.loadPage('nutrition');
        } else {
            window.location.hash = '#nutrition';
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة إضافة بيانات جديدة لتحديث العادات
        document.addEventListener('dataUpdated', () => {
            this.analyzeUserHabits();
        });

        // مراقبة تغيير الإعدادات
        document.addEventListener('settingsChanged', (event) => {
            if (event.detail && event.detail.notifications) {
                this.settings = { ...this.settings, ...event.detail.notifications };
                this.saveSettings();
                this.restartReminders();
            }
        });
    }

    /**
     * إعادة تشغيل التذكيرات
     */
    restartReminders() {
        // إيقاف التذكيرات الحالية
        Object.values(this.intervals).forEach(interval => {
            clearInterval(interval);
        });
        this.intervals = {};

        // بدء التذكيرات الجديدة
        this.startSmartReminders();
    }

    /**
     * حفظ الإشعارات
     */
    saveNotifications() {
        try {
            localStorage.setItem('health_app_notifications', JSON.stringify(this.notifications));
        } catch (error) {
            console.error('خطأ في حفظ الإشعارات:', error);
        }
    }

    /**
     * تحميل الإشعارات
     */
    loadNotifications() {
        try {
            const saved = localStorage.getItem('health_app_notifications');
            if (saved) {
                this.notifications = JSON.parse(saved);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }

    /**
     * الحصول على الإشعارات غير المقروءة
     */
    getUnreadNotifications() {
        return this.notifications.filter(notification => !notification.read);
    }

    /**
     * تحديد إشعار كمقروء
     */
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.saveNotifications();
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.saveNotifications();
    }

    /**
     * مسح الإشعارات القديمة
     */
    clearOldNotifications(daysOld = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysOld);

        this.notifications = this.notifications.filter(notification => {
            return new Date(notification.timestamp) > cutoffDate;
        });

        this.saveNotifications();
    }

    /**
     * تدمير نظام الإشعارات
     */
    destroy() {
        // إيقاف جميع التذكيرات
        Object.values(this.intervals).forEach(interval => {
            clearInterval(interval);
        });

        // مسح البيانات
        this.intervals = {};
        this.notifications = [];
        this.userHabits = {};
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.SmartNotifications = SmartNotifications;
}
