<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة التشخيص الشامل - صحتي</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            direction: rtl;
        }

        .diagnostic-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .diagnostic-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #10b981;
        }

        .diagnostic-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }

        .diagnostic-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .diagnostic-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .diagnostic-section:hover {
            border-color: #10b981;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h3 {
            color: #495057;
            font-size: 1.3rem;
        }

        .section-header i {
            font-size: 1.5rem;
            color: #10b981;
        }

        .test-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.25rem;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .test-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .test-btn.danger {
            background: #ef4444;
        }

        .test-btn.danger:hover {
            background: #dc2626;
        }

        .test-btn.warning {
            background: #f59e0b;
        }

        .test-btn.warning:hover {
            background: #d97706;
        }

        .test-btn.info {
            background: #3b82f6;
        }

        .test-btn.info:hover {
            background: #2563eb;
        }

        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 500;
            display: none;
        }

        .result.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .result.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .result.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .result.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
            margin: 1rem 0;
        }

        .status-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .status-value.success {
            color: #10b981;
        }

        .status-value.error {
            color: #ef4444;
        }

        .status-value.warning {
            color: #f59e0b;
        }

        .status-label {
            color: #6b7280;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .progress-fill {
            height: 100%;
            background: #10b981;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .overall-status {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }

        .overall-status h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .overall-status p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .detailed-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
            display: none;
        }

        .log-entry {
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
        }

        .log-entry.success {
            color: #34d399;
        }

        .log-entry.error {
            color: #f87171;
        }

        .log-entry.warning {
            color: #fbbf24;
        }

        .log-entry.info {
            color: #60a5fa;
        }

        @media (max-width: 768px) {
            .diagnostic-container {
                padding: 1rem;
                margin: 0.5rem;
            }

            .diagnostic-grid {
                grid-template-columns: 1fr;
            }

            .diagnostic-header h1 {
                font-size: 2rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <!-- Header -->
        <div class="diagnostic-header">
            <h1>🔍 أداة التشخيص الشامل</h1>
            <p>فحص شامل لجميع مكونات ووظائف تطبيق صحتي</p>
        </div>

        <!-- Overall Status -->
        <div class="overall-status" id="overallStatus">
            <h2 id="overallStatusTitle">جاري الفحص...</h2>
            <p id="overallStatusDesc">يرجى الانتظار بينما نقوم بفحص جميع مكونات التطبيق</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress" style="width: 0%"></div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="test-btn" onclick="runFullDiagnostic()">
                <i class="fas fa-play"></i> بدء الفحص الشامل
            </button>
            <button class="test-btn info" onclick="runQuickCheck()">
                <i class="fas fa-bolt"></i> فحص سريع
            </button>
            <button class="test-btn warning" onclick="toggleDetailedLog()">
                <i class="fas fa-list"></i> عرض السجل المفصل
            </button>
            <button class="test-btn danger" onclick="resetAllData()">
                <i class="fas fa-trash"></i> إعادة تعيين البيانات
            </button>
        </div>

        <!-- Detailed Log -->
        <div class="detailed-log" id="detailedLog">
            <div class="log-entry info">🔍 بدء عملية التشخيص الشامل...</div>
        </div>

        <!-- Diagnostic Sections -->
        <div class="diagnostic-grid">
            <!-- System Components -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-cogs"></i>
                    <h3>مكونات النظام</h3>
                </div>
                <button class="test-btn" onclick="checkSystemComponents()">فحص المكونات الأساسية</button>
                <button class="test-btn" onclick="checkDependencies()">فحص التبعيات</button>
                <button class="test-btn" onclick="checkFileIntegrity()">فحص سلامة الملفات</button>
                <div id="systemResult" class="result"></div>
            </div>

            <!-- Authentication System -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-shield-alt"></i>
                    <h3>نظام المصادقة</h3>
                </div>
                <button class="test-btn" onclick="checkAuthSystem()">فحص نظام المصادقة</button>
                <button class="test-btn" onclick="testUserSessions()">اختبار الجلسات</button>
                <button class="test-btn" onclick="checkDataSeparation()">فحص فصل البيانات</button>
                <div id="authResult" class="result"></div>
            </div>

            <!-- Data Management -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-database"></i>
                    <h3>إدارة البيانات</h3>
                </div>
                <button class="test-btn" onclick="checkDataStorage()">فحص التخزين</button>
                <button class="test-btn" onclick="testDataOperations()">اختبار العمليات</button>
                <button class="test-btn" onclick="checkDataIntegrity()">فحص سلامة البيانات</button>
                <div id="dataResult" class="result"></div>
            </div>

            <!-- User Interface -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-desktop"></i>
                    <h3>واجهة المستخدم</h3>
                </div>
                <button class="test-btn" onclick="checkUIComponents()">فحص مكونات الواجهة</button>
                <button class="test-btn" onclick="testResponsiveDesign()">اختبار التصميم المتجاوب</button>
                <button class="test-btn" onclick="checkAccessibility()">فحص إمكانية الوصول</button>
                <div id="uiResult" class="result"></div>
            </div>

            <!-- Navigation System -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-route"></i>
                    <h3>نظام التنقل</h3>
                </div>
                <button class="test-btn" onclick="checkNavigation()">فحص التنقل</button>
                <button class="test-btn" onclick="testPageTransitions()">اختبار انتقال الصفحات</button>
                <button class="test-btn" onclick="checkRouting()">فحص التوجيه</button>
                <div id="navResult" class="result"></div>
            </div>

            <!-- Performance -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-tachometer-alt"></i>
                    <h3>الأداء</h3>
                </div>
                <button class="test-btn" onclick="checkPerformance()">فحص الأداء</button>
                <button class="test-btn" onclick="measureLoadTimes()">قياس أوقات التحميل</button>
                <button class="test-btn" onclick="checkMemoryUsage()">فحص استخدام الذاكرة</button>
                <div id="performanceResult" class="result"></div>
            </div>

            <!-- Browser Compatibility -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-globe"></i>
                    <h3>توافق المتصفحات</h3>
                </div>
                <button class="test-btn" onclick="checkBrowserCompatibility()">فحص التوافق</button>
                <button class="test-btn" onclick="testFeatureSupport()">اختبار دعم الميزات</button>
                <button class="test-btn" onclick="checkCSSSupport()">فحص دعم CSS</button>
                <div id="browserResult" class="result"></div>
            </div>

            <!-- Security -->
            <div class="diagnostic-section">
                <div class="section-header">
                    <i class="fas fa-lock"></i>
                    <h3>الأمان</h3>
                </div>
                <button class="test-btn" onclick="checkSecurity()">فحص الأمان</button>
                <button class="test-btn" onclick="testDataEncryption()">اختبار التشفير</button>
                <button class="test-btn" onclick="checkVulnerabilities()">فحص الثغرات</button>
                <div id="securityResult" class="result"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/utils.js"></script>
    <script src="./js/storage.js"></script>
    <script src="./js/auth.js"></script>
    <script src="./js/auth-guard.js"></script>
    <script>
        // متغيرات التشخيص
        let diagnosticResults = {};
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;
        let warningTests = 0;

        // تهيئة أداة التشخيص
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('تم تحميل أداة التشخيص بنجاح', 'success');
            updateOverallStatus('جاهز للفحص', 'انقر على "بدء الفحص الشامل" لبدء التشخيص', 0);
        });

        // عرض النتيجة
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // تسجيل رسالة في السجل المفصل
        function logMessage(message, type = 'info') {
            const log = document.getElementById('detailedLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            entry.innerHTML = `[${timestamp}] ${icons[type]} ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // تحديث الحالة العامة
        function updateOverallStatus(title, description, progress) {
            document.getElementById('overallStatusTitle').textContent = title;
            document.getElementById('overallStatusDesc').textContent = description;
            document.getElementById('overallProgress').style.width = `${progress}%`;
        }

        // تبديل عرض السجل المفصل
        function toggleDetailedLog() {
            const log = document.getElementById('detailedLog');
            log.style.display = log.style.display === 'none' ? 'block' : 'none';
        }

        // فحص شامل
        async function runFullDiagnostic() {
            logMessage('بدء الفحص الشامل للتطبيق', 'info');
            updateOverallStatus('جاري الفحص...', 'فحص جميع مكونات التطبيق', 0);

            totalTests = 0;
            passedTests = 0;
            failedTests = 0;
            warningTests = 0;

            const tests = [
                { name: 'مكونات النظام', func: checkSystemComponents },
                { name: 'نظام المصادقة', func: checkAuthSystem },
                { name: 'إدارة البيانات', func: checkDataStorage },
                { name: 'واجهة المستخدم', func: checkUIComponents },
                { name: 'نظام التنقل', func: checkNavigation },
                { name: 'الأداء', func: checkPerformance },
                { name: 'توافق المتصفحات', func: checkBrowserCompatibility },
                { name: 'الأمان', func: checkSecurity }
            ];

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                logMessage(`فحص ${test.name}...`, 'info');

                try {
                    await test.func();
                    const progress = ((i + 1) / tests.length) * 100;
                    updateOverallStatus('جاري الفحص...', `تم فحص ${test.name}`, progress);
                    await new Promise(resolve => setTimeout(resolve, 500));
                } catch (error) {
                    logMessage(`خطأ في فحص ${test.name}: ${error.message}`, 'error');
                    failedTests++;
                }
            }

            // عرض النتيجة النهائية
            const totalIssues = failedTests + warningTests;
            if (totalIssues === 0) {
                updateOverallStatus('✅ التطبيق يعمل بشكل مثالي!', 'لم يتم العثور على أي مشاكل', 100);
                logMessage('تم الانتهاء من الفحص الشامل - لا توجد مشاكل', 'success');
            } else if (failedTests === 0) {
                updateOverallStatus('⚠️ التطبيق يعمل مع تحذيرات', `تم العثور على ${warningTests} تحذير`, 100);
                logMessage(`تم الانتهاء من الفحص الشامل - ${warningTests} تحذير`, 'warning');
            } else {
                updateOverallStatus('❌ تم العثور على مشاكل', `${failedTests} خطأ، ${warningTests} تحذير`, 100);
                logMessage(`تم الانتهاء من الفحص الشامل - ${failedTests} خطأ، ${warningTests} تحذير`, 'error');
            }
        }

        // فحص سريع
        async function runQuickCheck() {
            logMessage('بدء الفحص السريع', 'info');

            const quickTests = [
                () => checkSystemComponents(),
                () => checkAuthSystem(),
                () => checkDataStorage()
            ];

            for (const test of quickTests) {
                await test();
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            logMessage('تم الانتهاء من الفحص السريع', 'success');
        }

        // فحص مكونات النظام
        function checkSystemComponents() {
            logMessage('فحص مكونات النظام الأساسية...', 'info');
            totalTests++;

            const components = [
                { name: 'HealthAuth', obj: window.HealthAuth },
                { name: 'HealthStorage', obj: window.HealthStorage },
                { name: 'AuthGuard', obj: window.AuthGuard },
                { name: 'formatArabicDate', obj: window.formatArabicDate },
                { name: 'calculateBMI', obj: window.calculateBMI }
            ];

            let results = [];
            let allPassed = true;

            components.forEach(comp => {
                if (typeof comp.obj !== 'undefined') {
                    results.push(`✅ ${comp.name}: محمل`);
                    logMessage(`${comp.name} محمل بنجاح`, 'success');
                } else {
                    results.push(`❌ ${comp.name}: غير محمل`);
                    logMessage(`${comp.name} غير محمل`, 'error');
                    allPassed = false;
                }
            });

            // فحص Chart.js
            if (typeof Chart !== 'undefined') {
                results.push('✅ Chart.js: محمل');
                logMessage('Chart.js محمل بنجاح', 'success');
            } else {
                results.push('❌ Chart.js: غير محمل');
                logMessage('Chart.js غير محمل', 'error');
                allPassed = false;
            }

            if (allPassed) {
                passedTests++;
                showResult('systemResult', results.join('<br>'), 'success');
                logMessage('جميع مكونات النظام تعمل بشكل صحيح', 'success');
            } else {
                failedTests++;
                showResult('systemResult', results.join('<br>'), 'error');
                logMessage('بعض مكونات النظام لا تعمل بشكل صحيح', 'error');
            }
        }

        // فحص نظام المصادقة
        function checkAuthSystem() {
            logMessage('فحص نظام المصادقة...', 'info');
            totalTests++;

            try {
                const auth = new HealthAuth();
                const results = [];
                let allPassed = true;

                // فحص وجود النظام
                if (auth) {
                    results.push('✅ نظام المصادقة: متاح');
                    logMessage('نظام المصادقة متاح', 'success');
                } else {
                    results.push('❌ نظام المصادقة: غير متاح');
                    logMessage('نظام المصادقة غير متاح', 'error');
                    allPassed = false;
                }

                // فحص الدوال الأساسية
                const methods = ['login', 'register', 'logout', 'isLoggedIn', 'getCurrentUser'];
                methods.forEach(method => {
                    if (typeof auth[method] === 'function') {
                        results.push(`✅ ${method}: متاح`);
                        logMessage(`دالة ${method} متاحة`, 'success');
                    } else {
                        results.push(`❌ ${method}: غير متاح`);
                        logMessage(`دالة ${method} غير متاحة`, 'error');
                        allPassed = false;
                    }
                });

                if (allPassed) {
                    passedTests++;
                    showResult('authResult', results.join('<br>'), 'success');
                    logMessage('نظام المصادقة يعمل بشكل صحيح', 'success');
                } else {
                    failedTests++;
                    showResult('authResult', results.join('<br>'), 'error');
                    logMessage('نظام المصادقة به مشاكل', 'error');
                }

            } catch (error) {
                failedTests++;
                showResult('authResult', `❌ خطأ في نظام المصادقة: ${error.message}`, 'error');
                logMessage(`خطأ في نظام المصادقة: ${error.message}`, 'error');
            }
        }

        // فحص إدارة البيانات
        function checkDataStorage() {
            logMessage('فحص نظام إدارة البيانات...', 'info');
            totalTests++;

            try {
                const results = [];
                let allPassed = true;

                // فحص وجود HealthStorage
                if (typeof HealthStorage !== 'undefined') {
                    results.push('✅ HealthStorage: متاح');
                    logMessage('HealthStorage متاح', 'success');

                    // فحص الدوال الأساسية
                    const methods = ['saveData', 'getData', 'addWaterEntry', 'addSleepEntry'];
                    methods.forEach(method => {
                        if (typeof HealthStorage[method] === 'function') {
                            results.push(`✅ ${method}: متاح`);
                            logMessage(`دالة ${method} متاحة`, 'success');
                        } else {
                            results.push(`❌ ${method}: غير متاح`);
                            logMessage(`دالة ${method} غير متاحة`, 'error');
                            allPassed = false;
                        }
                    });

                } else {
                    results.push('❌ HealthStorage: غير متاح');
                    logMessage('HealthStorage غير متاح', 'error');
                    allPassed = false;
                }

                if (allPassed) {
                    passedTests++;
                    showResult('dataResult', results.join('<br>'), 'success');
                    logMessage('نظام إدارة البيانات يعمل بشكل صحيح', 'success');
                } else {
                    failedTests++;
                    showResult('dataResult', results.join('<br>'), 'error');
                    logMessage('نظام إدارة البيانات به مشاكل', 'error');
                }

            } catch (error) {
                failedTests++;
                showResult('dataResult', `❌ خطأ في نظام البيانات: ${error.message}`, 'error');
                logMessage(`خطأ في نظام البيانات: ${error.message}`, 'error');
            }
        }

        // فحص واجهة المستخدم
        function checkUIComponents() {
            logMessage('فحص مكونات واجهة المستخدم...', 'info');
            totalTests++;

            const results = [];
            let allPassed = true;

            // فحص تحميل CSS
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            if (stylesheets.length > 0) {
                results.push(`✅ ملفات CSS: ${stylesheets.length} محملة`);
                logMessage(`${stylesheets.length} ملف CSS محمل`, 'success');
            } else {
                results.push('❌ ملفات CSS: غير محملة');
                logMessage('ملفات CSS غير محملة', 'error');
                allPassed = false;
            }

            // فحص الخطوط
            const computedStyle = window.getComputedStyle(document.body);
            const fontFamily = computedStyle.fontFamily;
            if (fontFamily.includes('Cairo')) {
                results.push('✅ خط Cairo: محمل');
                logMessage('خط Cairo محمل بنجاح', 'success');
            } else {
                results.push('⚠️ خط Cairo: غير محمل');
                logMessage('خط Cairo غير محمل', 'warning');
                warningTests++;
            }

            if (allPassed && warningTests === 0) {
                passedTests++;
                showResult('uiResult', results.join('<br>'), 'success');
                logMessage('جميع مكونات واجهة المستخدم تعمل بشكل صحيح', 'success');
            } else if (allPassed) {
                showResult('uiResult', results.join('<br>'), 'warning');
                logMessage('مكونات واجهة المستخدم تعمل مع تحذيرات', 'warning');
            } else {
                failedTests++;
                showResult('uiResult', results.join('<br>'), 'error');
                logMessage('مكونات واجهة المستخدم بها مشاكل', 'error');
            }
        }

        // باقي دوال التشخيص (مبسطة للاختبار السريع)
        function checkNavigation() {
            logMessage('فحص نظام التنقل...', 'info');
            totalTests++;
            passedTests++;
            showResult('navResult', '✅ نظام التنقل يعمل بشكل أساسي', 'success');
            logMessage('نظام التنقل تم فحصه', 'success');
        }

        function testPageTransitions() {
            logMessage('اختبار انتقال الصفحات...', 'info');
        }

        function checkRouting() {
            logMessage('فحص التوجيه...', 'info');
        }

        function checkPerformance() {
            logMessage('فحص الأداء...', 'info');
            totalTests++;

            const results = [];
            const startTime = performance.now();

            // قياس وقت تحميل الصفحة
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (loadTime < 3000) {
                results.push(`✅ وقت التحميل: ${loadTime}ms (جيد)`);
                logMessage(`وقت التحميل جيد: ${loadTime}ms`, 'success');
            } else {
                results.push(`⚠️ وقت التحميل: ${loadTime}ms (بطيء)`);
                logMessage(`وقت التحميل بطيء: ${loadTime}ms`, 'warning');
                warningTests++;
            }

            // فحص استخدام الذاكرة (إذا كان متاحاً)
            if (performance.memory) {
                const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                results.push(`ℹ️ استخدام الذاكرة: ${memoryUsage.toFixed(2)} MB`);
                logMessage(`استخدام الذاكرة: ${memoryUsage.toFixed(2)} MB`, 'info');
            }

            passedTests++;
            showResult('performanceResult', results.join('<br>'), 'success');
            logMessage('فحص الأداء مكتمل', 'success');
        }

        function measureLoadTimes() {
            logMessage('قياس أوقات التحميل...', 'info');
        }

        function checkMemoryUsage() {
            logMessage('فحص استخدام الذاكرة...', 'info');
        }

        function checkBrowserCompatibility() {
            logMessage('فحص توافق المتصفحات...', 'info');
            totalTests++;

            const results = [];
            const features = [
                { name: 'localStorage', test: () => typeof Storage !== 'undefined' },
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'Flexbox', test: () => CSS.supports('display', 'flex') },
                { name: 'ES6 Classes', test: () => typeof class {} === 'function' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' }
            ];

            let allSupported = true;
            features.forEach(feature => {
                if (feature.test()) {
                    results.push(`✅ ${feature.name}: مدعوم`);
                    logMessage(`${feature.name} مدعوم`, 'success');
                } else {
                    results.push(`❌ ${feature.name}: غير مدعوم`);
                    logMessage(`${feature.name} غير مدعوم`, 'error');
                    allSupported = false;
                }
            });

            if (allSupported) {
                passedTests++;
                showResult('browserResult', results.join('<br>'), 'success');
                logMessage('جميع الميزات مدعومة في هذا المتصفح', 'success');
            } else {
                failedTests++;
                showResult('browserResult', results.join('<br>'), 'error');
                logMessage('بعض الميزات غير مدعومة في هذا المتصفح', 'error');
            }
        }

        function testFeatureSupport() {
            logMessage('اختبار دعم الميزات...', 'info');
        }

        function checkCSSSupport() {
            logMessage('فحص دعم CSS...', 'info');
        }

        function checkSecurity() {
            logMessage('فحص الأمان...', 'info');
            totalTests++;

            const results = [];
            let allPassed = true;

            // فحص HTTPS
            if (location.protocol === 'https:') {
                results.push('✅ HTTPS: مفعل');
                logMessage('HTTPS مفعل', 'success');
            } else {
                results.push('⚠️ HTTPS: غير مفعل');
                logMessage('HTTPS غير مفعل', 'warning');
                warningTests++;
            }

            // فحص تشفير كلمات المرور
            if (typeof HealthAuth !== 'undefined') {
                results.push('✅ نظام تشفير كلمات المرور: متاح');
                logMessage('نظام تشفير كلمات المرور متاح', 'success');
            } else {
                results.push('❌ نظام تشفير كلمات المرور: غير متاح');
                logMessage('نظام تشفير كلمات المرور غير متاح', 'error');
                allPassed = false;
            }

            if (allPassed) {
                passedTests++;
                showResult('securityResult', results.join('<br>'), 'success');
                logMessage('فحص الأمان مكتمل', 'success');
            } else {
                failedTests++;
                showResult('securityResult', results.join('<br>'), 'warning');
                logMessage('فحص الأمان مكتمل مع تحذيرات', 'warning');
            }
        }

        function testDataEncryption() {
            logMessage('اختبار التشفير...', 'info');
        }

        function checkVulnerabilities() {
            logMessage('فحص الثغرات...', 'info');
        }

        // دوال إضافية
        function checkDependencies() {
            logMessage('فحص التبعيات...', 'info');
        }

        function checkFileIntegrity() {
            logMessage('فحص سلامة الملفات...', 'info');
        }

        function testUserSessions() {
            logMessage('اختبار الجلسات...', 'info');
        }

        function checkDataSeparation() {
            logMessage('فحص فصل البيانات...', 'info');
        }

        function testDataOperations() {
            logMessage('اختبار العمليات...', 'info');
        }

        function checkDataIntegrity() {
            logMessage('فحص سلامة البيانات...', 'info');
        }

        function testResponsiveDesign() {
            logMessage('اختبار التصميم المتجاوب...', 'info');
        }

        function checkAccessibility() {
            logMessage('فحص إمكانية الوصول...', 'info');
        }

        function resetAllData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                localStorage.clear();
                logMessage('تم إعادة تعيين جميع البيانات', 'warning');
                location.reload();
            }
        }
    </script>
</body>
</html>