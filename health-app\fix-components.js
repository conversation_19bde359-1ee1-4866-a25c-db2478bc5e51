/**
 * Health App - Component Fixes
 * إصلاحات شاملة لجميع مكونات التطبيق
 */

// تشغيل الإصلاحات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 بدء إصلاحات المكونات...');
    
    // إصلاح 1: التأكد من تحميل جميع المكونات
    fixComponentLoading();
    
    // إصلاح 2: ربط الأحداث المفقودة
    fixEventListeners();
    
    // إصلاح 3: تحديث البيانات
    fixDataBinding();
    
    // إصلاح 4: إصلاح التنقل
    fixNavigation();
    
    // إصلاح 5: إصلاح الإجراءات السريعة
    fixQuickActions();

    // إصلاح 6: إعداد قائمة المستخدم
    setupUserMenu();

    console.log('✅ تم الانتهاء من جميع الإصلاحات');
});

/**
 * إصلاح تحميل المكونات
 */
function fixComponentLoading() {
    console.log('🔄 إصلاح تحميل المكونات...');
    
    // التحقق من المكونات الأساسية
    const requiredComponents = [
        'HealthStorage',
        'HealthCharts', 
        'WaterTracker',
        'SleepTracker',
        'ExerciseTracker',
        'NutritionTracker'
    ];
    
    requiredComponents.forEach(component => {
        if (typeof window[component] === 'undefined') {
            console.warn(`⚠️ المكون ${component} غير محمل`);
        } else {
            console.log(`✅ المكون ${component} محمل بنجاح`);
        }
    });
}

/**
 * إصلاح مستمعي الأحداث
 */
function fixEventListeners() {
    console.log('🔄 إصلاح مستمعي الأحداث...');
    
    // إصلاح أزرار الإجراءات السريعة
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(btn => {
        if (!btn.hasAttribute('data-fixed')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.textContent.trim();
                
                if (action.includes('ماء')) {
                    addWaterQuick();
                } else if (action.includes('نوم')) {
                    goToSleepPage();
                } else if (action.includes('تمرين')) {
                    goToExercisePage();
                } else if (action.includes('وجبة')) {
                    goToNutritionPage();
                }
            });
            btn.setAttribute('data-fixed', 'true');
        }
    });
    
    // إصلاح روابط التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        if (!link.hasAttribute('data-fixed')) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = this.getAttribute('data-page');
                if (page) {
                    navigateToPage(page);
                }
            });
            link.setAttribute('data-fixed', 'true');
        }
    });
}

/**
 * إصلاح ربط البيانات
 */
function fixDataBinding() {
    console.log('🔄 إصلاح ربط البيانات...');
    
    // تحديث الإحصائيات في لوحة التحكم
    updateDashboardStats();
    
    // تحديث التاريخ الحالي
    updateCurrentDate();
}

/**
 * إصلاح التنقل
 */
function fixNavigation() {
    console.log('🔄 إصلاح التنقل...');
    
    // التأكد من وجود جميع الصفحات
    const pages = ['dashboard', 'sleep', 'water', 'exercise', 'nutrition'];
    pages.forEach(page => {
        const pageElement = document.getElementById(`${page}Page`);
        if (!pageElement) {
            console.warn(`⚠️ الصفحة ${page}Page غير موجودة`);
        }
    });
}

/**
 * إصلاح الإجراءات السريعة
 */
function fixQuickActions() {
    console.log('🔄 إصلاح الإجراءات السريعة...');
    
    // إضافة الدوال المفقودة للنافذة العامة
    window.addWaterQuick = addWaterQuick;
    window.goToSleepPage = goToSleepPage;
    window.goToExercisePage = goToExercisePage;
    window.goToNutritionPage = goToNutritionPage;
    window.navigateToPage = navigateToPage;
    window.updateDashboardStats = updateDashboardStats;
    window.updateCurrentDate = updateCurrentDate;
    window.setupUserMenu = setupUserMenu;
}

/**
 * إضافة ماء سريع
 */
function addWaterQuick() {
    try {
        const waterEntry = {
            id: Date.now(),
            amount: 250,
            timestamp: new Date().toISOString(),
            type: 'glass'
        };
        
        HealthStorage.addWaterEntry(waterEntry);
        
        // عرض إشعار
        showToast('تم إضافة كوب ماء (250ml)', 'success');
        
        // تحديث الإحصائيات
        updateDashboardStats();
        
    } catch (error) {
        console.error('خطأ في إضافة الماء:', error);
        showToast('خطأ في إضافة الماء', 'error');
    }
}

/**
 * الانتقال لصفحة النوم
 */
function goToSleepPage() {
    navigateToPage('sleep');
}

/**
 * الانتقال لصفحة التمارين
 */
function goToExercisePage() {
    navigateToPage('exercise');
}

/**
 * الانتقال لصفحة التغذية
 */
function goToNutritionPage() {
    navigateToPage('nutrition');
}

/**
 * التنقل إلى صفحة
 */
function navigateToPage(page) {
    try {
        // إخفاء جميع الصفحات
        const pages = document.querySelectorAll('.page');
        pages.forEach(p => p.classList.remove('active'));
        
        // إزالة الفئة النشطة من الروابط
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => link.classList.remove('active'));
        
        // إظهار الصفحة المطلوبة
        const targetPage = document.getElementById(`${page}Page`);
        const targetLink = document.querySelector(`[data-page="${page}"]`);
        
        if (targetPage) {
            targetPage.classList.add('active');
            
            if (targetLink) {
                targetLink.classList.add('active');
            }
            
            // تحديث عنوان الصفحة
            updatePageTitle(page);
            
            console.log(`✅ تم الانتقال إلى صفحة ${page}`);
        } else {
            console.warn(`⚠️ الصفحة ${page} غير موجودة`);
        }
        
    } catch (error) {
        console.error('خطأ في التنقل:', error);
    }
}

/**
 * تحديث عنوان الصفحة
 */
function updatePageTitle(page) {
    const titles = {
        dashboard: 'لوحة التحكم',
        sleep: 'تتبع النوم',
        water: 'تتبع الماء',
        exercise: 'التمارين الرياضية',
        nutrition: 'التغذية'
    };
    
    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle && titles[page]) {
        pageTitle.textContent = titles[page];
    }
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStats() {
    try {
        // تحديث إحصائيات الماء
        const todayWater = HealthStorage.getTodayWaterIntake();
        const waterElement = document.querySelector('.water-stat .stat-value span');
        if (waterElement) {
            waterElement.textContent = (todayWater / 1000).toFixed(1);
        }
        
        // تحديث إحصائيات النوم
        const lastSleep = HealthStorage.getLastSleepDuration();
        const sleepElement = document.querySelector('.sleep-stat .stat-value span');
        if (sleepElement) {
            sleepElement.textContent = lastSleep.toFixed(1);
        }
        
        // تحديث إحصائيات التمارين
        const todayExercise = HealthStorage.getTodayExerciseMinutes();
        const exerciseElement = document.querySelector('.exercise-stat .stat-value span');
        if (exerciseElement) {
            exerciseElement.textContent = todayExercise;
        }
        
        // تحديث إحصائيات السعرات
        const todayCalories = HealthStorage.getTodayCalories();
        const caloriesElement = document.querySelector('.nutrition-stat .stat-value span');
        if (caloriesElement) {
            caloriesElement.textContent = todayCalories;
        }
        
        console.log('✅ تم تحديث إحصائيات لوحة التحكم');
        
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

/**
 * تحديث التاريخ الحالي
 */
function updateCurrentDate() {
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

/**
 * عرض إشعار Toast
 */
function showToast(message, type = 'info') {
    // إنشاء عنصر Toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-message">${message}</span>
        </div>
    `;

    // إضافة الأنماط
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        border-radius: 8px;
        padding: 16px 20px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-weight: 500;
    `;

    // إضافة إلى الصفحة
    document.body.appendChild(toast);

    // إظهار Toast
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء Toast
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * إعداد قائمة المستخدم
 */
function setupUserMenu() {
    console.log('🔄 إعداد قائمة المستخدم...');

    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.querySelector('.user-menu');
    const userDropdown = document.getElementById('userDropdown');

    if (userMenuBtn && userMenu) {
        // تبديل القائمة عند النقر
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenu.classList.toggle('active');
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!userMenu.contains(e.target)) {
                userMenu.classList.remove('active');
            }
        });

        // معالجة روابط القائمة
        if (userDropdown) {
            const dropdownItems = userDropdown.querySelectorAll('.dropdown-item[data-page]');
            dropdownItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = item.getAttribute('data-page');
                    if (page) {
                        navigateToPage(page);
                        userMenu.classList.remove('active');
                    }
                });
            });
        }
    }
}

console.log('🔧 تم تحميل ملف الإصلاحات');
