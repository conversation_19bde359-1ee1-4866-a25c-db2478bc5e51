/**
 * PDFMerge Pro - Utility Functions
 * مجموعة الدوال المساعدة للمشروع
 */

// ===== FILE UTILITIES =====

/**
 * تحويل حجم الملف إلى تنسيق قابل للقراءة
 * @param {number} bytes - حجم الملف بالبايت
 * @returns {string} - الحجم بالتنسيق المناسب
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * التحقق من نوع الملف
 * @param {File} file - الملف المراد فحصه
 * @returns {boolean} - true إذا كان PDF
 */
function isPDFFile(file) {
    return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
}

/**
 * التحقق من حجم الملف
 * @param {File} file - الملف المراد فحصه
 * @param {number} maxSize - الحد الأقصى للحجم بالبايت
 * @returns {boolean} - true إذا كان الحجم مقبول
 */
function isValidFileSize(file, maxSize = 100 * 1024 * 1024) { // 100MB default
    return file.size <= maxSize;
}

/**
 * إنشاء معرف فريد للملف
 * @returns {string} - معرف فريد
 */
function generateFileId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * قراءة الملف كـ ArrayBuffer
 * @param {File} file - الملف المراد قراءته
 * @returns {Promise<ArrayBuffer>} - محتوى الملف
 */
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(reader.error);
        reader.readAsArrayBuffer(file);
    });
}

// ===== DOM UTILITIES =====

/**
 * إنشاء عنصر HTML مع خصائص
 * @param {string} tag - نوع العنصر
 * @param {Object} attributes - الخصائص
 * @param {string} content - المحتوى النصي
 * @returns {HTMLElement} - العنصر المُنشأ
 */
function createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    Object.keys(attributes).forEach(key => {
        if (key === 'className') {
            element.className = attributes[key];
        } else if (key === 'dataset') {
            Object.keys(attributes[key]).forEach(dataKey => {
                element.dataset[dataKey] = attributes[key][dataKey];
            });
        } else {
            element.setAttribute(key, attributes[key]);
        }
    });
    
    if (content) {
        element.textContent = content;
    }
    
    return element;
}

/**
 * إضافة مستمع أحداث مع إزالة تلقائية
 * @param {HTMLElement} element - العنصر
 * @param {string} event - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 * @param {Object} options - خيارات إضافية
 */
function addEventListenerOnce(element, event, handler, options = {}) {
    const wrappedHandler = (e) => {
        handler(e);
        element.removeEventListener(event, wrappedHandler, options);
    };
    element.addEventListener(event, wrappedHandler, options);
}

/**
 * إضافة فئة CSS مع حركة
 * @param {HTMLElement} element - العنصر
 * @param {string} className - اسم الفئة
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function addClassWithAnimation(element, className, duration = 300) {
    element.classList.add(className);
    setTimeout(() => {
        element.classList.remove(className);
    }, duration);
}

// ===== VALIDATION UTILITIES =====

/**
 * التحقق من صحة البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {boolean} - true إذا كان صحيح
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تنظيف النص من الأحرف الخطيرة
 * @param {string} text - النص المراد تنظيفه
 * @returns {string} - النص المُنظف
 */
function sanitizeText(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// ===== ANIMATION UTILITIES =====

/**
 * تحريك رقم من قيمة إلى أخرى
 * @param {HTMLElement} element - العنصر المحتوي على الرقم
 * @param {number} start - القيمة الابتدائية
 * @param {number} end - القيمة النهائية
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function animateNumber(element, start, end, duration = 1000) {
    const startTime = performance.now();
    const difference = end - start;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = start + (difference * easeOut);
        
        element.textContent = Math.round(current);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * تحريك شريط التقدم
 * @param {HTMLElement} progressBar - عنصر شريط التقدم
 * @param {number} percentage - النسبة المئوية (0-100)
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function animateProgressBar(progressBar, percentage, duration = 500) {
    progressBar.style.transition = `width ${duration}ms ease-out`;
    progressBar.style.width = `${percentage}%`;
}

// ===== STORAGE UTILITIES =====

/**
 * حفظ البيانات في التخزين المحلي
 * @param {string} key - مفتاح التخزين
 * @param {any} data - البيانات المراد حفظها
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.warn('فشل في حفظ البيانات:', error);
    }
}

/**
 * استرجاع البيانات من التخزين المحلي
 * @param {string} key - مفتاح التخزين
 * @param {any} defaultValue - القيمة الافتراضية
 * @returns {any} - البيانات المسترجعة
 */
function getFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.warn('فشل في استرجاع البيانات:', error);
        return defaultValue;
    }
}

/**
 * حذف البيانات من التخزين المحلي
 * @param {string} key - مفتاح التخزين
 */
function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
    } catch (error) {
        console.warn('فشل في حذف البيانات:', error);
    }
}

// ===== TIME UTILITIES =====

/**
 * تحويل الوقت إلى تنسيق قابل للقراءة
 * @param {number} seconds - الوقت بالثواني
 * @returns {string} - الوقت بالتنسيق المناسب
 */
function formatTime(seconds) {
    if (seconds < 60) {
        return `${Math.round(seconds)} ثانية`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        return `${minutes} دقيقة${remainingSeconds > 0 ? ` و ${remainingSeconds} ثانية` : ''}`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours} ساعة${minutes > 0 ? ` و ${minutes} دقيقة` : ''}`;
    }
}

/**
 * حساب الوقت المتبقي المقدر
 * @param {number} processed - العدد المعالج
 * @param {number} total - العدد الإجمالي
 * @param {number} elapsedTime - الوقت المنقضي بالميلي ثانية
 * @returns {number} - الوقت المتبقي بالثواني
 */
function calculateRemainingTime(processed, total, elapsedTime) {
    if (processed === 0) return 0;
    
    const averageTimePerItem = elapsedTime / processed;
    const remainingItems = total - processed;
    return (remainingItems * averageTimePerItem) / 1000; // Convert to seconds
}

// ===== ERROR HANDLING =====

/**
 * عرض رسالة خطأ للمستخدم
 * @param {string} message - رسالة الخطأ
 * @param {string} type - نوع الرسالة (error, warning, info, success)
 */
function showNotification(message, type = 'info') {
    // سيتم تنفيذ هذه الدالة في ملف منفصل للإشعارات
    console.log(`[${type.toUpperCase()}] ${message}`);
}

/**
 * معالجة الأخطاء العامة
 * @param {Error} error - كائن الخطأ
 * @param {string} context - سياق حدوث الخطأ
 */
function handleError(error, context = 'عملية غير محددة') {
    console.error(`خطأ في ${context}:`, error);
    
    let userMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    
    if (error.name === 'QuotaExceededError') {
        userMessage = 'مساحة التخزين ممتلئة. يرجى تحرير بعض المساحة.';
    } else if (error.name === 'NetworkError') {
        userMessage = 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال.';
    } else if (error.message.includes('PDF')) {
        userMessage = 'خطأ في معالجة ملف PDF. تأكد من أن الملف غير تالف.';
    }
    
    showNotification(userMessage, 'error');
}

// ===== PERFORMANCE UTILITIES =====

/**
 * تأخير تنفيذ دالة (Debounce)
 * @param {Function} func - الدالة المراد تأخيرها
 * @param {number} wait - وقت التأخير بالميلي ثانية
 * @returns {Function} - الدالة المُحسنة
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * تحديد معدل تنفيذ دالة (Throttle)
 * @param {Function} func - الدالة المراد تحديد معدلها
 * @param {number} limit - الحد الأدنى للوقت بين التنفيذات
 * @returns {Function} - الدالة المُحسنة
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== EXPORT FOR MODULE SYSTEMS =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatFileSize,
        isPDFFile,
        isValidFileSize,
        generateFileId,
        readFileAsArrayBuffer,
        createElement,
        addEventListenerOnce,
        addClassWithAnimation,
        isValidEmail,
        sanitizeText,
        animateNumber,
        animateProgressBar,
        saveToLocalStorage,
        getFromLocalStorage,
        removeFromLocalStorage,
        formatTime,
        calculateRemainingTime,
        showNotification,
        handleError,
        debounce,
        throttle
    };
}
