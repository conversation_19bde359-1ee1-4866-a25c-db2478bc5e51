/**
 * Health App - Sleep Tracking
 * تتبع النوم
 */

class SleepTracker {
    constructor(app) {
        this.app = app;
        this.isTracking = false;
        this.sleepStartTime = null;
        this.sleepTimer = null;
        
        this.init();
    }

    /**
     * تهيئة متتبع النوم
     */
    init() {
        this.loadSleepPage();
        this.setupEventHandlers();
    }

    /**
     * تحميل صفحة النوم
     */
    loadSleepPage() {
        const sleepPage = document.getElementById('sleepPage');
        if (!sleepPage) return;

        sleepPage.innerHTML = `
            <div class="sleep-container">
                <!-- Sleep Stats -->
                <div class="sleep-stats-grid">
                    <div class="sleep-stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <div class="stat-info">
                            <h3>النوم الليلة الماضية</h3>
                            <div class="stat-value" id="lastNightSleep">-- ساعة</div>
                            <div class="stat-quality" id="sleepQuality">جودة: --</div>
                        </div>
                    </div>
                    
                    <div class="sleep-stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>متوسط هذا الأسبوع</h3>
                            <div class="stat-value" id="weeklyAverage">-- ساعة</div>
                            <div class="stat-trend" id="sleepTrend">--</div>
                        </div>
                    </div>
                    
                    <div class="sleep-stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="stat-info">
                            <h3>الهدف اليومي</h3>
                            <div class="stat-value" id="sleepGoal">${this.app.user.goals.sleep} ساعة</div>
                            <div class="stat-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="sleepGoalProgress"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sleep-stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-info">
                            <h3>أيام متتالية</h3>
                            <div class="stat-value" id="sleepStreak">-- يوم</div>
                            <div class="stat-description">تحقيق الهدف</div>
                        </div>
                    </div>
                </div>

                <!-- Sleep Tracker -->
                <div class="sleep-tracker-section">
                    <div class="tracker-card">
                        <div class="tracker-header">
                            <h3>متتبع النوم</h3>
                            <div class="sleep-status" id="sleepStatus">
                                <span class="status-text">مستيقظ</span>
                                <div class="status-indicator"></div>
                            </div>
                        </div>
                        
                        <div class="sleep-timer" id="sleepTimer">
                            <div class="timer-display">
                                <span id="sleepDuration">00:00:00</span>
                            </div>
                            <div class="timer-controls">
                                <button class="btn btn-primary btn-large" id="sleepToggleBtn">
                                    <i class="fas fa-bed"></i>
                                    بدء النوم
                                </button>
                            </div>
                        </div>
                        
                        <div class="sleep-quality-selector hidden" id="qualitySelector">
                            <h4>كيف كانت جودة نومك؟</h4>
                            <div class="quality-options">
                                <button class="quality-btn" data-quality="1">
                                    <i class="fas fa-frown"></i>
                                    سيئة
                                </button>
                                <button class="quality-btn" data-quality="2">
                                    <i class="fas fa-meh"></i>
                                    متوسطة
                                </button>
                                <button class="quality-btn" data-quality="3">
                                    <i class="fas fa-smile"></i>
                                    جيدة
                                </button>
                                <button class="quality-btn" data-quality="4">
                                    <i class="fas fa-grin"></i>
                                    ممتازة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Sleep Entry -->
                <div class="manual-entry-section">
                    <div class="entry-card">
                        <div class="card-header">
                            <h3>إدخال يدوي للنوم</h3>
                            <button class="btn btn-outline" id="manualEntryToggle">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </button>
                        </div>
                        
                        <div class="manual-entry-form hidden" id="manualEntryForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">تاريخ النوم</label>
                                    <input type="date" class="form-input" id="sleepDate" value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">وقت النوم</label>
                                    <input type="time" class="form-input" id="bedTime">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">وقت الاستيقاظ</label>
                                    <input type="time" class="form-input" id="wakeTime">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">جودة النوم</label>
                                    <select class="form-select" id="manualQuality">
                                        <option value="1">سيئة</option>
                                        <option value="2">متوسطة</option>
                                        <option value="3" selected>جيدة</option>
                                        <option value="4">ممتازة</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">ملاحظات (اختياري)</label>
                                <textarea class="form-textarea" id="sleepNotes" placeholder="أي ملاحظات حول نومك..."></textarea>
                            </div>
                            
                            <div class="form-actions">
                                <button class="btn btn-primary" id="saveSleepEntry">
                                    <i class="fas fa-save"></i>
                                    حفظ
                                </button>
                                <button class="btn btn-ghost" id="cancelSleepEntry">
                                    إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sleep History -->
                <div class="sleep-history-section">
                    <div class="history-card">
                        <div class="card-header">
                            <h3>سجل النوم</h3>
                            <div class="history-filters">
                                <select class="form-select" id="historyFilter">
                                    <option value="week">هذا الأسبوع</option>
                                    <option value="month">هذا الشهر</option>
                                    <option value="all">جميع السجلات</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="sleep-history-list" id="sleepHistoryList">
                            <!-- Sleep history will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Sleep Chart -->
                <div class="sleep-chart-section">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>تحليل النوم</h3>
                            <select class="form-select" id="chartPeriod">
                                <option value="week">أسبوعي</option>
                                <option value="month">شهري</option>
                            </select>
                        </div>
                        <div class="chart-content">
                            <canvas id="sleepAnalysisChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadSleepData();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // زر تبديل النوم
        document.addEventListener('click', (e) => {
            if (e.target.id === 'sleepToggleBtn' || e.target.closest('#sleepToggleBtn')) {
                this.toggleSleep();
            }
            
            // أزرار جودة النوم
            if (e.target.classList.contains('quality-btn')) {
                const quality = parseInt(e.target.dataset.quality);
                this.saveSleepSession(quality);
            }
            
            // تبديل الإدخال اليدوي
            if (e.target.id === 'manualEntryToggle' || e.target.closest('#manualEntryToggle')) {
                this.toggleManualEntry();
            }
            
            // حفظ الإدخال اليدوي
            if (e.target.id === 'saveSleepEntry') {
                this.saveManualEntry();
            }
            
            // إلغاء الإدخال اليدوي
            if (e.target.id === 'cancelSleepEntry') {
                this.cancelManualEntry();
            }
        });

        // تغيير فلتر التاريخ
        document.addEventListener('change', (e) => {
            if (e.target.id === 'historyFilter') {
                this.filterSleepHistory(e.target.value);
            }
            
            if (e.target.id === 'chartPeriod') {
                this.updateSleepChart(e.target.value);
            }
        });
    }

    /**
     * تحميل بيانات النوم
     */
    loadSleepData() {
        this.updateSleepStats();
        this.loadSleepHistory();
        this.createSleepChart();
    }

    /**
     * تحديث إحصائيات النوم
     */
    updateSleepStats() {
        const sleepData = this.app.data.sleep;
        
        // النوم الليلة الماضية
        const lastNight = this.getLastNightSleep();
        document.getElementById('lastNightSleep').textContent = 
            lastNight ? `${lastNight.duration.toFixed(1)} ساعة` : '-- ساعة';
        
        if (lastNight) {
            const qualityText = this.getQualityText(lastNight.quality);
            document.getElementById('sleepQuality').textContent = `جودة: ${qualityText}`;
        }
        
        // متوسط الأسبوع
        const weeklyAverage = this.getWeeklyAverage();
        document.getElementById('weeklyAverage').textContent = 
            weeklyAverage ? `${weeklyAverage.toFixed(1)} ساعة` : '-- ساعة';
        
        // تقدم الهدف
        const todaySleep = this.getTodaySleep();
        const goalProgress = todaySleep ? (todaySleep / this.app.user.goals.sleep) * 100 : 0;
        document.getElementById('sleepGoalProgress').style.width = `${Math.min(goalProgress, 100)}%`;
        
        // أيام متتالية
        const streak = this.calculateSleepStreak();
        document.getElementById('sleepStreak').textContent = `${streak} يوم`;
    }

    /**
     * تبديل حالة النوم
     */
    toggleSleep() {
        if (this.isTracking) {
            this.stopSleepTracking();
        } else {
            this.startSleepTracking();
        }
    }

    /**
     * بدء تتبع النوم
     */
    startSleepTracking() {
        this.isTracking = true;
        this.sleepStartTime = new Date();
        
        // تحديث الواجهة
        const toggleBtn = document.getElementById('sleepToggleBtn');
        const statusText = document.querySelector('.status-text');
        const statusIndicator = document.querySelector('.status-indicator');
        
        toggleBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف النوم';
        toggleBtn.classList.remove('btn-primary');
        toggleBtn.classList.add('btn-secondary');
        
        statusText.textContent = 'نائم';
        statusIndicator.classList.add('sleeping');
        
        // بدء المؤقت
        this.startSleepTimer();
        
        this.app.showToast('تم بدء تتبع النوم', 'success');
    }

    /**
     * إيقاف تتبع النوم
     */
    stopSleepTracking() {
        this.isTracking = false;
        
        // إيقاف المؤقت
        if (this.sleepTimer) {
            clearInterval(this.sleepTimer);
        }
        
        // حساب مدة النوم
        const sleepDuration = (new Date() - this.sleepStartTime) / (1000 * 60 * 60); // بالساعات
        
        // عرض محدد جودة النوم
        document.getElementById('qualitySelector').classList.remove('hidden');
        document.getElementById('sleepTimer').classList.add('hidden');
        
        this.currentSleepSession = {
            startTime: this.sleepStartTime,
            endTime: new Date(),
            duration: sleepDuration
        };
    }

    /**
     * بدء مؤقت النوم
     */
    startSleepTimer() {
        this.sleepTimer = setInterval(() => {
            const elapsed = new Date() - this.sleepStartTime;
            const hours = Math.floor(elapsed / (1000 * 60 * 60));
            const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);
            
            document.getElementById('sleepDuration').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    /**
     * حفظ جلسة النوم
     */
    saveSleepSession(quality) {
        const sleepEntry = {
            id: Date.now(),
            startTime: this.currentSleepSession.startTime.toISOString(),
            endTime: this.currentSleepSession.endTime.toISOString(),
            duration: this.currentSleepSession.duration,
            quality: quality,
            timestamp: new Date().toISOString(),
            type: 'tracked'
        };
        
        this.app.data.sleep.push(sleepEntry);
        this.app.saveData();
        
        // إعادة تعيين الواجهة
        this.resetSleepTracker();
        
        // تحديث البيانات
        this.loadSleepData();
        
        this.app.showToast('تم حفظ بيانات النوم بنجاح', 'success');
    }

    /**
     * إعادة تعيين متتبع النوم
     */
    resetSleepTracker() {
        const toggleBtn = document.getElementById('sleepToggleBtn');
        const statusText = document.querySelector('.status-text');
        const statusIndicator = document.querySelector('.status-indicator');
        
        toggleBtn.innerHTML = '<i class="fas fa-bed"></i> بدء النوم';
        toggleBtn.classList.remove('btn-secondary');
        toggleBtn.classList.add('btn-primary');
        
        statusText.textContent = 'مستيقظ';
        statusIndicator.classList.remove('sleeping');
        
        document.getElementById('qualitySelector').classList.add('hidden');
        document.getElementById('sleepTimer').classList.remove('hidden');
        document.getElementById('sleepDuration').textContent = '00:00:00';
        
        this.sleepStartTime = null;
        this.currentSleepSession = null;
    }

    // المزيد من الدوال سيتم إضافتها...
}
