/**
 * Health App - Authentication System
 * نظام المصادقة للتطبيق الصحي
 */

class HealthAuth {
    constructor() {
        this.currentUser = null;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
        this.rememberMeTimeout = 30 * 24 * 60 * 60 * 1000; // 30 يوم
        
        this.init();
    }

    /**
     * تهيئة نظام المصادقة
     */
    init() {
        console.log('🔐 تهيئة نظام المصادقة');
        
        // التحقق من وجود جلسة نشطة
        this.checkExistingSession();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
    }

    /**
     * التحقق من وجود جلسة نشطة
     */
    checkExistingSession() {
        const sessionData = localStorage.getItem('health_app_session');
        const rememberData = localStorage.getItem('health_app_remember');
        
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                if (this.isSessionValid(session)) {
                    this.currentUser = session.user;
                    this.showApp();
                    return;
                }
            } catch (error) {
                console.error('خطأ في قراءة بيانات الجلسة:', error);
            }
        }
        
        if (rememberData) {
            try {
                const remember = JSON.parse(rememberData);
                if (this.isRememberValid(remember)) {
                    this.currentUser = remember.user;
                    this.createSession(this.currentUser, true);
                    this.showApp();
                    return;
                }
            } catch (error) {
                console.error('خطأ في قراءة بيانات التذكر:', error);
            }
        }
        
        // لا توجد جلسة صالحة، عرض صفحة تسجيل الدخول
        this.showLoginPage();
    }

    /**
     * التحقق من صحة الجلسة
     */
    isSessionValid(session) {
        if (!session || !session.user || !session.expiresAt) {
            return false;
        }
        
        return new Date().getTime() < session.expiresAt;
    }

    /**
     * التحقق من صحة بيانات التذكر
     */
    isRememberValid(remember) {
        if (!remember || !remember.user || !remember.expiresAt) {
            return false;
        }
        
        return new Date().getTime() < remember.expiresAt;
    }

    /**
     * تسجيل دخول المستخدم
     */
    async login(email, password, rememberMe = false) {
        try {
            // التحقق من صحة البيانات
            if (!this.validateEmail(email)) {
                throw new Error('البريد الإلكتروني غير صحيح');
            }
            
            if (!password || password.length < 6) {
                throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            // البحث عن المستخدم
            const user = await this.findUser(email);
            if (!user) {
                throw new Error('البريد الإلكتروني غير مسجل');
            }

            // التحقق من كلمة المرور
            const isPasswordValid = await this.verifyPassword(password, user.passwordHash);
            if (!isPasswordValid) {
                throw new Error('كلمة المرور غير صحيحة');
            }

            // تسجيل الدخول بنجاح
            this.currentUser = {
                id: user.id,
                email: user.email,
                name: user.name,
                profile: user.profile || {},
                settings: user.settings || {},
                joinDate: user.joinDate
            };

            // إنشاء جلسة
            this.createSession(this.currentUser, rememberMe);

            // تحديث آخر تسجيل دخول
            await this.updateLastLogin(user.id);

            // عرض التطبيق
            this.showApp();

            return { success: true, user: this.currentUser };

        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * تسجيل مستخدم جديد
     */
    async register(userData) {
        try {
            // التحقق من صحة البيانات
            const validation = this.validateRegistrationData(userData);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }

            // التحقق من عدم وجود المستخدم مسبقاً
            const existingUser = await this.findUser(userData.email);
            if (existingUser) {
                throw new Error('البريد الإلكتروني مسجل مسبقاً');
            }

            // تشفير كلمة المرور
            const passwordHash = await this.hashPassword(userData.password);

            // إنشاء المستخدم الجديد
            const newUser = {
                id: this.generateUserId(),
                email: userData.email.toLowerCase(),
                name: userData.name,
                passwordHash: passwordHash,
                profile: {
                    age: userData.age || null,
                    gender: userData.gender || null,
                    weight: userData.weight || null,
                    height: userData.height || null,
                    activityLevel: userData.activityLevel || 'moderate',
                    goals: {
                        sleep: userData.sleepGoal || 8,
                        water: userData.waterGoal || 2.5,
                        exercise: userData.exerciseGoal || 60,
                        calories: userData.calorieGoal || 2000
                    }
                },
                settings: {
                    theme: 'light',
                    language: 'ar',
                    notifications: {
                        water: true,
                        sleep: true,
                        exercise: true,
                        nutrition: true
                    },
                    privacy: {
                        shareData: false,
                        analytics: true
                    }
                },
                joinDate: new Date().toISOString(),
                lastLogin: new Date().toISOString(),
                isActive: true
            };

            // حفظ المستخدم
            await this.saveUser(newUser);

            // تسجيل الدخول تلقائياً
            this.currentUser = {
                id: newUser.id,
                email: newUser.email,
                name: newUser.name,
                profile: newUser.profile,
                settings: newUser.settings,
                joinDate: newUser.joinDate
            };

            // إنشاء جلسة
            this.createSession(this.currentUser, false);

            // عرض التطبيق
            this.showApp();

            return { success: true, user: this.currentUser };

        } catch (error) {
            console.error('خطأ في التسجيل:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * تسجيل خروج المستخدم
     */
    logout() {
        try {
            // مسح الجلسة
            localStorage.removeItem('health_app_session');
            localStorage.removeItem('health_app_remember');
            
            // مسح المستخدم الحالي
            this.currentUser = null;
            
            // عرض صفحة تسجيل الدخول
            this.showLoginPage();
            
            console.log('✅ تم تسجيل الخروج بنجاح');
            
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        }
    }

    /**
     * إنشاء جلسة جديدة
     */
    createSession(user, rememberMe = false) {
        const now = new Date().getTime();
        
        // إنشاء جلسة عادية
        const session = {
            user: user,
            createdAt: now,
            expiresAt: now + this.sessionTimeout
        };
        
        localStorage.setItem('health_app_session', JSON.stringify(session));
        
        // إنشاء جلسة تذكر إذا طُلب ذلك
        if (rememberMe) {
            const remember = {
                user: user,
                createdAt: now,
                expiresAt: now + this.rememberMeTimeout
            };
            
            localStorage.setItem('health_app_remember', JSON.stringify(remember));
        }
    }

    /**
     * البحث عن مستخدم
     */
    async findUser(email) {
        try {
            const users = this.getAllUsers();
            return users.find(user => user.email.toLowerCase() === email.toLowerCase());
        } catch (error) {
            console.error('خطأ في البحث عن المستخدم:', error);
            return null;
        }
    }

    /**
     * حفظ مستخدم جديد
     */
    async saveUser(user) {
        try {
            const users = this.getAllUsers();
            users.push(user);
            localStorage.setItem('health_app_users', JSON.stringify(users));
            
            // إنشاء مجلد بيانات للمستخدم
            this.initializeUserData(user.id);
            
        } catch (error) {
            console.error('خطأ في حفظ المستخدم:', error);
            throw error;
        }
    }

    /**
     * الحصول على جميع المستخدمين
     */
    getAllUsers() {
        try {
            const users = localStorage.getItem('health_app_users');
            return users ? JSON.parse(users) : [];
        } catch (error) {
            console.error('خطأ في قراءة المستخدمين:', error);
            return [];
        }
    }

    /**
     * تهيئة بيانات المستخدم
     */
    initializeUserData(userId) {
        const userDataKey = `health_app_user_data_${userId}`;
        const initialData = {
            sleep: [],
            water: [],
            exercise: [],
            nutrition: [],
            goals: [],
            reminders: [],
            achievements: [],
            createdAt: new Date().toISOString()
        };
        
        localStorage.setItem(userDataKey, JSON.stringify(initialData));
    }

    /**
     * تشفير كلمة المرور
     */
    async hashPassword(password) {
        // استخدام crypto API للتشفير
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'health_app_salt_2024');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    /**
     * التحقق من كلمة المرور
     */
    async verifyPassword(password, hash) {
        const passwordHash = await this.hashPassword(password);
        return passwordHash === hash;
    }

    /**
     * توليد معرف مستخدم فريد
     */
    generateUserId() {
        return 'user_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * التحقق من صحة بيانات التسجيل
     */
    validateRegistrationData(data) {
        if (!data.name || data.name.trim().length < 2) {
            return { isValid: false, error: 'الاسم يجب أن يكون حرفين على الأقل' };
        }
        
        if (!this.validateEmail(data.email)) {
            return { isValid: false, error: 'البريد الإلكتروني غير صحيح' };
        }
        
        if (!data.password || data.password.length < 6) {
            return { isValid: false, error: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
        }
        
        if (data.password !== data.confirmPassword) {
            return { isValid: false, error: 'كلمات المرور غير متطابقة' };
        }
        
        return { isValid: true };
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    async updateLastLogin(userId) {
        try {
            const users = this.getAllUsers();
            const userIndex = users.findIndex(user => user.id === userId);
            
            if (userIndex !== -1) {
                users[userIndex].lastLogin = new Date().toISOString();
                localStorage.setItem('health_app_users', JSON.stringify(users));
            }
        } catch (error) {
            console.error('خطأ في تحديث آخر تسجيل دخول:', error);
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مراقبة انتهاء الجلسة
        setInterval(() => {
            if (this.currentUser) {
                const sessionData = localStorage.getItem('health_app_session');
                if (sessionData) {
                    const session = JSON.parse(sessionData);
                    if (!this.isSessionValid(session)) {
                        this.logout();
                    }
                }
            }
        }, 60000); // فحص كل دقيقة
    }

    /**
     * عرض صفحة تسجيل الدخول
     */
    showLoginPage() {
        // سيتم تنفيذها في ملف منفصل
        if (typeof window.showAuthPage === 'function') {
            window.showAuthPage();
        }
    }

    /**
     * عرض التطبيق الرئيسي
     */
    showApp() {
        // سيتم تنفيذها في ملف منفصل
        if (typeof window.showMainApp === 'function') {
            window.showMainApp();
        }
    }

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * التحقق من تسجيل الدخول
     */
    isLoggedIn() {
        return this.currentUser !== null;
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthAuth = HealthAuth;
}
