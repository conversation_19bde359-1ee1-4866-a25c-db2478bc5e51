/**
 * Health App - Navigation Management
 * إدارة التنقل في التطبيق
 */

class HealthNavigation {
    constructor(app) {
        this.app = app;
        this.currentPage = 'dashboard';
        this.pageHistory = ['dashboard'];
        this.maxHistoryLength = 10;
        
        this.init();
    }

    /**
     * تهيئة نظام التنقل
     */
    init() {
        this.setupNavigationEvents();
        this.setupKeyboardNavigation();
        this.setupBrowserNavigation();
        this.loadInitialPage();
    }

    /**
     * إعداد أحداث التنقل
     */
    setupNavigationEvents() {
        // روابط التنقل في الشريط الجانبي
        document.addEventListener('click', (e) => {
            const navLink = e.target.closest('.nav-link');
            if (navLink && navLink.dataset.page) {
                e.preventDefault();
                this.navigateToPage(navLink.dataset.page);
            }
        });

        // أزرار الرجوع والتقدم
        document.addEventListener('click', (e) => {
            if (e.target.id === 'backBtn' || e.target.closest('#backBtn')) {
                this.goBack();
            }
            
            if (e.target.id === 'forwardBtn' || e.target.closest('#forwardBtn')) {
                this.goForward();
            }
        });
    }

    /**
     * إعداد التنقل بلوحة المفاتيح
     */
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Alt + أرقام للتنقل السريع
            if (e.altKey && !e.ctrlKey && !e.shiftKey) {
                const pageMap = {
                    '1': 'dashboard',
                    '2': 'sleep',
                    '3': 'water',
                    '4': 'exercise',
                    '5': 'nutrition',
                    '6': 'reports',
                    '7': 'goals',
                    '8': 'reminders',
                    '9': 'profile',
                    '0': 'settings'
                };
                
                if (pageMap[e.key]) {
                    e.preventDefault();
                    this.navigateToPage(pageMap[e.key]);
                }
            }
            
            // Ctrl + ← للرجوع
            if (e.ctrlKey && e.key === 'ArrowLeft') {
                e.preventDefault();
                this.goBack();
            }
            
            // Ctrl + → للتقدم
            if (e.ctrlKey && e.key === 'ArrowRight') {
                e.preventDefault();
                this.goForward();
            }
        });
    }

    /**
     * إعداد تنقل المتصفح
     */
    setupBrowserNavigation() {
        // معالجة أحداث التاريخ
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.loadPage(e.state.page, false);
            }
        });
    }

    /**
     * تحميل الصفحة الأولية
     */
    loadInitialPage() {
        // التحقق من URL الحالي
        const hash = window.location.hash.substring(1);
        const initialPage = hash || 'dashboard';
        
        this.navigateToPage(initialPage, false);
    }

    /**
     * التنقل إلى صفحة
     */
    navigateToPage(pageName, addToHistory = true) {
        if (!this.isValidPage(pageName)) {
            console.warn(`صفحة غير صحيحة: ${pageName}`);
            return false;
        }

        // التحقق من إمكانية مغادرة الصفحة الحالية
        if (!this.canLeavePage(this.currentPage)) {
            return false;
        }

        // تحديث التاريخ
        if (addToHistory) {
            this.addToHistory(pageName);
            this.updateBrowserHistory(pageName);
        }

        // تحميل الصفحة
        this.loadPage(pageName);
        
        return true;
    }

    /**
     * تحميل صفحة
     */
    loadPage(pageName, updateHistory = true) {
        try {
            // إخفاء جميع الصفحات
            this.hideAllPages();
            
            // إزالة الفئة النشطة من جميع الروابط
            this.deactivateAllNavLinks();
            
            // عرض الصفحة المطلوبة
            const targetPage = document.getElementById(`${pageName}Page`);
            const targetLink = document.querySelector(`[data-page="${pageName}"]`);
            
            if (targetPage) {
                // تفعيل الصفحة
                targetPage.classList.add('active');
                this.currentPage = pageName;
                
                // تحديث عنوان الصفحة
                this.updatePageTitle(pageName);
                
                // تفعيل الرابط
                if (targetLink) {
                    targetLink.classList.add('active');
                }
                
                // تحميل محتوى الصفحة
                this.loadPageContent(pageName);
                
                // تحديث URL إذا لزم الأمر
                if (updateHistory) {
                    this.updateBrowserHistory(pageName);
                }
                
                // إطلاق حدث تغيير الصفحة
                this.dispatchPageChangeEvent(pageName);
                
                return true;
            } else {
                throw new Error(`الصفحة ${pageName} غير موجودة`);
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الصفحة:', error);
            this.app.showToast('خطأ في تحميل الصفحة', 'error');
            return false;
        }
    }

    /**
     * إخفاء جميع الصفحات
     */
    hideAllPages() {
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.remove('active');
        });
    }

    /**
     * إلغاء تفعيل جميع روابط التنقل
     */
    deactivateAllNavLinks() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(pageName) {
        const titles = {
            dashboard: 'لوحة التحكم',
            sleep: 'تتبع النوم',
            water: 'تتبع الماء',
            exercise: 'التمارين الرياضية',
            nutrition: 'التغذية',
            reports: 'التقارير والإحصائيات',
            goals: 'الأهداف',
            reminders: 'التذكيرات',
            profile: 'الملف الشخصي',
            settings: 'الإعدادات'
        };
        
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && titles[pageName]) {
            pageTitle.textContent = titles[pageName];
        }
        
        // تحديث عنوان المتصفح
        document.title = `صحتي - ${titles[pageName] || 'تطبيق تتبع العادات الصحية'}`;
    }

    /**
     * تحميل محتوى الصفحة
     */
    loadPageContent(pageName) {
        // استدعاء دالة التحميل المناسبة في التطبيق الرئيسي
        const loadMethod = `load${pageName.charAt(0).toUpperCase() + pageName.slice(1)}Page`;
        if (typeof this.app[loadMethod] === 'function') {
            this.app[loadMethod]();
        }
    }

    /**
     * التحقق من صحة الصفحة
     */
    isValidPage(pageName) {
        const validPages = [
            'dashboard', 'sleep', 'water', 'exercise', 'nutrition',
            'reports', 'goals', 'reminders', 'profile', 'settings'
        ];
        return validPages.includes(pageName);
    }

    /**
     * التحقق من إمكانية مغادرة الصفحة
     */
    canLeavePage(pageName) {
        // يمكن إضافة منطق للتحقق من وجود تغييرات غير محفوظة
        // مثلاً في صفحة الإعدادات أو الملف الشخصي
        
        if (pageName === 'profile' || pageName === 'settings') {
            // التحقق من وجود تغييرات غير محفوظة
            const hasUnsavedChanges = this.checkUnsavedChanges(pageName);
            if (hasUnsavedChanges) {
                return confirm('لديك تغييرات غير محفوظة. هل تريد المتابعة؟');
            }
        }
        
        return true;
    }

    /**
     * التحقق من التغييرات غير المحفوظة
     */
    checkUnsavedChanges(pageName) {
        // منطق للتحقق من التغييرات غير المحفوظة
        // يمكن تطويره حسب الحاجة
        return false;
    }

    /**
     * إضافة إلى التاريخ
     */
    addToHistory(pageName) {
        // إزالة الصفحة من التاريخ إذا كانت موجودة
        const existingIndex = this.pageHistory.indexOf(pageName);
        if (existingIndex !== -1) {
            this.pageHistory.splice(existingIndex, 1);
        }
        
        // إضافة الصفحة في المقدمة
        this.pageHistory.unshift(pageName);
        
        // الحفاظ على حد أقصى للتاريخ
        if (this.pageHistory.length > this.maxHistoryLength) {
            this.pageHistory = this.pageHistory.slice(0, this.maxHistoryLength);
        }
    }

    /**
     * تحديث تاريخ المتصفح
     */
    updateBrowserHistory(pageName) {
        const url = `#${pageName}`;
        const state = { page: pageName, timestamp: Date.now() };
        
        if (window.location.hash !== url) {
            window.history.pushState(state, '', url);
        }
    }

    /**
     * الرجوع للصفحة السابقة
     */
    goBack() {
        if (this.pageHistory.length > 1) {
            const previousPage = this.pageHistory[1];
            this.navigateToPage(previousPage);
        } else {
            this.app.showToast('لا توجد صفحة سابقة', 'info');
        }
    }

    /**
     * التقدم للصفحة التالية
     */
    goForward() {
        // يمكن تطوير هذه الوظيفة لاحقاً
        this.app.showToast('وظيفة التقدم غير متاحة حالياً', 'info');
    }

    /**
     * إطلاق حدث تغيير الصفحة
     */
    dispatchPageChangeEvent(pageName) {
        const event = new CustomEvent('pageChanged', {
            detail: {
                currentPage: pageName,
                previousPage: this.pageHistory[1] || null,
                timestamp: Date.now()
            }
        });
        
        document.dispatchEvent(event);
    }

    /**
     * الحصول على الصفحة الحالية
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * الحصول على تاريخ التنقل
     */
    getNavigationHistory() {
        return [...this.pageHistory];
    }

    /**
     * مسح تاريخ التنقل
     */
    clearHistory() {
        this.pageHistory = [this.currentPage];
    }

    /**
     * التنقل إلى الصفحة الرئيسية
     */
    goHome() {
        this.navigateToPage('dashboard');
    }

    /**
     * إعداد اختصارات التنقل
     */
    setupNavigationShortcuts() {
        // يمكن إضافة اختصارات إضافية هنا
        console.log('تم إعداد اختصارات التنقل');
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        this.pageHistory = [];
        console.log('تم تنظيف موارد التنقل');
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthNavigation = HealthNavigation;
}
