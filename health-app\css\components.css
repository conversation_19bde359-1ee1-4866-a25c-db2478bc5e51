/* ===== COMPONENTS STYLES ===== */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    line-height: 1.5;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--white);
    border-color: var(--secondary-color);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--white);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--gray-800);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    color: var(--gray-800);
    background: var(--white);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-input:invalid {
    border-color: var(--accent-color);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
    appearance: none;
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-modal);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.modal-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* Notifications Panel */
.notifications-panel {
    position: fixed;
    top: var(--header-height);
    left: var(--spacing-xl);
    width: 350px;
    max-height: 500px;
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    z-index: var(--z-popover);
    transform: translateY(-20px);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.notifications-panel.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.panel-header {
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--gray-900);
}

.panel-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.panel-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.panel-content {
    max-height: 400px;
    overflow-y: auto;
}

.notifications-list {
    padding: var(--spacing-md);
}

.notification-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    transition: background var(--transition-fast);
    cursor: pointer;
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-item.unread {
    background: rgba(16, 185, 129, 0.05);
    border-left: 4px solid var(--primary-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
    background: var(--gradient-primary);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.notification-message {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.notification-time {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-xl);
    left: var(--spacing-xl);
    z-index: var(--z-tooltip);
    pointer-events: none;
}

.toast {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    max-width: 400px;
    transform: translateX(-100%);
    opacity: 0;
    transition: all var(--transition-normal);
    pointer-events: auto;
    position: relative;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left: 4px solid var(--primary-color);
}

.toast.error {
    border-left: 4px solid var(--accent-color);
}

.toast.warning {
    border-left: 4px solid var(--exercise-color);
}

.toast.info {
    border-left: 4px solid var(--secondary-color);
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
    flex-shrink: 0;
}

.toast.success .toast-icon {
    background: var(--primary-color);
}

.toast.error .toast-icon {
    background: var(--accent-color);
}

.toast.warning .toast-icon {
    background: var(--exercise-color);
}

.toast.info .toast-icon {
    background: var(--secondary-color);
}

.toast-message {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.toast-text {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.toast-close {
    position: absolute;
    top: var(--spacing-sm);
    left: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1em;
    margin-bottom: var(--spacing-sm);
}

.skeleton-text:last-child {
    margin-bottom: 0;
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-card {
    height: 200px;
    width: 100%;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.select-none { user-select: none; }
.select-text { user-select: text; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* Responsive Utilities */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        max-width: none;
    }
    
    .notifications-panel {
        width: calc(100% - 2 * var(--spacing-xl));
        left: var(--spacing-xl);
        right: var(--spacing-xl);
    }
    
    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
        top: var(--spacing-md);
    }
    
    .toast {
        max-width: none;
    }
}

/* ===== COMING SOON STYLES ===== */
.coming-soon {
    text-align: center;
    padding: var(--spacing-4xl) var(--spacing-2xl);
    color: var(--gray-600);
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    margin: var(--spacing-xl) 0;
}

.coming-soon i {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xl);
    opacity: 0.7;
}

.coming-soon h3 {
    font-size: var(--font-size-2xl);
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.coming-soon p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
}

.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    text-align: right;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.feature-item i {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    width: 16px;
    flex-shrink: 0;
}

.feature-item span {
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* ===== WATER BOTTLE STYLES ===== */
.water-progress-section {
    margin-bottom: var(--spacing-2xl);
}

.progress-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2xl);
}

.water-bottle {
    flex-shrink: 0;
}

.bottle-container {
    position: relative;
    width: 120px;
    height: 300px;
}

.bottle {
    width: 100%;
    height: 280px;
    background: linear-gradient(to bottom,
        transparent 0%,
        rgba(6, 182, 212, 0.1) 20%,
        rgba(6, 182, 212, 0.1) 100%);
    border: 3px solid var(--water-color);
    border-radius: 0 0 40px 40px;
    position: relative;
    overflow: hidden;
}

.bottle-cap {
    width: 60px;
    height: 30px;
    background: var(--water-color);
    border-radius: 15px 15px 0 0;
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
}

.water-level {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 0%;
    background: linear-gradient(to top, #06b6d4, #0891b2);
    transition: height 0.5s ease-in-out;
    border-radius: 0 0 37px 37px;
}

.bottle-marks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.mark {
    position: absolute;
    right: -40px;
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-weight: 600;
}

.mark-25 { bottom: 75%; }
.mark-50 { bottom: 50%; }
.mark-75 { bottom: 25%; }
.mark-100 { bottom: 5%; }

.progress-info {
    flex: 1;
    text-align: center;
}

.current-intake {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.intake-amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--water-color);
}

.intake-unit {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    font-weight: 600;
}

.goal-info {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-md);
}

.progress-percentage {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== QUICK ADD STYLES ===== */
.quick-add-section {
    margin-bottom: var(--spacing-2xl);
}

.quick-add-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.quick-add-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.quick-add-btn:hover {
    border-color: var(--water-color);
    background: rgba(6, 182, 212, 0.05);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.quick-add-btn .btn-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-water);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
}

.quick-add-btn .btn-amount {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-800);
}

.quick-add-btn .btn-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* ===== CUSTOM AMOUNT STYLES ===== */
.custom-amount-section {
    margin-bottom: var(--spacing-2xl);
}

.custom-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.custom-input-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.custom-input-group .form-input {
    flex: 1;
    max-width: 200px;
}

.input-unit {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 600;
}

/* ===== WATER LOG STYLES ===== */
.water-log-section {
    margin-bottom: var(--spacing-2xl);
}

.water-log-list {
    max-height: 400px;
    overflow-y: auto;
}

.water-entry {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-100);
    transition: background var(--transition-fast);
}

.water-entry:hover {
    background: var(--gray-50);
}

.water-entry:last-child {
    border-bottom: none;
}

.water-entry .entry-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-water);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-sm);
}

.water-entry .entry-info {
    flex: 1;
}

.water-entry .entry-amount {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.water-entry .entry-time {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.delete-entry {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.delete-entry:hover {
    background: var(--gray-100);
    color: var(--accent-color);
}

.empty-log {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--gray-500);
}

.empty-log i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* ===== SLEEP TRACKER STYLES ===== */
.sleep-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.sleep-stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.sleep-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.sleep-tracker-section {
    margin-bottom: var(--spacing-2xl);
}

.tracker-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.tracker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.sleep-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-400);
    transition: background var(--transition-fast);
}

.status-indicator.sleeping {
    background: var(--sleep-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.sleep-timer {
    margin-bottom: var(--spacing-xl);
}

.timer-display {
    font-size: 3rem;
    font-weight: 800;
    color: var(--sleep-color);
    margin-bottom: var(--spacing-xl);
    font-family: 'Courier New', monospace;
}

.quality-selector {
    margin-top: var(--spacing-xl);
}

.quality-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.quality-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.quality-btn:hover {
    border-color: var(--sleep-color);
    background: rgba(99, 102, 241, 0.05);
}

.quality-btn i {
    font-size: var(--font-size-xl);
    color: var(--sleep-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .progress-card {
        flex-direction: column;
        text-align: center;
    }

    .quick-add-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .custom-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .custom-input-group .form-input {
        max-width: none;
    }

    .sleep-stats-grid {
        grid-template-columns: 1fr;
    }

    .quality-options {
        grid-template-columns: repeat(2, 1fr);
    }
}
