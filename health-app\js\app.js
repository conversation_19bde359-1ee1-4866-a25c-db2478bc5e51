/**
 * Health App - Main Application Controller
 * التطبيق الرئيسي لتتبع العادات الصحية
 */

class HealthApp {
    constructor() {
        this.version = '2.0.0';
        this.isInitialized = false;
        this.currentPage = 'dashboard';
        this.user = null;
        this.data = {
            sleep: [],
            water: [],
            exercise: [],
            nutrition: [],
            goals: [],
            reminders: []
        };
        
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    async init() {
        try {
            console.log(`🌟 Health App v${this.version} - بدء التشغيل`);

            // إخفاء شاشة التحميل فوراً
            this.hideLoadingScreenImmediate();

            // تحميل البيانات
            await this.loadData();

            // تهيئة المكونات
            this.initializeComponents();

            // إعداد معالجات الأحداث
            this.setupEventHandlers();

            // تهيئة التنقل
            this.initializeNavigation();

            // تحديث التاريخ
            this.updateCurrentDate();

            // تحميل الصفحة الافتراضية
            this.loadPage('dashboard');

            this.isInitialized = true;
            console.log('✅ تم تشغيل التطبيق بنجاح');

        } catch (error) {
            console.error('❌ خطأ في تشغيل التطبيق:', error);
            this.handleError(error);
        }
    }

    /**
     * عرض شاشة التحميل
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        const progressBar = document.getElementById('loadingProgress');
        const loadingText = document.getElementById('loadingText');
        
        if (!loadingScreen) return;
        
        loadingScreen.style.display = 'flex';
        
        // محاكاة تقدم التحميل
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            progressBar.style.width = `${progress}%`;
            
            if (progress < 30) {
                loadingText.textContent = 'جاري تحميل البيانات...';
            } else if (progress < 60) {
                loadingText.textContent = 'جاري تهيئة المكونات...';
            } else if (progress < 90) {
                loadingText.textContent = 'جاري إعداد الواجهة...';
            } else {
                loadingText.textContent = 'اكتمل التحميل!';
                clearInterval(interval);
            }
        }, 100);
    }

    /**
     * إخفاء شاشة التحميل
     */
    async hideLoadingScreen() {
        return new Promise((resolve) => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                        resolve();
                    }, 500);
                } else {
                    resolve();
                }
            }, 500);
        });
    }

    /**
     * إخفاء شاشة التحميل فوراً
     */
    hideLoadingScreenImmediate() {
        const loadingScreen = document.getElementById('loadingScreen');
        const app = document.getElementById('app');

        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }

        if (app) {
            app.style.display = 'flex';
            app.style.opacity = '1';
        }
    }

    /**
     * تحميل البيانات
     */
    async loadData() {
        try {
            // تحميل بيانات المستخدم
            this.user = HealthStorage.getUser() || {
                name: 'مستخدم جديد',
                age: 25,
                weight: 70,
                height: 170,
                gender: 'male',
                activityLevel: 'moderate',
                goals: {
                    sleep: 8,
                    water: 2.5,
                    exercise: 60,
                    calories: 2000
                },
                joinDate: new Date().toISOString()
            };
            
            // تحميل بيانات العادات
            this.data.sleep = HealthStorage.getSleepData() || [];
            this.data.water = HealthStorage.getWaterData() || [];
            this.data.exercise = HealthStorage.getExerciseData() || [];
            this.data.nutrition = HealthStorage.getNutritionData() || [];
            this.data.goals = HealthStorage.getGoals() || [];
            this.data.reminders = HealthStorage.getReminders() || [];
            
            // تحديث اسم المستخدم في الواجهة
            const userNameElement = document.getElementById('userName');
            if (userNameElement) {
                userNameElement.textContent = this.user.name;
            }
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showToast('خطأ في تحميل البيانات', 'error');
        }
    }

    /**
     * حفظ البيانات
     */
    saveData() {
        try {
            HealthStorage.saveUser(this.user);
            HealthStorage.saveSleepData(this.data.sleep);
            HealthStorage.saveWaterData(this.data.water);
            HealthStorage.saveExerciseData(this.data.exercise);
            HealthStorage.saveNutritionData(this.data.nutrition);
            HealthStorage.saveGoals(this.data.goals);
            HealthStorage.saveReminders(this.data.reminders);
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            this.showToast('خطأ في حفظ البيانات', 'error');
        }
    }

    /**
     * تهيئة المكونات
     */
    initializeComponents() {
        // تهيئة الرسوم البيانية
        if (typeof HealthCharts !== 'undefined') {
            this.charts = new HealthCharts();
        }

        // تهيئة لوحة التحكم
        if (typeof HealthDashboard !== 'undefined') {
            this.dashboard = new HealthDashboard(this);
        }

        // تهيئة متتبع الماء
        if (typeof WaterTracker !== 'undefined') {
            this.waterTracker = new WaterTracker(this);
        }

        // تهيئة متتبع النوم
        if (typeof SleepTracker !== 'undefined') {
            this.sleepTracker = new SleepTracker(this);
        }

        // تهيئة متتبع التمارين
        if (typeof ExerciseTracker !== 'undefined') {
            this.exerciseTracker = new ExerciseTracker(this);
        }

        // تهيئة متتبع التغذية
        if (typeof NutritionTracker !== 'undefined') {
            this.nutritionTracker = new NutritionTracker(this);
        }

        // تهيئة مدير التقارير
        if (typeof ReportsManager !== 'undefined') {
            this.reportsManager = new ReportsManager(this);
        }

        // تهيئة مدير الأهداف
        if (typeof GoalsManager !== 'undefined') {
            this.goalsManager = new GoalsManager(this);
        }

        // تهيئة مدير التذكيرات
        if (typeof RemindersManager !== 'undefined') {
            this.remindersManager = new RemindersManager(this);
        }

        // تهيئة مدير الملف الشخصي
        if (typeof ProfileManager !== 'undefined') {
            this.profileManager = new ProfileManager(this);
        }

        // تهيئة مدير الإعدادات
        if (typeof SettingsManager !== 'undefined') {
            this.settingsManager = new SettingsManager(this);
        }

        // تهيئة مكونات أخرى
        this.initializeModals();
        this.initializeNotifications();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // زر القائمة المحمولة
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        // زر تبديل الشريط الجانبي
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        // زر الإشعارات
        const notificationsBtn = document.getElementById('notificationsBtn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => {
                this.toggleNotifications();
            });
        }
        
        // زر الإضافة السريعة
        const quickAddBtn = document.getElementById('quickAddBtn');
        if (quickAddBtn) {
            quickAddBtn.addEventListener('click', () => {
                this.showQuickAddModal();
            });
        }
        
        // الإجراءات السريعة
        const actionBtns = document.querySelectorAll('.action-btn');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });
        
        // إغلاق المودالات عند النقر خارجها
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', () => {
                this.closeAllModals();
            });
        }
        
        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
        
        // حفظ البيانات عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });
    }

    /**
     * تهيئة التنقل
     */
    initializeNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                if (page) {
                    this.loadPage(page);
                }
            });
        });
    }

    /**
     * تحميل صفحة
     */
    loadPage(pageName) {
        try {
            // إخفاء جميع الصفحات
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('active');
            });
            
            // إزالة الفئة النشطة من جميع الروابط
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // عرض الصفحة المطلوبة
            const targetPage = document.getElementById(`${pageName}Page`);
            const targetLink = document.querySelector(`[data-page="${pageName}"]`);
            
            if (targetPage) {
                targetPage.classList.add('active');
                this.currentPage = pageName;
                
                // تحديث عنوان الصفحة
                this.updatePageTitle(pageName);
                
                // تفعيل الرابط
                if (targetLink) {
                    targetLink.classList.add('active');
                }
                
                // تحميل محتوى الصفحة
                this.loadPageContent(pageName);
                
                // تحديث URL
                window.history.pushState({ page: pageName }, '', `#${pageName}`);
                
            } else {
                console.warn(`الصفحة ${pageName} غير موجودة`);
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الصفحة:', error);
            this.showToast('خطأ في تحميل الصفحة', 'error');
        }
    }

    /**
     * تحميل محتوى الصفحة
     */
    loadPageContent(pageName) {
        switch (pageName) {
            case 'dashboard':
                if (this.dashboard) {
                    this.dashboard.loadDashboard();
                }
                break;
            case 'sleep':
                if (this.sleepTracker) {
                    this.sleepTracker.loadSleepData();
                }
                break;
            case 'water':
                if (this.waterTracker) {
                    this.waterTracker.loadWaterData();
                }
                break;
            case 'exercise':
                if (this.exerciseTracker) {
                    this.exerciseTracker.loadExerciseData();
                }
                break;
            case 'nutrition':
                if (this.nutritionTracker) {
                    this.nutritionTracker.loadNutritionData();
                }
                break;
            case 'reports':
                if (this.reportsManager) {
                    this.reportsManager.loadReports();
                }
                break;
            case 'goals':
                if (this.goalsManager) {
                    this.goalsManager.loadGoals();
                }
                break;
            case 'reminders':
                if (this.remindersManager) {
                    this.remindersManager.loadReminders();
                }
                break;
            case 'profile':
                if (this.profileManager) {
                    this.profileManager.loadProfile();
                }
                break;
            case 'settings':
                if (this.settingsManager) {
                    this.settingsManager.loadSettings();
                }
                break;
            default:
                console.warn(`محتوى الصفحة ${pageName} غير محدد`);
        }
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(pageName) {
        const titles = {
            dashboard: 'لوحة التحكم',
            sleep: 'تتبع النوم',
            water: 'تتبع الماء',
            exercise: 'التمارين الرياضية',
            nutrition: 'التغذية',
            reports: 'التقارير والإحصائيات',
            goals: 'الأهداف',
            reminders: 'التذكيرات',
            profile: 'الملف الشخصي',
            settings: 'الإعدادات'
        };
        
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && titles[pageName]) {
            pageTitle.textContent = titles[pageName];
        }
    }

    /**
     * تحديث التاريخ الحالي
     */
    updateCurrentDate() {
        const currentDateElement = document.getElementById('currentDate');
        if (currentDateElement) {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    }

    /**
     * تبديل الشريط الجانبي
     */
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            if (window.innerWidth <= 1024) {
                sidebar.classList.toggle('open');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        }
    }

    /**
     * تبديل الإشعارات
     */
    toggleNotifications() {
        const notificationsPanel = document.getElementById('notificationsPanel');
        if (notificationsPanel) {
            notificationsPanel.classList.toggle('active');
            
            if (notificationsPanel.classList.contains('active')) {
                this.loadNotifications();
            }
        }
    }

    /**
     * تحميل الإشعارات
     */
    loadNotifications() {
        const notificationsList = document.getElementById('notificationsList');
        if (!notificationsList) return;
        
        // إشعارات تجريبية
        const notifications = [
            {
                id: 1,
                title: 'تذكير شرب الماء',
                message: 'حان وقت شرب كوب من الماء!',
                time: 'منذ 5 دقائق',
                type: 'water',
                unread: true
            },
            {
                id: 2,
                title: 'إنجاز جديد',
                message: 'تهانينا! لقد حققت هدف النوم لهذا الأسبوع',
                time: 'منذ ساعة',
                type: 'achievement',
                unread: true
            },
            {
                id: 3,
                title: 'تقرير أسبوعي',
                message: 'تقريرك الأسبوعي جاهز للمراجعة',
                time: 'منذ يوم',
                type: 'report',
                unread: false
            }
        ];
        
        notificationsList.innerHTML = notifications.map(notification => `
            <div class="notification-item ${notification.unread ? 'unread' : ''}" data-id="${notification.id}">
                <div class="notification-icon ${notification.type}">
                    <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${notification.time}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon(type) {
        const icons = {
            water: 'tint',
            sleep: 'moon',
            exercise: 'dumbbell',
            nutrition: 'apple-alt',
            achievement: 'trophy',
            report: 'chart-bar',
            reminder: 'bell'
        };
        return icons[type] || 'bell';
    }

    /**
     * عرض مودال الإضافة السريعة
     */
    showQuickAddModal() {
        const modal = document.getElementById('quickAddModal');
        const overlay = document.getElementById('modalOverlay');
        
        if (modal && overlay) {
            overlay.classList.add('active');
            modal.classList.add('active');
        }
    }

    /**
     * معالجة الإجراءات السريعة
     */
    handleQuickAction(action) {
        switch (action) {
            case 'add-water':
                this.addWaterQuick();
                break;
            case 'log-sleep':
                this.logSleepQuick();
                break;
            case 'start-workout':
                this.startWorkoutQuick();
                break;
            case 'add-meal':
                this.addMealQuick();
                break;
            default:
                console.warn(`إجراء غير معروف: ${action}`);
        }
    }

    /**
     * إضافة ماء سريع
     */
    addWaterQuick() {
        const waterEntry = {
            id: Date.now(),
            amount: 250, // 250ml
            timestamp: new Date().toISOString(),
            type: 'water'
        };
        
        this.data.water.push(waterEntry);
        this.saveData();
        this.showToast('تم إضافة كوب ماء (250ml)', 'success');
        
        // تحديث الإحصائيات
        if (this.dashboard) {
            this.dashboard.updateStats();
        }
    }

    /**
     * تسجيل نوم سريع
     */
    logSleepQuick() {
        this.showToast('سيتم فتح صفحة تسجيل النوم', 'info');
        this.loadPage('sleep');
    }

    /**
     * بدء تمرين سريع
     */
    startWorkoutQuick() {
        this.showToast('سيتم فتح صفحة التمارين', 'info');
        this.loadPage('exercise');
    }

    /**
     * إضافة وجبة سريعة
     */
    addMealQuick() {
        this.showToast('سيتم فتح صفحة التغذية', 'info');
        this.loadPage('nutrition');
    }

    /**
     * تحميل صفحة النوم
     */
    loadSleepPage() {
        if (!this.sleepTracker) {
            this.sleepTracker = new SleepTracker(this);
        }
    }

    /**
     * تحميل صفحة الماء
     */
    loadWaterPage() {
        if (!this.waterTracker) {
            this.waterTracker = new WaterTracker(this);
        }
    }

    /**
     * تحميل صفحة التمارين
     */
    loadExercisePage() {
        const exercisePage = document.getElementById('exercisePage');
        if (!exercisePage) return;

        exercisePage.innerHTML = `
            <div class="exercise-container">
                <div class="page-header">
                    <h2>التمارين الرياضية</h2>
                    <p>تتبع نشاطك البدني وتمارينك اليومية</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-dumbbell"></i>
                    <h3>قريباً</h3>
                    <p>صفحة التمارين الرياضية قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة التغذية
     */
    loadNutritionPage() {
        const nutritionPage = document.getElementById('nutritionPage');
        if (!nutritionPage) return;

        nutritionPage.innerHTML = `
            <div class="nutrition-container">
                <div class="page-header">
                    <h2>التغذية</h2>
                    <p>راقب نظامك الغذائي والسعرات الحرارية</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-apple-alt"></i>
                    <h3>قريباً</h3>
                    <p>صفحة التغذية قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة التقارير
     */
    loadReportsPage() {
        const reportsPage = document.getElementById('reportsPage');
        if (!reportsPage) return;

        reportsPage.innerHTML = `
            <div class="reports-container">
                <div class="page-header">
                    <h2>التقارير والإحصائيات</h2>
                    <p>تحليل مفصل لتقدمك الصحي</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-chart-bar"></i>
                    <h3>قريباً</h3>
                    <p>صفحة التقارير قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة الأهداف
     */
    loadGoalsPage() {
        const goalsPage = document.getElementById('goalsPage');
        if (!goalsPage) return;

        goalsPage.innerHTML = `
            <div class="goals-container">
                <div class="page-header">
                    <h2>الأهداف</h2>
                    <p>حدد وتتبع أهدافك الصحية</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-bullseye"></i>
                    <h3>قريباً</h3>
                    <p>صفحة الأهداف قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة التذكيرات
     */
    loadRemindersPage() {
        const remindersPage = document.getElementById('remindersPage');
        if (!remindersPage) return;

        remindersPage.innerHTML = `
            <div class="reminders-container">
                <div class="page-header">
                    <h2>التذكيرات</h2>
                    <p>إدارة تنبيهاتك وتذكيراتك</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-bell"></i>
                    <h3>قريباً</h3>
                    <p>صفحة التذكيرات قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة الملف الشخصي
     */
    loadProfilePage() {
        const profilePage = document.getElementById('profilePage');
        if (!profilePage) return;

        profilePage.innerHTML = `
            <div class="profile-container">
                <div class="page-header">
                    <h2>الملف الشخصي</h2>
                    <p>معلوماتك الشخصية وإعداداتك</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-user"></i>
                    <h3>قريباً</h3>
                    <p>صفحة الملف الشخصي قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تحميل صفحة الإعدادات
     */
    loadSettingsPage() {
        const settingsPage = document.getElementById('settingsPage');
        if (!settingsPage) return;

        settingsPage.innerHTML = `
            <div class="settings-container">
                <div class="page-header">
                    <h2>الإعدادات</h2>
                    <p>تخصيص التطبيق حسب احتياجاتك</p>
                </div>

                <div class="coming-soon">
                    <i class="fas fa-cog"></i>
                    <h3>قريباً</h3>
                    <p>صفحة الإعدادات قيد التطوير</p>
                </div>
            </div>
        `;
    }

    /**
     * تهيئة المودالات
     */
    initializeModals() {
        // إغلاق المودالات عند النقر على زر الإغلاق
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || e.target.closest('.modal-close')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal.id);
                }
            }
        });
    }

    /**
     * تهيئة الإشعارات
     */
    initializeNotifications() {
        // إغلاق لوحة الإشعارات
        const closeNotifications = document.getElementById('closeNotifications');
        if (closeNotifications) {
            closeNotifications.addEventListener('click', () => {
                this.toggleNotifications();
            });
        }
    }

    /**
     * إغلاق جميع المودالات
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        const overlay = document.getElementById('modalOverlay');

        modals.forEach(modal => {
            modal.classList.remove('active');
        });

        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * إغلاق مودال محدد
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        const overlay = document.getElementById('modalOverlay');

        if (modal) {
            modal.classList.remove('active');
        }

        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * عرض إشعار منبثق
     */
    showToast(message, type = 'info', duration = 5000) {
        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        // إنشاء الإشعار
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">
                    <i class="fas fa-${iconMap[type] || iconMap.info}"></i>
                </div>
                <div class="toast-message">
                    <div class="toast-text">${message}</div>
                </div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // إضافة معالج الإغلاق
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.removeToast(toast);
        });

        // إضافة الإشعار
        toastContainer.appendChild(toast);

        // عرض الإشعار
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // إزالة تلقائية
        setTimeout(() => {
            this.removeToast(toast);
        }, duration);
    }

    /**
     * إزالة إشعار
     */
    removeToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    /**
     * معالجة تغيير حجم النافذة
     */
    handleWindowResize() {
        // إغلاق الشريط الجانبي على الشاشات الصغيرة
        if (window.innerWidth <= 1024) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
            }
        }
    }

    /**
     * معالجة الأخطاء
     */
    handleError(error) {
        console.error('خطأ في التطبيق:', error);

        // عرض رسالة خطأ للمستخدم
        this.showToast('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');

        // يمكن إضافة تقرير الأخطاء هنا
    }

    /**
     * معالجة الإجراءات السريعة
     */
    handleQuickAction(action) {
        switch (action) {
            case 'add-water':
                // إضافة كوب ماء سريع
                if (this.waterTracker) {
                    this.waterTracker.addWater(250);
                } else {
                    this.loadPage('water');
                }
                break;

            case 'log-sleep':
                // الانتقال لصفحة النوم
                this.loadPage('sleep');
                break;

            case 'start-workout':
                // الانتقال لصفحة التمارين
                this.loadPage('exercise');
                break;

            case 'add-meal':
                // الانتقال لصفحة التغذية
                this.loadPage('nutrition');
                break;

            default:
                console.warn(`إجراء غير معروف: ${action}`);
        }
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        console.log('🧹 تنظيف موارد التطبيق');

        // حفظ البيانات
        this.saveData();

        // تنظيف المؤقتات
        if (this.dashboard && this.dashboard.cleanup) {
            this.dashboard.cleanup();
        }

        // تنظيف الرسوم البيانية
        if (this.charts && this.charts.destroyAllCharts) {
            this.charts.destroyAllCharts();
        }
    }
}

// إنشاء مثيل التطبيق عند تحميل الصفحة
let healthApp;

document.addEventListener('DOMContentLoaded', () => {
    healthApp = new HealthApp();
});

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (healthApp) {
        healthApp.cleanup();
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthApp = HealthApp;
}
}
