/**
 * Health App - Complete Integration Fix
 * إصلاح التكامل الشامل للتطبيق
 */

class IntegrationFix {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.fixes = [];
        
        this.init();
    }

    /**
     * تهيئة الإصلاح الشامل
     */
    init() {
        console.log('🔧 بدء إصلاح التكامل الشامل');
        
        // إصلاح المشاكل الأساسية
        this.fixCoreIssues();
        
        // إصلاح التكامل بين المكونات
        this.fixComponentIntegration();
        
        // إصلاح نظام المصادقة
        this.fixAuthenticationSystem();
        
        // إصلاح التنقل
        this.fixNavigation();
        
        // إصلاح عرض البيانات
        this.fixDataDisplay();
        
        // اختبار شامل
        this.runComprehensiveTests();
        
        // عرض النتائج
        this.showResults();
    }

    /**
     * إصلاح المشاكل الأساسية
     */
    fixCoreIssues() {
        try {
            // التأكد من تحميل جميع المكونات الأساسية
            this.ensureComponentsLoaded();
            
            // إصلاح الدوال المفقودة
            this.fixMissingFunctions();
            
            // إصلاح معالجة الأخطاء
            this.fixErrorHandling();
            
            this.fixes.push('✅ تم إصلاح المشاكل الأساسية');
            
        } catch (error) {
            this.errors.push(`❌ خطأ في إصلاح المشاكل الأساسية: ${error.message}`);
        }
    }

    /**
     * التأكد من تحميل المكونات
     */
    ensureComponentsLoaded() {
        const requiredComponents = [
            'HealthStorage',
            'HealthAuth',
            'HealthApp'
        ];

        requiredComponents.forEach(component => {
            if (typeof window[component] === 'undefined') {
                this.warnings.push(`⚠️ المكون ${component} غير محمل`);
            } else {
                console.log(`✅ المكون ${component} محمل بنجاح`);
            }
        });
    }

    /**
     * إصلاح الدوال المفقودة
     */
    fixMissingFunctions() {
        // إضافة دوال مفقودة لـ HealthStorage
        if (typeof HealthStorage !== 'undefined') {
            // دالة الحصول على آخر مدة نوم (تم إضافتها بالفعل)
            if (!HealthStorage.getLastSleepDuration) {
                HealthStorage.getLastSleepDuration = function() {
                    const sleepData = this.getSleepData();
                    if (sleepData.length === 0) return 0;
                    
                    const sortedData = sleepData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    return sortedData[0].duration || 0;
                };
                this.fixes.push('✅ تم إضافة دالة getLastSleepDuration');
            }

            // دالة الحصول على إحصائيات سريعة
            if (!HealthStorage.getQuickStats) {
                HealthStorage.getQuickStats = function() {
                    return {
                        water: this.getWaterStats ? this.getWaterStats() : { todayTotal: 0 },
                        sleep: this.getSleepStats ? this.getSleepStats() : { lastDuration: 0 },
                        exercise: this.getExerciseStats ? this.getExerciseStats() : { todayTotal: 0 }
                    };
                };
                this.fixes.push('✅ تم إضافة دالة getQuickStats');
            }
        }
    }

    /**
     * إصلاح معالجة الأخطاء
     */
    fixErrorHandling() {
        // إضافة معالج أخطاء عام
        window.addEventListener('error', (event) => {
            console.error('خطأ JavaScript:', event.error);
            this.handleGlobalError(event.error);
        });

        // إضافة معالج للوعود المرفوضة
        window.addEventListener('unhandledrejection', (event) => {
            console.error('وعد مرفوض:', event.reason);
            this.handleGlobalError(event.reason);
        });
    }

    /**
     * معالجة الأخطاء العامة
     */
    handleGlobalError(error) {
        // عرض رسالة خطأ للمستخدم
        this.showErrorToast(`حدث خطأ: ${error.message || error}`);
        
        // محاولة إصلاح تلقائي
        this.attemptAutoFix(error);
    }

    /**
     * محاولة إصلاح تلقائي
     */
    attemptAutoFix(error) {
        const errorMessage = error.message || error.toString();
        
        // إصلاح أخطاء الدوال المفقودة
        if (errorMessage.includes('is not a function')) {
            console.log('🔧 محاولة إصلاح دالة مفقودة...');
            this.fixMissingFunctions();
        }
        
        // إصلاح أخطاء التخزين
        if (errorMessage.includes('localStorage')) {
            console.log('🔧 محاولة إصلاح مشاكل التخزين...');
            this.fixStorageIssues();
        }
    }

    /**
     * إصلاح مشاكل التخزين
     */
    fixStorageIssues() {
        try {
            // اختبار localStorage
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            
            // إصلاح البيانات التالفة
            const keys = Object.keys(localStorage).filter(key => key.startsWith('health_app_'));
            keys.forEach(key => {
                try {
                    const data = localStorage.getItem(key);
                    JSON.parse(data);
                } catch (error) {
                    console.warn(`إزالة بيانات تالفة: ${key}`);
                    localStorage.removeItem(key);
                }
            });
            
            this.fixes.push('✅ تم إصلاح مشاكل التخزين');
            
        } catch (error) {
            this.errors.push(`❌ خطأ في إصلاح التخزين: ${error.message}`);
        }
    }

    /**
     * إصلاح التكامل بين المكونات
     */
    fixComponentIntegration() {
        try {
            // ربط التطبيق الرئيسي بالمكونات
            if (window.healthApp) {
                this.integrateMainApp();
            }
            
            // ربط نظام الإشعارات
            this.integrateNotifications();
            
            // ربط نظام التقارير
            this.integrateReports();
            
            // ربط نظام الإنجازات
            this.integrateAchievements();
            
            this.fixes.push('✅ تم إصلاح التكامل بين المكونات');
            
        } catch (error) {
            this.errors.push(`❌ خطأ في التكامل: ${error.message}`);
        }
    }

    /**
     * تكامل التطبيق الرئيسي
     */
    integrateMainApp() {
        const app = window.healthApp;
        
        // التأكد من تحميل البيانات
        if (!app.data || Object.keys(app.data).length === 0) {
            app.loadData();
        }
        
        // التأكد من تهيئة المكونات المتقدمة
        if (!app.smartNotifications && typeof SmartNotifications !== 'undefined') {
            app.smartNotifications = new SmartNotifications();
        }
        
        if (!app.advancedReports && typeof AdvancedReports !== 'undefined') {
            app.advancedReports = new AdvancedReports();
        }
        
        if (!app.achievementsSystem && typeof AchievementsSystem !== 'undefined') {
            app.achievementsSystem = new AchievementsSystem();
        }
    }

    /**
     * تكامل نظام الإشعارات
     */
    integrateNotifications() {
        if (typeof SmartNotifications !== 'undefined' && window.healthApp) {
            if (!window.healthApp.smartNotifications) {
                window.healthApp.smartNotifications = new SmartNotifications();
                console.log('✅ تم تكامل نظام الإشعارات الذكية');
            }
        }
    }

    /**
     * تكامل نظام التقارير
     */
    integrateReports() {
        if (typeof AdvancedReports !== 'undefined' && window.healthApp) {
            if (!window.healthApp.advancedReports) {
                window.healthApp.advancedReports = new AdvancedReports();
                console.log('✅ تم تكامل نظام التقارير المتقدمة');
            }
        }
    }

    /**
     * تكامل نظام الإنجازات
     */
    integrateAchievements() {
        if (typeof AchievementsSystem !== 'undefined' && window.healthApp) {
            if (!window.healthApp.achievementsSystem) {
                window.healthApp.achievementsSystem = new AchievementsSystem();
                console.log('✅ تم تكامل نظام الإنجازات');
            }
        }
    }

    /**
     * إصلاح نظام المصادقة
     */
    fixAuthenticationSystem() {
        try {
            // التحقق من حالة تسجيل الدخول
            this.checkAuthenticationState();
            
            // إصلاح قائمة المستخدم
            this.fixUserMenu();
            
            // إصلاح الجلسات
            this.fixSessions();
            
            this.fixes.push('✅ تم إصلاح نظام المصادقة');
            
        } catch (error) {
            this.errors.push(`❌ خطأ في نظام المصادقة: ${error.message}`);
        }
    }

    /**
     * فحص حالة المصادقة
     */
    checkAuthenticationState() {
        const session = localStorage.getItem('health_app_session');
        
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                const isValid = new Date().getTime() < sessionData.expiresAt;
                
                if (!isValid) {
                    localStorage.removeItem('health_app_session');
                    this.warnings.push('⚠️ تم إزالة جلسة منتهية الصلاحية');
                } else {
                    console.log('✅ جلسة المستخدم صالحة');
                    this.updateUserInterface(sessionData.user);
                }
            } catch (error) {
                localStorage.removeItem('health_app_session');
                this.warnings.push('⚠️ تم إزالة جلسة تالفة');
            }
        }
    }

    /**
     * تحديث واجهة المستخدم
     */
    updateUserInterface(user) {
        if (!user) return;
        
        // تحديث اسم المستخدم
        const userNameElements = document.querySelectorAll('.user-name, #userName');
        userNameElements.forEach(element => {
            element.textContent = user.name || 'مستخدم';
        });
        
        // تحديث صورة المستخدم
        const userAvatarElements = document.querySelectorAll('.user-avatar, #userAvatar');
        userAvatarElements.forEach(element => {
            element.src = this.generateAvatarUrl(user.name || 'مستخدم');
        });
        
        // إظهار قائمة المستخدم
        const userMenu = document.querySelector('.user-menu');
        if (userMenu) {
            userMenu.style.display = 'block';
        }
    }

    /**
     * توليد رابط صورة افتراضية
     */
    generateAvatarUrl(name) {
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
        const color = colors[name.length % colors.length];
        
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color.substring(1)}&color=fff&size=40&font-size=0.6`;
    }

    /**
     * إصلاح قائمة المستخدم
     */
    fixUserMenu() {
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userMenu = document.querySelector('.user-menu');
        
        if (userMenuBtn && userMenu) {
            // إزالة مستمعي الأحداث القدامى
            const newBtn = userMenuBtn.cloneNode(true);
            userMenuBtn.parentNode.replaceChild(newBtn, userMenuBtn);
            
            // إضافة مستمع جديد
            newBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('active');
            });
            
            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', (e) => {
                if (!userMenu.contains(e.target)) {
                    userMenu.classList.remove('active');
                }
            });
            
            console.log('✅ تم إصلاح قائمة المستخدم');
        }
    }

    /**
     * إصلاح الجلسات
     */
    fixSessions() {
        // فحص وإصلاح الجلسات المنتهية الصلاحية
        const session = localStorage.getItem('health_app_session');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                if (new Date().getTime() >= sessionData.expiresAt) {
                    localStorage.removeItem('health_app_session');
                    this.warnings.push('⚠️ تم إزالة جلسة منتهية الصلاحية');
                }
            } catch (error) {
                localStorage.removeItem('health_app_session');
                this.warnings.push('⚠️ تم إزالة جلسة تالفة');
            }
        }
    }

    /**
     * إصلاح التنقل
     */
    fixNavigation() {
        try {
            // إصلاح روابط التنقل
            this.fixNavigationLinks();

            // إصلاح الأزرار التفاعلية
            this.fixInteractiveButtons();

            // إصلاح التنقل بين الصفحات
            this.fixPageNavigation();

            this.fixes.push('✅ تم إصلاح نظام التنقل');

        } catch (error) {
            this.errors.push(`❌ خطأ في إصلاح التنقل: ${error.message}`);
        }
    }

    /**
     * إصلاح روابط التنقل
     */
    fixNavigationLinks() {
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            if (!link.hasAttribute('data-fixed')) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.getAttribute('data-page');
                    if (page) {
                        this.navigateToPage(page);
                    }
                });
                link.setAttribute('data-fixed', 'true');
            }
        });

        console.log(`✅ تم إصلاح ${navLinks.length} رابط تنقل`);
    }

    /**
     * إصلاح الأزرار التفاعلية
     */
    fixInteractiveButtons() {
        // إصلاح زر الإضافة السريعة
        const quickAddBtn = document.getElementById('quickAddBtn');
        if (quickAddBtn && !quickAddBtn.hasAttribute('data-fixed')) {
            quickAddBtn.addEventListener('click', () => {
                this.showQuickAddModal();
            });
            quickAddBtn.setAttribute('data-fixed', 'true');
        }

        // إصلاح أزرار الإجراءات
        const actionBtns = document.querySelectorAll('.action-btn');
        actionBtns.forEach(btn => {
            if (!btn.hasAttribute('data-fixed')) {
                btn.addEventListener('click', (e) => {
                    const action = btn.getAttribute('data-action') || btn.textContent.trim();
                    this.handleQuickAction(action);
                });
                btn.setAttribute('data-fixed', 'true');
            }
        });

        console.log(`✅ تم إصلاح ${actionBtns.length} زر تفاعلي`);
    }

    /**
     * التنقل إلى صفحة
     */
    navigateToPage(page) {
        try {
            if (window.healthApp && window.healthApp.loadPage) {
                window.healthApp.loadPage(page);
            } else {
                // تنقل بديل
                this.alternativeNavigation(page);
            }
        } catch (error) {
            console.error('خطأ في التنقل:', error);
            this.alternativeNavigation(page);
        }
    }

    /**
     * تنقل بديل
     */
    alternativeNavigation(page) {
        // إخفاء جميع الصفحات
        const pages = document.querySelectorAll('.page');
        pages.forEach(p => p.classList.remove('active'));

        // إزالة الفئة النشطة من الروابط
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => link.classList.remove('active'));

        // إظهار الصفحة المطلوبة
        const targetPage = document.getElementById(`${page}Page`);
        const targetLink = document.querySelector(`[data-page="${page}"]`);

        if (targetPage) {
            targetPage.classList.add('active');
            if (targetLink) {
                targetLink.classList.add('active');
            }

            // تحديث عنوان الصفحة
            this.updatePageTitle(page);

            console.log(`✅ تم الانتقال إلى صفحة ${page}`);
        }
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(page) {
        const titles = {
            dashboard: 'لوحة التحكم',
            sleep: 'تتبع النوم',
            water: 'تتبع الماء',
            exercise: 'التمارين الرياضية',
            nutrition: 'التغذية',
            reports: 'التقارير',
            goals: 'الأهداف',
            reminders: 'التذكيرات',
            profile: 'الملف الشخصي',
            settings: 'الإعدادات'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && titles[page]) {
            pageTitle.textContent = titles[page];
        }
    }

    /**
     * إصلاح التنقل بين الصفحات
     */
    fixPageNavigation() {
        // إصلاح التنقل بالتاريخ
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.page) {
                this.navigateToPage(event.state.page);
            }
        });

        // إصلاح التنقل بالهاش
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash.substring(1);
            if (hash) {
                this.navigateToPage(hash);
            }
        });
    }

    /**
     * عرض مودال الإضافة السريعة
     */
    showQuickAddModal() {
        const modal = document.getElementById('quickAddModal');
        const overlay = document.getElementById('modalOverlay');

        if (modal && overlay) {
            overlay.classList.add('active');
            modal.classList.add('active');
        } else {
            // إنشاء مودال بديل
            this.createQuickAddModal();
        }
    }

    /**
     * إنشاء مودال إضافة سريعة
     */
    createQuickAddModal() {
        const modalHTML = `
            <div id="modalOverlay" class="modal-overlay active">
                <div id="quickAddModal" class="modal active">
                    <div class="modal-header">
                        <h3>إضافة سريعة</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="window.integrationFix.addWaterQuick()">
                                <i class="fas fa-tint"></i>
                                إضافة كوب ماء
                            </button>
                            <button class="quick-action-btn" onclick="window.integrationFix.navigateToPage('sleep')">
                                <i class="fas fa-moon"></i>
                                تسجيل النوم
                            </button>
                            <button class="quick-action-btn" onclick="window.integrationFix.navigateToPage('exercise')">
                                <i class="fas fa-dumbbell"></i>
                                بدء تمرين
                            </button>
                            <button class="quick-action-btn" onclick="window.integrationFix.navigateToPage('nutrition')">
                                <i class="fas fa-apple-alt"></i>
                                إضافة وجبة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * معالجة الإجراءات السريعة
     */
    handleQuickAction(action) {
        if (action.includes('ماء') || action === 'add-water') {
            this.addWaterQuick();
        } else if (action.includes('نوم') || action === 'log-sleep') {
            this.navigateToPage('sleep');
        } else if (action.includes('تمرين') || action === 'start-workout') {
            this.navigateToPage('exercise');
        } else if (action.includes('وجبة') || action === 'add-meal') {
            this.navigateToPage('nutrition');
        }
    }

    /**
     * إضافة ماء سريع
     */
    addWaterQuick() {
        try {
            if (typeof HealthStorage !== 'undefined') {
                const waterEntry = {
                    id: Date.now(),
                    amount: 250,
                    timestamp: new Date().toISOString(),
                    type: 'glass'
                };

                HealthStorage.addWaterEntry(waterEntry);
                this.showSuccessToast('تم إضافة كوب ماء (250ml)');

                // تحديث الإحصائيات
                this.updateDashboardStats();

                // إشعار الإنجازات
                if (window.healthApp && window.healthApp.achievementsSystem) {
                    window.healthApp.achievementsSystem.checkAchievements();
                }

            } else {
                this.showErrorToast('نظام التخزين غير متاح');
            }
        } catch (error) {
            this.showErrorToast('خطأ في إضافة الماء');
            console.error('خطأ في إضافة الماء:', error);
        }
    }

    /**
     * إصلاح عرض البيانات
     */
    fixDataDisplay() {
        try {
            // تحديث الإحصائيات
            this.updateDashboardStats();

            // إصلاح الرسوم البيانية
            this.fixCharts();

            // تحديث التاريخ
            this.updateCurrentDate();

            this.fixes.push('✅ تم إصلاح عرض البيانات');

        } catch (error) {
            this.errors.push(`❌ خطأ في عرض البيانات: ${error.message}`);
        }
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats() {
        try {
            if (typeof HealthStorage !== 'undefined') {
                // إحصائيات الماء
                const waterStats = HealthStorage.getWaterStats ? HealthStorage.getWaterStats() : { todayTotal: 0 };
                this.updateWaterDisplay(waterStats);

                // إحصائيات النوم
                const sleepStats = HealthStorage.getSleepStats ? HealthStorage.getSleepStats() : { lastDuration: 0 };
                this.updateSleepDisplay(sleepStats);

                // إحصائيات التمارين
                const exerciseStats = HealthStorage.getExerciseStats ? HealthStorage.getExerciseStats() : { todayTotal: 0 };
                this.updateExerciseDisplay(exerciseStats);

                console.log('✅ تم تحديث إحصائيات لوحة التحكم');
            }
        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الماء
     */
    updateWaterDisplay(stats) {
        const waterElements = {
            todayTotal: document.querySelector('#waterToday, .water-today'),
            progress: document.querySelector('#waterProgress, .water-progress'),
            percentage: document.querySelector('#waterPercentage, .water-percentage')
        };

        const goal = 2500; // 2.5 لتر
        const percentage = Math.min((stats.todayTotal / goal) * 100, 100);

        if (waterElements.todayTotal) {
            waterElements.todayTotal.textContent = `${stats.todayTotal}ml`;
        }

        if (waterElements.progress) {
            waterElements.progress.style.width = `${percentage}%`;
        }

        if (waterElements.percentage) {
            waterElements.percentage.textContent = `${Math.round(percentage)}%`;
        }
    }

    /**
     * تحديث عرض النوم
     */
    updateSleepDisplay(stats) {
        const sleepElements = {
            lastDuration: document.querySelector('#sleepDuration, .sleep-duration'),
            quality: document.querySelector('#sleepQuality, .sleep-quality'),
            average: document.querySelector('#sleepAverage, .sleep-average')
        };

        if (sleepElements.lastDuration) {
            sleepElements.lastDuration.textContent = `${stats.lastDuration || 0} ساعة`;
        }

        if (sleepElements.average) {
            sleepElements.average.textContent = `${(stats.averageDuration || 0).toFixed(1)} ساعة`;
        }
    }

    /**
     * تحديث عرض التمارين
     */
    updateExerciseDisplay(stats) {
        const exerciseElements = {
            todayTotal: document.querySelector('#exerciseToday, .exercise-today'),
            progress: document.querySelector('#exerciseProgress, .exercise-progress'),
            percentage: document.querySelector('#exercisePercentage, .exercise-percentage')
        };

        const goal = 60; // 60 دقيقة
        const percentage = Math.min((stats.todayTotal / goal) * 100, 100);

        if (exerciseElements.todayTotal) {
            exerciseElements.todayTotal.textContent = `${stats.todayTotal} دقيقة`;
        }

        if (exerciseElements.progress) {
            exerciseElements.progress.style.width = `${percentage}%`;
        }

        if (exerciseElements.percentage) {
            exerciseElements.percentage.textContent = `${Math.round(percentage)}%`;
        }
    }

    /**
     * إصلاح الرسوم البيانية
     */
    fixCharts() {
        if (typeof Chart !== 'undefined') {
            // تحسين إعدادات Chart.js
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
            Chart.defaults.plugins.legend.rtl = true;

            console.log('✅ تم إصلاح إعدادات الرسوم البيانية');
        }
    }

    /**
     * تحديث التاريخ الحالي
     */
    updateCurrentDate() {
        const currentDateElement = document.getElementById('currentDate');
        if (currentDateElement) {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    }

    /**
     * اختبار شامل
     */
    runComprehensiveTests() {
        console.log('🧪 بدء الاختبار الشامل...');

        // اختبار المكونات الأساسية
        this.testCoreComponents();

        // اختبار التخزين
        this.testStorage();

        // اختبار التنقل
        this.testNavigation();

        // اختبار الواجهة
        this.testUserInterface();

        console.log('✅ انتهى الاختبار الشامل');
    }

    /**
     * اختبار المكونات الأساسية
     */
    testCoreComponents() {
        const components = ['HealthStorage', 'HealthAuth', 'HealthApp'];

        components.forEach(component => {
            if (typeof window[component] !== 'undefined') {
                console.log(`✅ ${component}: متاح`);
            } else {
                this.warnings.push(`⚠️ ${component}: غير متاح`);
            }
        });
    }

    /**
     * اختبار التخزين
     */
    testStorage() {
        try {
            localStorage.setItem('test_key', 'test_value');
            const value = localStorage.getItem('test_key');
            localStorage.removeItem('test_key');

            if (value === 'test_value') {
                console.log('✅ التخزين: يعمل بشكل صحيح');
            } else {
                this.warnings.push('⚠️ التخزين: مشكلة في القراءة/الكتابة');
            }
        } catch (error) {
            this.errors.push('❌ التخزين: غير متاح');
        }
    }

    /**
     * اختبار التنقل
     */
    testNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const pages = document.querySelectorAll('.page');

        console.log(`✅ روابط التنقل: ${navLinks.length}`);
        console.log(`✅ الصفحات: ${pages.length}`);

        if (navLinks.length === 0) {
            this.warnings.push('⚠️ لا توجد روابط تنقل');
        }

        if (pages.length === 0) {
            this.warnings.push('⚠️ لا توجد صفحات');
        }
    }

    /**
     * اختبار واجهة المستخدم
     */
    testUserInterface() {
        const userMenu = document.querySelector('.user-menu');
        const sidebar = document.getElementById('sidebar');
        const header = document.querySelector('.header');

        if (userMenu) console.log('✅ قائمة المستخدم: موجودة');
        else this.warnings.push('⚠️ قائمة المستخدم: مفقودة');

        if (sidebar) console.log('✅ الشريط الجانبي: موجود');
        else this.warnings.push('⚠️ الشريط الجانبي: مفقود');

        if (header) console.log('✅ الرأس: موجود');
        else this.warnings.push('⚠️ الرأس: مفقود');
    }

    /**
     * عرض النتائج
     */
    showResults() {
        console.log('\n🎯 نتائج إصلاح التكامل الشامل:');
        console.log(`✅ الإصلاحات: ${this.fixes.length}`);
        console.log(`⚠️ التحذيرات: ${this.warnings.length}`);
        console.log(`❌ الأخطاء: ${this.errors.length}`);

        if (this.fixes.length > 0) {
            console.log('\n✅ الإصلاحات المكتملة:');
            this.fixes.forEach(fix => console.log(fix));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ التحذيرات:');
            this.warnings.forEach(warning => console.log(warning));
        }

        if (this.errors.length > 0) {
            console.log('\n❌ الأخطاء:');
            this.errors.forEach(error => console.log(error));
        }

        // عرض إشعار للمستخدم
        if (this.errors.length === 0) {
            this.showSuccessToast('تم إصلاح التكامل بنجاح!');
        } else {
            this.showErrorToast(`${this.errors.length} خطأ يحتاج إصلاح`);
        }
    }

    /**
     * عرض إشعار نجاح
     */
    showSuccessToast(message) {
        this.showToast(message, 'success');
    }

    /**
     * عرض إشعار خطأ
     */
    showErrorToast(message) {
        this.showToast(message, 'error');
    }

    /**
     * عرض إشعار
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        `;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
}

// تشغيل إصلاح التكامل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.integrationFix = new IntegrationFix();
    }, 1000);
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.IntegrationFix = IntegrationFix;
}
