/**
 * PDFMerge Pro - PDF Processing Engine
 * محرك معالجة ملفات PDF المتقدم
 */

class PDFProcessor {
    constructor() {
        this.files = [];
        this.isProcessing = false;
        this.onProgress = null;
        this.onComplete = null;
        this.onError = null;
    }

    /**
     * إضافة ملفات PDF للمعالجة
     * @param {FileList|Array} files - قائمة الملفات
     */
    async addFiles(files) {
        const fileArray = Array.from(files);
        const validFiles = [];

        for (const file of fileArray) {
            try {
                // التحقق من نوع الملف
                if (!isPDFFile(file)) {
                    throw new Error(`الملف ${file.name} ليس ملف PDF صحيح`);
                }

                // التحقق من حجم الملف
                if (!isValidFileSize(file)) {
                    throw new Error(`الملف ${file.name} كبير جداً (الحد الأقصى 100MB)`);
                }

                // قراءة الملف والتحقق من صحته
                const arrayBuffer = await readFileAsArrayBuffer(file);
                const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
                
                const fileInfo = {
                    id: generateFileId(),
                    file: file,
                    name: file.name,
                    size: file.size,
                    arrayBuffer: arrayBuffer,
                    pdfDoc: pdfDoc,
                    pageCount: pdfDoc.getPageCount(),
                    addedAt: new Date()
                };

                validFiles.push(fileInfo);
            } catch (error) {
                console.error(`خطأ في معالجة الملف ${file.name}:`, error);
                if (this.onError) {
                    this.onError(error, file.name);
                }
            }
        }

        this.files.push(...validFiles);
        return validFiles;
    }

    /**
     * إزالة ملف من القائمة
     * @param {string} fileId - معرف الملف
     */
    removeFile(fileId) {
        this.files = this.files.filter(file => file.id !== fileId);
    }

    /**
     * إعادة ترتيب الملفات
     * @param {string} fileId - معرف الملف
     * @param {number} newIndex - الموضع الجديد
     */
    reorderFile(fileId, newIndex) {
        const fileIndex = this.files.findIndex(file => file.id === fileId);
        if (fileIndex === -1) return;

        const [file] = this.files.splice(fileIndex, 1);
        this.files.splice(newIndex, 0, file);
    }

    /**
     * الحصول على معلومات الملفات
     */
    getFilesInfo() {
        return this.files.map(file => ({
            id: file.id,
            name: file.name,
            size: file.size,
            pageCount: file.pageCount,
            addedAt: file.addedAt
        }));
    }

    /**
     * دمج جميع الملفات
     * @param {Object} options - خيارات الدمج
     */
    async mergeFiles(options = {}) {
        if (this.files.length === 0) {
            throw new Error('لا توجد ملفات للدمج');
        }

        if (this.isProcessing) {
            throw new Error('عملية دمج أخرى قيد التنفيذ');
        }

        this.isProcessing = true;
        const startTime = performance.now();

        try {
            // إنشاء مستند PDF جديد
            const mergedPdf = await PDFLib.PDFDocument.create();
            let totalPages = 0;
            let processedFiles = 0;

            // حساب العدد الإجمالي للصفحات
            const totalPagesCount = this.files.reduce((sum, file) => sum + file.pageCount, 0);

            // دمج كل ملف
            for (const fileInfo of this.files) {
                try {
                    if (this.onProgress) {
                        this.onProgress({
                            type: 'file_start',
                            fileName: fileInfo.name,
                            fileIndex: processedFiles,
                            totalFiles: this.files.length,
                            processedPages: totalPages,
                            totalPages: totalPagesCount
                        });
                    }

                    // نسخ الصفحات من الملف الحالي
                    const pageIndices = Array.from(
                        { length: fileInfo.pageCount }, 
                        (_, i) => i
                    );
                    
                    const copiedPages = await mergedPdf.copyPages(
                        fileInfo.pdfDoc, 
                        pageIndices
                    );

                    // إضافة الصفحات للمستند المدموج
                    copiedPages.forEach(page => {
                        mergedPdf.addPage(page);
                        totalPages++;

                        if (this.onProgress) {
                            this.onProgress({
                                type: 'page_processed',
                                fileName: fileInfo.name,
                                fileIndex: processedFiles,
                                totalFiles: this.files.length,
                                processedPages: totalPages,
                                totalPages: totalPagesCount,
                                percentage: (totalPages / totalPagesCount) * 100
                            });
                        }
                    });

                    processedFiles++;

                    if (this.onProgress) {
                        this.onProgress({
                            type: 'file_complete',
                            fileName: fileInfo.name,
                            fileIndex: processedFiles - 1,
                            totalFiles: this.files.length,
                            processedPages: totalPages,
                            totalPages: totalPagesCount
                        });
                    }

                } catch (error) {
                    console.error(`خطأ في دمج الملف ${fileInfo.name}:`, error);
                    if (this.onError) {
                        this.onError(error, fileInfo.name);
                    }
                    // متابعة معالجة الملفات الأخرى
                }
            }

            // تطبيق التحسينات إذا كانت مطلوبة
            if (options.optimize) {
                await this.optimizePDF(mergedPdf);
            }

            // تطبيق الضغط إذا كان مطلوباً
            if (options.compress) {
                await this.compressPDF(mergedPdf, options.compressionLevel || 'medium');
            }

            // إنشاء الملف النهائي
            const pdfBytes = await mergedPdf.save();
            const endTime = performance.now();
            const processingTime = endTime - startTime;

            const result = {
                pdfBytes: pdfBytes,
                fileName: this.generateMergedFileName(),
                fileSize: pdfBytes.length,
                pageCount: totalPages,
                processingTime: processingTime,
                originalFiles: this.files.length,
                originalSize: this.files.reduce((sum, file) => sum + file.size, 0)
            };

            if (this.onComplete) {
                this.onComplete(result);
            }

            return result;

        } catch (error) {
            console.error('خطأ في عملية الدمج:', error);
            if (this.onError) {
                this.onError(error);
            }
            throw error;
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * تحسين ملف PDF
     * @param {PDFDocument} pdfDoc - مستند PDF
     */
    async optimizePDF(pdfDoc) {
        try {
            // ضغط الصور (إذا كان متاحاً)
            // إزالة البيانات الوصفية غير الضرورية
            // تحسين الخطوط

            // هذه ميزة متقدمة تحتاج مكتبات إضافية
            console.log('تطبيق تحسينات PDF...');
        } catch (error) {
            console.warn('فشل في تحسين PDF:', error);
        }
    }

    /**
     * ضغط ملف PDF
     * @param {PDFDocument} pdfDoc - مستند PDF
     * @param {string} level - مستوى الضغط (low, medium, high)
     */
    async compressPDF(pdfDoc, level = 'medium') {
        try {
            console.log(`تطبيق ضغط ${level} على PDF...`);

            // إعدادات الضغط حسب المستوى
            const compressionSettings = {
                low: {
                    imageQuality: 0.9,
                    removeMetadata: false,
                    optimizeFonts: true,
                    compressStreams: false
                },
                medium: {
                    imageQuality: 0.7,
                    removeMetadata: true,
                    optimizeFonts: true,
                    compressStreams: true
                },
                high: {
                    imageQuality: 0.5,
                    removeMetadata: true,
                    optimizeFonts: true,
                    compressStreams: true
                }
            };

            const settings = compressionSettings[level] || compressionSettings.medium;

            // إزالة البيانات الوصفية إذا كان مطلوباً
            if (settings.removeMetadata) {
                pdfDoc.setTitle('');
                pdfDoc.setAuthor('');
                pdfDoc.setSubject('');
                pdfDoc.setKeywords([]);
                pdfDoc.setProducer('PDFMerge Pro');
                pdfDoc.setCreator('PDFMerge Pro');
            }

            // تحسين الخطوط
            if (settings.optimizeFonts) {
                // تحسين الخطوط المدمجة
                console.log('تحسين الخطوط...');
            }

            // ضغط المحتوى
            if (settings.compressStreams) {
                console.log('ضغط المحتوى...');
                // تطبيق ضغط على المحتوى
            }

            console.log(`تم تطبيق ضغط ${level} بنجاح`);

        } catch (error) {
            console.warn('فشل في ضغط PDF:', error);
            throw new Error(`فشل في ضغط الملف: ${error.message}`);
        }
    }

    /**
     * إنشاء اسم للملف المدموج
     */
    generateMergedFileName() {
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
        return `merged_document_${timestamp}.pdf`;
    }

    /**
     * تحليل ملف PDF للحصول على معلومات متقدمة
     * @param {File} file - ملف PDF
     */
    async analyzePDF(file) {
        try {
            const arrayBuffer = await readFileAsArrayBuffer(file);
            const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
            
            const pageCount = pdfDoc.getPageCount();
            const pages = [];

            // تحليل كل صفحة
            for (let i = 0; i < Math.min(pageCount, 10); i++) { // تحليل أول 10 صفحات فقط
                const page = pdfDoc.getPage(i);
                const { width, height } = page.getSize();
                
                pages.push({
                    index: i,
                    width: Math.round(width),
                    height: Math.round(height),
                    orientation: width > height ? 'landscape' : 'portrait'
                });
            }

            return {
                pageCount,
                pages,
                title: pdfDoc.getTitle() || '',
                author: pdfDoc.getAuthor() || '',
                subject: pdfDoc.getSubject() || '',
                creator: pdfDoc.getCreator() || '',
                creationDate: pdfDoc.getCreationDate(),
                modificationDate: pdfDoc.getModificationDate()
            };

        } catch (error) {
            console.error('خطأ في تحليل PDF:', error);
            throw error;
        }
    }

    /**
     * إنشاء معاينة مصغرة للصفحة الأولى
     * @param {File} file - ملف PDF
     */
    async generateThumbnail(file) {
        try {
            // هذه الميزة تحتاج مكتبة إضافية مثل PDF.js
            // سيتم تنفيذها في إصدار لاحق
            return null;
        } catch (error) {
            console.error('خطأ في إنشاء المعاينة:', error);
            return null;
        }
    }

    /**
     * تصدير إعدادات الدمج
     */
    exportSettings() {
        return {
            files: this.getFilesInfo(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * استيراد إعدادات الدمج
     * @param {Object} settings - الإعدادات المحفوظة
     */
    importSettings(settings) {
        // سيتم تنفيذ هذه الميزة لاحقاً
        console.log('استيراد الإعدادات:', settings);
    }

    /**
     * مسح جميع الملفات
     */
    clearFiles() {
        this.files = [];
    }

    /**
     * الحصول على إحصائيات المعالجة
     */
    getStats() {
        const totalSize = this.files.reduce((sum, file) => sum + file.size, 0);
        const totalPages = this.files.reduce((sum, file) => sum + file.pageCount, 0);
        
        return {
            fileCount: this.files.length,
            totalSize: totalSize,
            totalPages: totalPages,
            averageFileSize: this.files.length > 0 ? totalSize / this.files.length : 0,
            averagePagesPerFile: this.files.length > 0 ? totalPages / this.files.length : 0
        };
    }
}

// إنشاء مثيل عام للمعالج
const pdfProcessor = new PDFProcessor();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
    window.PDFProcessor = PDFProcessor;
    window.pdfProcessor = pdfProcessor;
}
