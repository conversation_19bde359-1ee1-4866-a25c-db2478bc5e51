/* ===== UPLOAD SECTION ===== */
.upload-section {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.upload-container {
    max-width: 800px;
    margin: 0 auto;
}

.upload-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.upload-header h2 {
    font-size: var(--font-size-3xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.upload-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    text-align: center;
    transition: all var(--transition-normal);
    background: var(--gray-50);
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    background-opacity: 0.05;
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.upload-content {
    position: relative;
    z-index: 2;
}

.upload-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.upload-area h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.upload-area p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
}

.upload-features {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
}

.upload-features .feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.upload-features .feature i {
    color: var(--success-color);
}

/* ===== FILES LIST ===== */
.files-list {
    margin-top: var(--spacing-2xl);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.files-header h3 {
    margin: 0;
    color: var(--gray-800);
}

.files-actions {
    display: flex;
    gap: var(--spacing-md);
}

.files-container {
    padding: var(--spacing-lg);
}

.file-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    background: var(--white);
    transition: all var(--transition-fast);
    cursor: grab;
}

.file-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.file-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    cursor: grabbing;
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-drag-handle {
    color: var(--gray-400);
    cursor: grab;
    font-size: var(--font-size-lg);
}

.file-icon {
    font-size: var(--font-size-2xl);
    color: var(--error-color);
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.file-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.file-details {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.file-preview {
    width: 60px;
    height: 80px;
    background: var(--gray-100);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    border: 1px solid var(--gray-200);
}

.file-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.file-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--gray-100);
    color: var(--gray-600);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.file-action-btn.delete:hover {
    background: var(--error-color);
}

/* ===== MERGE CONTROLS ===== */
.merge-controls {
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

.merge-options {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    justify-content: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: bold;
}

/* ===== COMPRESSION SETTINGS ===== */
.compression-settings {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    transition: all var(--transition-normal);
}

.compression-settings h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.compression-settings h4::before {
    content: '🗜️';
    font-size: var(--font-size-xl);
}

.compression-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.compression-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: background var(--transition-fast);
}

.compression-option:hover {
    background: var(--gray-50);
}

.compression-option input[type="radio"] {
    display: none;
}

.radio-mark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    position: relative;
    transition: all var(--transition-fast);
}

.compression-option input[type="radio"]:checked + .radio-mark {
    border-color: var(--primary-color);
    background: var(--primary-color);
}

.compression-option input[type="radio"]:checked + .radio-mark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--white);
    border-radius: 50%;
}

.compression-option span:not(.radio-mark) {
    font-weight: 500;
    color: var(--gray-700);
}

.compression-option input[type="radio"]:checked ~ span:not(.radio-mark) {
    color: var(--primary-color);
    font-weight: 600;
}

/* Compression Level Indicators */
.compression-option[data-level="low"] {
    border-right: 3px solid var(--success-color);
}

.compression-option[data-level="medium"] {
    border-right: 3px solid var(--warning-color);
}

.compression-option[data-level="high"] {
    border-right: 3px solid var(--error-color);
}

/* ===== ADDITIONAL TOOLS SECTION ===== */
.tools-section {
    padding: var(--spacing-3xl) 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.tools-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.tools-header h2 {
    font-size: var(--font-size-3xl);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.tools-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1000px;
    margin: 0 auto;
}

.tool-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.tool-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.tool-card:nth-child(2) .tool-icon {
    background: var(--gradient-secondary);
}

.tool-card:nth-child(3) .tool-icon {
    background: var(--gradient-success);
}

.tool-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.tool-card p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.tool-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-normal);
    background: var(--gray-50);
    position: relative;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.tool-upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.02);
}

.tool-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-600);
}

.tool-upload-content i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.tool-btn {
    width: 100%;
    justify-content: center;
}

/* Tool-specific styles */
.compress-tool .tool-upload-area {
    border-color: var(--primary-color);
}

.split-tool .tool-upload-area {
    border-color: var(--secondary-color);
}

.images-tool .tool-upload-area {
    border-color: var(--success-color);
}

/* File preview in tools */
.tool-file-preview {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.tool-file-preview .file-icon {
    font-size: var(--font-size-xl);
    color: var(--error-color);
}

.tool-file-preview .file-info {
    flex: 1;
    text-align: right;
}

.tool-file-preview .file-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.tool-file-preview .file-size {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.tool-file-preview .remove-file {
    background: var(--error-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
}

/* Compression result display */
.compression-result {
    background: var(--success-color);
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-lg);
    text-align: center;
}

.compression-stats {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
}

.compression-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.compression-stat .label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.compression-stat .value {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

/* Compression Progress */
.compression-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--gray-700);
}

.progress-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.progress-text {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: progressShimmer 2s ease-in-out infinite;
}

.progress-percentage {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

@keyframes progressShimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== MODAL ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

/* ===== PROCESSING MODAL ===== */
.processing-content {
    text-align: center;
}

.processing-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.processing-content h3 {
    margin-bottom: var(--spacing-xl);
    color: var(--gray-800);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
}

.processing-stats {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-lg);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.stat-value {
    font-weight: 600;
    color: var(--gray-800);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }
    
    .nav {
        display: none;
    }
    
    .header-actions .btn {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-features {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .upload-area {
        padding: var(--spacing-xl);
    }
    
    .upload-features {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .files-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .files-actions {
        justify-content: center;
    }
    
    .file-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .file-details {
        justify-content: center;
    }
    
    .merge-options {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }
    
    .processing-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 280px;
    }
    
    .upload-area {
        padding: var(--spacing-lg);
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .modal-content {
        padding: var(--spacing-lg);
        margin: var(--spacing-md);
    }
}

/* ===== ANIMATIONS ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== UTILITY CLASSES ===== */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden;
}

.opacity-0 {
    opacity: 0;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-100 {
    opacity: 1;
}

/* ===== NOTIFICATIONS SYSTEM ===== */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: var(--z-tooltip);
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

.notification {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transform: translateX(-100%);
    opacity: 0;
    transition: all var(--transition-normal);
    pointer-events: auto;
    border-right: 4px solid var(--info-color);
}

.notification.notification-show {
    transform: translateX(0);
    opacity: 1;
}

.notification.notification-hide {
    transform: translateX(-100%);
    opacity: 0;
}

.notification-success {
    border-right-color: var(--success-color);
}

.notification-error {
    border-right-color: var(--error-color);
}

.notification-warning {
    border-right-color: var(--warning-color);
}

.notification-info {
    border-right-color: var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.notification-content i {
    font-size: var(--font-size-lg);
}

.notification-success .notification-content i {
    color: var(--success-color);
}

.notification-error .notification-content i {
    color: var(--error-color);
}

.notification-warning .notification-content i {
    color: var(--warning-color);
}

.notification-info .notification-content i {
    color: var(--info-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* ===== ERROR HANDLING ===== */
.initialization-error {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
}

.error-content {
    text-align: center;
    max-width: 500px;
    padding: var(--spacing-2xl);
}

.error-content h2 {
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
}

.error-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
}

/* ===== ADVANCED FILE PREVIEW ===== */
.file-preview-advanced {
    width: 80px;
    height: 100px;
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.file-preview-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: var(--error-color);
}

.file-preview-advanced .preview-icon {
    font-size: var(--font-size-xl);
    color: var(--error-color);
    margin-bottom: var(--spacing-xs);
}

.file-preview-advanced .preview-text {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: 600;
}

/* ===== DRAG INDICATORS ===== */
.drag-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(99, 102, 241, 0.9);
    color: var(--white);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.drag-indicator.active {
    opacity: 1;
    visibility: visible;
}

/* ===== LOADING STATES ===== */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-dropdown);
    border-radius: inherit;
}

.loading-spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== SUCCESS STATES ===== */
.success-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(16, 185, 129, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
    border-radius: inherit;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.success-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== ADVANCED BUTTONS ===== */
.btn-floating {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.btn-floating:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.btn-floating.hidden {
    transform: translateY(100px);
    opacity: 0;
}

/* ===== TOOLTIPS ===== */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: var(--z-tooltip);
    margin-bottom: 5px;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-800);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ===== CONTEXT MENU ===== */
.context-menu {
    position: fixed;
    background: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-sm) 0;
    min-width: 150px;
    z-index: var(--z-popover);
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: all var(--transition-fast);
}

.context-menu.active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    cursor: pointer;
    transition: background var(--transition-fast);
    color: var(--gray-700);
}

.context-menu-item:hover {
    background: var(--gray-100);
}

.context-menu-item i {
    width: 16px;
    text-align: center;
}

.context-menu-separator {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-sm) 0;
}

/* ===== KEYBOARD SHORTCUTS INDICATOR ===== */
.keyboard-shortcut {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: var(--z-tooltip);
}

.keyboard-shortcut.active {
    opacity: 1;
    visibility: visible;
}

.keyboard-shortcut kbd {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    margin: 0 2px;
}
