/* ===== ADVANCED HEALTH-THEMED ANIMATIONS ===== */

/* Background Gradient Animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Heartbeat Animation */
@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.05);
    }
}

/* Spin Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Text Glow Animation */
@keyframes textGlow {
    0%, 100% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.2);
    }
}

/* Floating Particles */
@keyframes floatUp {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

@keyframes floatSide {
    0%, 100% {
        transform: translateX(0px) translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateX(20px) translateY(-20px) rotate(90deg);
        opacity: 1;
    }
    50% {
        transform: translateX(0px) translateY(-40px) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateX(-20px) translateY(-20px) rotate(270deg);
        opacity: 1;
    }
}

/* Health Icons Animation */
@keyframes healthIconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.7;
    }
}

/* Pulse Rings Animation */
@keyframes pulseRing {
    0% {
        transform: scale(0.1);
        opacity: 1;
    }
    80%, 100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

/* Progress Ring Animation */
@keyframes progressFill {
    0% {
        stroke-dasharray: 0 100;
    }
    100% {
        stroke-dasharray: var(--progress) 100;
    }
}

/* Bounce In Animation */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(50px);
    }
    50% {
        opacity: 1;
        transform: scale(1.05) translateY(-10px);
    }
    70% {
        transform: scale(0.9) translateY(0px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0px);
    }
}

/* Slide In Animations */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade In Animation */
@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* Scale In Animation */
@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== FLOATING PARTICLES ===== */
.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.6) 0%, rgba(16, 185, 129, 0.1) 70%);
    border-radius: 50%;
    animation: floatUp 15s linear infinite;
}

.particle:nth-child(1) {
    width: 20px;
    height: 20px;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 12s;
}

.particle:nth-child(2) {
    width: 15px;
    height: 15px;
    left: 30%;
    animation-delay: 3s;
    animation-duration: 18s;
}

.particle:nth-child(3) {
    width: 25px;
    height: 25px;
    left: 60%;
    animation-delay: 6s;
    animation-duration: 14s;
}

.particle:nth-child(4) {
    width: 18px;
    height: 18px;
    left: 80%;
    animation-delay: 9s;
    animation-duration: 16s;
}

.particle:nth-child(5) {
    width: 22px;
    height: 22px;
    left: 45%;
    animation-delay: 12s;
    animation-duration: 20s;
}

.particle:nth-child(6) {
    width: 16px;
    height: 16px;
    left: 75%;
    animation-delay: 15s;
    animation-duration: 13s;
}

/* ===== HEALTH ICONS ===== */
.health-icons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.health-icon {
    position: absolute;
    font-size: 2rem;
    color: rgba(16, 185, 129, 0.2);
    animation: healthIconFloat 8s ease-in-out infinite;
}

.health-icon:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
    color: rgba(239, 68, 68, 0.3);
}

.health-icon:nth-child(2) {
    top: 60%;
    left: 85%;
    animation-delay: 2s;
    color: rgba(16, 185, 129, 0.3);
}

.health-icon:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    color: rgba(99, 102, 241, 0.3);
}

.health-icon:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 6s;
    color: rgba(245, 158, 11, 0.3);
}

.health-icon:nth-child(5) {
    top: 50%;
    left: 50%;
    animation-delay: 8s;
    color: rgba(16, 185, 129, 0.3);
}

/* ===== PULSE RINGS ===== */
.pulse-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.pulse-ring {
    position: absolute;
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 50%;
    animation: pulseRing 4s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.pulse-ring:nth-child(1) {
    width: 200px;
    height: 200px;
    margin: -100px 0 0 -100px;
    animation-delay: 0s;
}

.pulse-ring:nth-child(2) {
    width: 400px;
    height: 400px;
    margin: -200px 0 0 -200px;
    animation-delay: 1s;
}

.pulse-ring:nth-child(3) {
    width: 600px;
    height: 600px;
    margin: -300px 0 0 -300px;
    animation-delay: 2s;
}

/* ===== ANIMATION CLASSES ===== */
.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out;
}

/* ===== HOVER EFFECTS ===== */
.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.4);
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.scroll-reveal-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .particle,
    .health-icon,
    .pulse-ring {
        animation: none !important;
    }
    
    .bg-gradient {
        animation: none !important;
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    }
}

/* ===== WAVE ANIMATION FOR TEXT ===== */
.wave-char {
    display: inline-block;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 40%, 100% {
        transform: translateY(0);
    }
    20% {
        transform: translateY(-10px);
    }
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes loadingPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '...';
    animation: loadingDots 1.4s ease-in-out infinite both;
}

/* ===== FEATURE CARD ANIMATIONS ===== */
@keyframes featureCardHover {
    0% {
        transform: translateY(0) rotateY(0deg);
    }
    100% {
        transform: translateY(-10px) rotateY(5deg);
    }
}

.feature-card {
    transform-style: preserve-3d;
    perspective: 1000px;
}

/* ===== CTA SECTION ANIMATIONS ===== */
@keyframes ctaGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
    }
}

.cta-btn {
    animation: ctaGlow 3s ease-in-out infinite;
}

/* ===== NOTIFICATION ANIMATIONS ===== */
@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notificationSlideOut {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.show {
    animation: notificationSlideIn 0.3s ease-out;
}

.notification.hide {
    animation: notificationSlideOut 0.3s ease-in;
}

/* ===== FOOTER ANIMATIONS ===== */
@keyframes socialBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.social-link:hover {
    animation: socialBounce 0.6s ease;
}

/* ===== ADVANCED PARTICLE SYSTEM ===== */
@keyframes particleOrbit {
    0% {
        transform: rotate(0deg) translateX(100px) rotate(0deg);
    }
    100% {
        transform: rotate(360deg) translateX(100px) rotate(-360deg);
    }
}

.dynamic-particle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.8) 0%, rgba(16, 185, 129, 0.1) 70%);
}

.dynamic-particle:nth-child(odd) {
    animation: particleOrbit 20s linear infinite;
}

.dynamic-particle:nth-child(even) {
    animation: floatUp 15s linear infinite;
}

/* ===== HEALTH ICON SPECIAL EFFECTS ===== */
@keyframes heartPulse {
    0%, 100% {
        transform: scale(1);
        filter: hue-rotate(0deg);
    }
    50% {
        transform: scale(1.2);
        filter: hue-rotate(180deg);
    }
}

.health-icon:first-child {
    animation: heartPulse 2s ease-in-out infinite;
}

/* ===== PROGRESS RING ADVANCED ANIMATION ===== */
@keyframes progressGlow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(16, 185, 129, 0.5));
    }
    50% {
        filter: drop-shadow(0 0 15px rgba(16, 185, 129, 0.8));
    }
}

.progress-ring {
    animation: progressGlow 2s ease-in-out infinite;
}

/* ===== PHONE MOCKUP ADVANCED ANIMATION ===== */
@keyframes phoneScreenGlow {
    0%, 100% {
        box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow: inset 0 0 40px rgba(255, 255, 255, 0.3);
    }
}

.phone-screen {
    animation: phoneScreenGlow 4s ease-in-out infinite;
}

/* ===== HABIT CARD ANIMATIONS ===== */
@keyframes habitCardPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

.habit-card:hover {
    animation: habitCardPulse 1s ease-in-out;
}

/* ===== SECTION REVEAL ANIMATIONS ===== */
@keyframes sectionReveal {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-reveal {
    animation: sectionReveal 0.8s ease-out;
}

/* ===== BUTTON RIPPLE EFFECT ===== */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.floating-particles,
.health-icons,
.pulse-rings,
.dynamic-particle {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* ===== ACCESSIBILITY ANIMATIONS ===== */
@media (prefers-reduced-motion: reduce) {
    .wave-char,
    .dynamic-particle,
    .health-icon,
    .progress-ring,
    .phone-screen,
    .habit-card,
    .cta-btn {
        animation: none !important;
    }

    .feature-card:hover,
    .btn:hover,
    .social-link:hover {
        transform: none !important;
    }
}
