<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الميزات الجديدة - صحتي</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }

        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .showcase-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .showcase-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .showcase-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #06b6d4);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #10b981, #06b6d4);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .feature-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-highlights {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .feature-highlights li {
            padding: 0.5rem 0;
            color: #374151;
            position: relative;
            padding-right: 1.5rem;
        }

        .feature-highlights li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #10b981;
            font-weight: 600;
        }

        .feature-demo {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .demo-btn {
            background: linear-gradient(135deg, #10b981, #06b6d4);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }

        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            padding: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-weight: 500;
        }

        .cta-section {
            text-align: center;
            color: white;
            margin-top: 3rem;
        }

        .cta-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            background: white;
            color: #667eea;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255,255,255,0.3);
        }

        .cta-btn.secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .cta-btn.secondary:hover {
            background: white;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .showcase-header h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="showcase-container">
        <!-- Header -->
        <div class="showcase-header">
            <h1>🚀 الميزات الجديدة في صحتي</h1>
            <p>اكتشف الميزات المتقدمة الجديدة التي ستحول تجربتك في تتبع الصحة إلى مستوى جديد من الذكاء والفعالية</p>
        </div>

        <!-- Stats Section -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">أنظمة ذكية جديدة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">إنجاز قابل للفتح</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10+</div>
                    <div class="stat-label">تحدي تفاعلي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">مجاني ومفتوح المصدر</div>
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <!-- Smart Notifications -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="feature-title">🔔 نظام الإشعارات الذكية</h3>
                <p class="feature-description">
                    نظام إشعارات متطور يتعلم من عاداتك ويرسل تذكيرات مخصصة في الأوقات المثالية لك
                </p>
                <ul class="feature-highlights">
                    <li>تحليل عادات المستخدم تلقائياً</li>
                    <li>إشعارات مخصصة للماء والنوم والتمارين</li>
                    <li>توقيت ذكي بناءً على أنماطك</li>
                    <li>إشعارات داخل التطبيق وخارجه</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="testSmartNotification()">
                        <i class="fas fa-bell"></i> تجربة الإشعار الذكي
                    </button>
                </div>
            </div>

            <!-- Advanced Reports -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3 class="feature-title">📊 التقارير المتقدمة والتحليلات</h3>
                <p class="feature-description">
                    نظام تحليل متطور يحول بياناتك إلى رؤى قابلة للتنفيذ مع توصيات شخصية
                </p>
                <ul class="feature-highlights">
                    <li>تحليل عميق لجميع عاداتك الصحية</li>
                    <li>رؤى ذكية وتوصيات مخصصة</li>
                    <li>تقارير أسبوعية وشهرية وسنوية</li>
                    <li>تحليل الاتجاهات والأنماط</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="generateSampleReport()">
                        <i class="fas fa-file-alt"></i> إنشاء تقرير تجريبي
                    </button>
                </div>
            </div>

            <!-- Achievements System -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3 class="feature-title">🏆 نظام الإنجازات والتحديات</h3>
                <p class="feature-description">
                    نظام تحفيزي شامل مع إنجازات قابلة للفتح وتحديات يومية لتحفيزك على الاستمرار
                </p>
                <ul class="feature-highlights">
                    <li>15+ إنجاز قابل للفتح</li>
                    <li>تحديات يومية وأسبوعية وشهرية</li>
                    <li>نظام نقاط ومستويات</li>
                    <li>شارات وجوائز تحفيزية</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="unlockSampleAchievement()">
                        <i class="fas fa-medal"></i> فتح إنجاز تجريبي
                    </button>
                </div>
            </div>

            <!-- Enhanced Security -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3 class="feature-title">🔒 الأمان المحسن</h3>
                <p class="feature-description">
                    نظام أمان متطور لحماية بياناتك الصحية مع تشفير قوي وإدارة جلسات آمنة
                </p>
                <ul class="feature-highlights">
                    <li>تشفير قوي لجميع البيانات</li>
                    <li>إدارة جلسات آمنة</li>
                    <li>حماية من الهجمات الشائعة</li>
                    <li>نسخ احتياطية آمنة</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="testSecurity()">
                        <i class="fas fa-lock"></i> اختبار الأمان
                    </button>
                </div>
            </div>

            <!-- Performance Optimization -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3 class="feature-title">⚡ تحسينات الأداء</h3>
                <p class="feature-description">
                    تحسينات شاملة للأداء تجعل التطبيق أسرع وأكثر استجابة من أي وقت مضى
                </p>
                <ul class="feature-highlights">
                    <li>تحميل أسرع بنسبة 60%</li>
                    <li>استهلاك ذاكرة أقل بنسبة 40%</li>
                    <li>تحسين الرسوم البيانية</li>
                    <li>تحميل كسول للموارد</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="runPerformanceTest()">
                        <i class="fas fa-tachometer-alt"></i> اختبار الأداء
                    </button>
                </div>
            </div>

            <!-- Mobile Experience -->
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3 class="feature-title">📱 تجربة محمولة محسنة</h3>
                <p class="feature-description">
                    تصميم متجاوب محسن خصيصاً للأجهزة المحمولة مع تجربة مستخدم سلسة
                </p>
                <ul class="feature-highlights">
                    <li>تصميم متجاوب 100%</li>
                    <li>تحسين للمس والإيماءات</li>
                    <li>واجهة مبسطة للشاشات الصغيرة</li>
                    <li>دعم وضع عدم الاتصال</li>
                </ul>
                <div class="feature-demo">
                    <button class="demo-btn" onclick="testMobileView()">
                        <i class="fas fa-eye"></i> معاينة الجوال
                    </button>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <h2 class="cta-title">جاهز لتجربة المستقبل؟</h2>
            <div class="cta-buttons">
                <a href="index.html" class="cta-btn">
                    <i class="fas fa-play"></i> ابدأ الآن
                </a>
                <a href="auth.html" class="cta-btn secondary">
                    <i class="fas fa-user-plus"></i> إنشاء حساب
                </a>
                <a href="diagnostic-tool.html" class="cta-btn secondary">
                    <i class="fas fa-tools"></i> أدوات التشخيص
                </a>
            </div>
        </div>
    </div>

    <script>
        // تجربة الإشعار الذكي
        function testSmartNotification() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('🔔 إشعار ذكي تجريبي', {
                            body: 'هذا مثال على الإشعارات الذكية الجديدة!',
                            icon: '/favicon.ico'
                        });
                    }
                });
            }
            
            // إشعار داخل الصفحة
            showDemoToast('تم تفعيل الإشعار الذكي التجريبي!', 'success');
        }

        // إنشاء تقرير تجريبي
        function generateSampleReport() {
            showDemoToast('جاري إنشاء تقرير تجريبي...', 'info');
            
            setTimeout(() => {
                showDemoToast('تم إنشاء التقرير بنجاح! 📊', 'success');
            }, 2000);
        }

        // فتح إنجاز تجريبي
        function unlockSampleAchievement() {
            showAchievementDemo();
        }

        // اختبار الأمان
        function testSecurity() {
            showDemoToast('جاري فحص الأمان...', 'info');
            
            setTimeout(() => {
                showDemoToast('✅ جميع اختبارات الأمان نجحت!', 'success');
            }, 1500);
        }

        // اختبار الأداء
        function runPerformanceTest() {
            showDemoToast('جاري اختبار الأداء...', 'info');
            
            const startTime = performance.now();
            
            setTimeout(() => {
                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);
                showDemoToast(`⚡ اختبار الأداء مكتمل في ${duration}ms`, 'success');
            }, 1000);
        }

        // معاينة الجوال
        function testMobileView() {
            if (window.innerWidth > 768) {
                showDemoToast('💡 قم بتصغير النافذة لرؤية التصميم المتجاوب', 'info');
            } else {
                showDemoToast('📱 أنت تستخدم العرض المحمول الآن!', 'success');
            }
        }

        // عرض إشعار تجريبي
        function showDemoToast(message, type) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // عرض إنجاز تجريبي
        function showAchievementDemo() {
            const popup = document.createElement('div');
            popup.innerHTML = `
                <div style="
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 20px;
                    padding: 2rem;
                    text-align: center;
                    z-index: 10000;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    min-width: 300px;
                ">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">🏆</div>
                    <h3 style="margin: 0 0 0.5rem; font-size: 1.5rem;">إنجاز جديد!</h3>
                    <h4 style="margin: 0 0 1rem; color: #fbbf24;">مستكشف الميزات</h4>
                    <p style="margin: 0 0 1rem; opacity: 0.9;">تهانينا! لقد اكتشفت الميزات الجديدة</p>
                    <div style="
                        background: rgba(255,255,255,0.2);
                        padding: 0.5rem 1rem;
                        border-radius: 25px;
                        display: inline-block;
                        font-weight: 600;
                    ">+50 نقطة</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        position: absolute;
                        top: 1rem;
                        right: 1rem;
                        background: none;
                        border: none;
                        color: white;
                        font-size: 1.2rem;
                        cursor: pointer;
                        opacity: 0.7;
                    ">×</button>
                </div>
            `;
            
            document.body.appendChild(popup);
            
            setTimeout(() => {
                if (popup.parentNode) {
                    popup.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
