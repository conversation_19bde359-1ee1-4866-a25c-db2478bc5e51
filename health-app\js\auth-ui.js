/**
 * Health App - Authentication UI Management
 * إدارة واجهة المصادقة
 */

class AuthUI {
    constructor() {
        this.auth = null;
        this.currentForm = 'login';
        
        this.init();
    }

    /**
     * تهيئة واجهة المصادقة
     */
    init() {
        console.log('🎨 تهيئة واجهة المصادقة');
        
        // تهيئة نظام المصادقة
        this.auth = new HealthAuth();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
        
        // إعداد التحقق من صحة النماذج
        this.setupFormValidation();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تبديل النماذج
        document.getElementById('showRegisterForm')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showForm('register');
        });

        document.getElementById('showLoginForm')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showForm('login');
        });

        document.getElementById('forgotPasswordLink')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showForm('forgot');
        });

        document.getElementById('backToLogin')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.showForm('login');
        });

        // إرسال النماذج
        document.getElementById('loginFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin(e);
        });

        document.getElementById('registerFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister(e);
        });

        document.getElementById('forgotFormElement')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleForgotPassword(e);
        });

        // تبديل إظهار كلمة المرور
        document.querySelectorAll('.toggle-password').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.togglePasswordVisibility(e.target);
            });
        });

        // التحقق الفوري من صحة البيانات
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }

    /**
     * عرض نموذج معين
     */
    showForm(formType) {
        // إخفاء جميع النماذج
        document.querySelectorAll('.auth-form').forEach(form => {
            form.classList.remove('active');
        });

        // عرض النموذج المطلوب
        const targetForm = document.getElementById(`${formType}Form`);
        if (targetForm) {
            targetForm.classList.add('active');
            this.currentForm = formType;
            
            // تركيز على أول حقل
            const firstInput = targetForm.querySelector('input');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 300);
            }
        }
    }

    /**
     * معالجة تسجيل الدخول
     */
    async handleLogin(event) {
        const formData = new FormData(event.target);
        const email = formData.get('email');
        const password = formData.get('password');
        const rememberMe = formData.get('rememberMe') === 'on';

        // عرض شاشة التحميل
        this.showLoading('جاري تسجيل الدخول...');

        try {
            const result = await this.auth.login(email, password, rememberMe);
            
            if (result.success) {
                this.showToast('تم تسجيل الدخول بنجاح!', 'success');
                
                // الانتقال للتطبيق الرئيسي
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
                
            } else {
                this.showToast(result.error, 'error');
            }
            
        } catch (error) {
            this.showToast('حدث خطأ غير متوقع', 'error');
            console.error('خطأ في تسجيل الدخول:', error);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * معالجة التسجيل
     */
    async handleRegister(event) {
        const formData = new FormData(event.target);
        
        // جمع البيانات
        const userData = {
            name: formData.get('name'),
            email: formData.get('email'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            age: formData.get('age') ? parseInt(formData.get('age')) : null,
            gender: formData.get('gender'),
            weight: formData.get('weight') ? parseFloat(formData.get('weight')) : null,
            height: formData.get('height') ? parseFloat(formData.get('height')) : null,
            activityLevel: formData.get('activityLevel'),
            sleepGoal: formData.get('sleepGoal') ? parseFloat(formData.get('sleepGoal')) : 8,
            waterGoal: formData.get('waterGoal') ? parseFloat(formData.get('waterGoal')) : 2.5,
            exerciseGoal: formData.get('exerciseGoal') ? parseInt(formData.get('exerciseGoal')) : 60,
            calorieGoal: formData.get('calorieGoal') ? parseInt(formData.get('calorieGoal')) : 2000
        };

        // التحقق من الموافقة على الشروط
        if (!formData.get('agreeTerms')) {
            this.showToast('يجب الموافقة على الشروط والأحكام', 'error');
            return;
        }

        // عرض شاشة التحميل
        this.showLoading('جاري إنشاء الحساب...');

        try {
            const result = await this.auth.register(userData);
            
            if (result.success) {
                this.showToast('تم إنشاء الحساب بنجاح!', 'success');
                
                // الانتقال للتطبيق الرئيسي
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
                
            } else {
                this.showToast(result.error, 'error');
            }
            
        } catch (error) {
            this.showToast('حدث خطأ غير متوقع', 'error');
            console.error('خطأ في التسجيل:', error);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * معالجة نسيان كلمة المرور
     */
    async handleForgotPassword(event) {
        const formData = new FormData(event.target);
        const email = formData.get('email');

        // عرض شاشة التحميل
        this.showLoading('جاري إرسال رابط الاستعادة...');

        try {
            // محاكاة إرسال البريد الإلكتروني
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.showToast('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
            this.showForm('login');
            
        } catch (error) {
            this.showToast('حدث خطأ في إرسال البريد الإلكتروني', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * تبديل إظهار كلمة المرور
     */
    togglePasswordVisibility(button) {
        const targetId = button.getAttribute('data-target');
        const input = document.getElementById(targetId);
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    /**
     * إعداد التحقق من صحة النماذج
     */
    setupFormValidation() {
        // التحقق من تطابق كلمات المرور
        const passwordField = document.getElementById('registerPassword');
        const confirmField = document.getElementById('confirmPassword');

        if (passwordField && confirmField) {
            [passwordField, confirmField].forEach(field => {
                field.addEventListener('input', () => {
                    if (confirmField.value && passwordField.value !== confirmField.value) {
                        this.showFieldError(confirmField, 'كلمات المرور غير متطابقة');
                    } else {
                        this.clearFieldError(confirmField);
                    }
                });
            });
        }
    }

    /**
     * التحقق من صحة حقل
     */
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const name = field.name;

        // مسح الأخطاء السابقة
        this.clearFieldError(field);

        // التحقق حسب نوع الحقل
        if (field.required && !value) {
            this.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }

        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'البريد الإلكتروني غير صحيح');
                return false;
            }
        }

        if (type === 'password' && value) {
            if (value.length < 6) {
                this.showFieldError(field, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        }

        if (name === 'name' && value) {
            if (value.length < 2) {
                this.showFieldError(field, 'الاسم يجب أن يكون حرفين على الأقل');
                return false;
            }
        }

        // إظهار علامة النجاح
        this.showFieldSuccess(field);
        return true;
    }

    /**
     * عرض خطأ في الحقل
     */
    showFieldError(field, message) {
        const inputGroup = field.closest('.input-group');
        inputGroup.classList.add('error');
        inputGroup.classList.remove('success');

        // إزالة رسالة الخطأ السابقة
        const existingError = inputGroup.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // إضافة رسالة خطأ جديدة
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        inputGroup.parentNode.appendChild(errorDiv);
    }

    /**
     * عرض نجاح في الحقل
     */
    showFieldSuccess(field) {
        const inputGroup = field.closest('.input-group');
        inputGroup.classList.add('success');
        inputGroup.classList.remove('error');
    }

    /**
     * مسح خطأ الحقل
     */
    clearFieldError(field) {
        const inputGroup = field.closest('.input-group');
        inputGroup.classList.remove('error', 'success');

        // إزالة رسالة الخطأ
        const errorMessage = inputGroup.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    /**
     * عرض شاشة التحميل
     */
    showLoading(text = 'جاري المعالجة...') {
        const overlay = document.getElementById('loadingOverlay');
        const loadingText = document.getElementById('loadingText');
        
        if (overlay && loadingText) {
            loadingText.textContent = text;
            overlay.classList.add('active');
        }
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * عرض إشعار Toast
     */
    showToast(message, type = 'info') {
        // إنشاء عنصر Toast
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-content">
                <i class="toast-icon ${iconMap[type]}"></i>
                <span class="toast-message">${message}</span>
                <button class="toast-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // إضافة إلى الصفحة
        document.body.appendChild(toast);

        // إظهار Toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // إعداد زر الإغلاق
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.hideToast(toast);
        });

        // إخفاء تلقائي بعد 5 ثوان
        setTimeout(() => {
            this.hideToast(toast);
        }, 5000);
    }

    /**
     * إخفاء إشعار Toast
     */
    hideToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// تهيئة واجهة المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new AuthUI();
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.AuthUI = AuthUI;
}
