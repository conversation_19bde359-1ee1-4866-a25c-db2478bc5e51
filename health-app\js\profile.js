/**
 * Health App - Profile Manager
 * مدير الملف الشخصي
 */

class ProfileManager {
    constructor(app) {
        this.app = app;
        this.userProfile = {};
        
        this.init();
    }

    /**
     * تهيئة مدير الملف الشخصي
     */
    init() {
        console.log('👤 تهيئة مدير الملف الشخصي');
        this.loadProfileData();
        this.setupProfilePage();
    }

    /**
     * تحميل بيانات الملف الشخصي
     */
    loadProfileData() {
        const data = this.app.storage.getProfileData();
        this.userProfile = data.profile || {
            name: 'مستخدم جديد',
            age: null,
            gender: null,
            height: null,
            weight: null,
            activityLevel: 'moderate'
        };
    }

    /**
     * إعداد صفحة الملف الشخصي
     */
    setupProfilePage() {
        const profilePage = document.getElementById('profilePage');
        if (!profilePage) return;

        profilePage.innerHTML = `
            <div class="profile-container">
                <div class="page-header">
                    <h2>الملف الشخصي</h2>
                    <p>معلوماتك الشخصية وإعداداتك</p>
                </div>
                
                <div class="coming-soon">
                    <i class="fas fa-user"></i>
                    <h3>قريباً</h3>
                    <p>صفحة الملف الشخصي قيد التطوير</p>
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>معلومات شخصية مفصلة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>حساب مؤشر كتلة الجسم</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>أهداف مخصصة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>تتبع التقدم الشخصي</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حفظ بيانات الملف الشخصي
     */
    saveProfileData() {
        this.app.storage.saveProfileData({
            profile: this.userProfile,
            lastUpdated: new Date().toISOString()
        });
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ProfileManager = ProfileManager;
}
