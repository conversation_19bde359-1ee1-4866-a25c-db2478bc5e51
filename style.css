/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2rem;
    color: #ffd700;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #ffd700;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 80px;
}

.hero-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.highlight {
    color: #ffd700;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #ffd700;
    color: #333;
}

.btn-primary:hover {
    background: #ffed4e;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
}

.hero-image {
    display: flex;
    justify-content: center;
}

.hero-card {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.hero-card i {
    font-size: 3rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

/* Table of Contents */
.toc-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.section-title i {
    color: #667eea;
    margin-left: 1rem;
}

.toc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.toc-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toc-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.toc-item i {
    font-size: 1.5rem;
    color: #667eea;
    min-width: 30px;
}

/* Content Sections */
.content-section {
    padding: 80px 0;
}

.bg-light {
    background: #f8f9fa;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.content-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.highlight-box {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    padding: 1.5rem;
    border-radius: 10px;
    margin: 2rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.highlight-box i {
    color: #e74c3c;
    font-size: 1.5rem;
    margin-top: 0.2rem;
}

.benefits-list {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.benefits-list h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.benefits-list ul {
    list-style: none;
}

.benefits-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.benefits-list li i {
    color: #27ae60;
    font-size: 1.2rem;
}

/* About Section */
.intro-text {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #666;
    line-height: 1.8;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Features Section */
.features-container {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.feature-item {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: start;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-content h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.feature-content p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.feature-content ul {
    list-style: none;
}

.feature-content li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-right: 1.5rem;
}

.feature-content li:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    right: 0;
}

/* Download Section */
.download-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
}

.download-section .section-title {
    color: white;
}

.download-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.step {
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.step-number {
    width: 50px;
    height: 50px;
    background: #ffd700;
    color: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.step h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.step ul {
    list-style: none;
}

.step li {
    padding: 0.5rem 0;
    position: relative;
    padding-right: 1.5rem;
}

.step li:before {
    content: "✓";
    color: #ffd700;
    position: absolute;
    right: 0;
}

.download-button-container {
    text-align: center;
}

.btn-download {
    background: #ffd700;
    color: #333;
    font-size: 1.2rem;
    padding: 15px 30px;
    margin-bottom: 1rem;
}

.btn-download:hover {
    background: #ffed4e;
    transform: translateY(-3px);
}

.download-note {
    opacity: 0.8;
    font-size: 0.9rem;
}

.download-note i {
    margin-left: 0.5rem;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffd700;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #ffd700;
    color: #333;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .feature-item {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .toc-grid {
        grid-template-columns: 1fr;
    }

    .download-steps {
        grid-template-columns: 1fr;
    }

    .interface-item {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .interface-item:nth-child(even) {
        direction: rtl;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .tips-grid {
        grid-template-columns: 1fr;
    }

    .security-feature {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .comparison-table {
        font-size: 0.8rem;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 0.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Interface Section */
.interface-content {
    display: flex;
    flex-direction: column;
    gap: 4rem;
}

.interface-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.interface-item:nth-child(even) {
    direction: ltr;
}

.interface-item:nth-child(even) .interface-text {
    direction: rtl;
}

.interface-text h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.interface-text h3 i {
    margin-left: 0.5rem;
}

.interface-text ul {
    list-style: none;
    margin-top: 1rem;
}

.interface-text li {
    padding: 0.5rem 0;
    position: relative;
    padding-right: 1.5rem;
}

.interface-text li:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    right: 0;
}

.interface-image {
    display: flex;
    justify-content: center;
}

.mockup-dashboard,
.mockup-sales {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
}

.mockup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: center;
}

.mockup-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.mockup-stats {
    padding: 1.5rem;
    display: grid;
    gap: 1rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card i {
    color: #667eea;
    font-size: 1.5rem;
}

.stat-card span {
    color: #666;
    font-size: 0.9rem;
}

.stat-card strong {
    color: #333;
    margin-right: auto;
}

.mockup-content {
    padding: 1.5rem;
}

.product-list {
    margin-bottom: 1rem;
}

.product-item {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.cart-total {
    background: #667eea;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
}

/* Examples Section */
.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.example-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.example-card:hover {
    transform: translateY(-10px);
}

.example-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.example-card h3 {
    text-align: center;
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.example-details p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.example-details ul {
    list-style: none;
    margin-bottom: 1.5rem;
}

.example-details li {
    padding: 0.5rem 0;
    position: relative;
    padding-right: 1.5rem;
}

.example-details li:before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    position: absolute;
    right: 0;
}

.result {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.result i {
    font-size: 1.5rem;
    color: #ffd700;
}

/* Comparison Section */
.comparison-table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.comparison-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
}

.comparison-table .highlight-col {
    background: rgba(102, 126, 234, 0.1);
    font-weight: 600;
}

.comparison-table .success {
    color: #27ae60;
}

.comparison-table .error {
    color: #e74c3c;
}

.comparison-table .warning {
    color: #f39c12;
}

.comparison-summary {
    display: flex;
    justify-content: center;
}

.summary-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    max-width: 600px;
}

.summary-card h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    text-align: center;
}

.summary-card ul {
    list-style: none;
}

.summary-card li {
    padding: 0.8rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-right: 1.5rem;
}

.summary-card li:last-child {
    border-bottom: none;
}

.summary-card li:before {
    content: "★";
    color: #ffd700;
    position: absolute;
    right: 0;
}

/* Tips Section */
.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tip-category {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.tip-category h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tip-category ul {
    list-style: none;
}

.tip-category li {
    padding: 0.8rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-right: 1.5rem;
}

.tip-category li:last-child {
    border-bottom: none;
}

.tip-category li:before {
    content: "💡";
    position: absolute;
    right: 0;
}

/* Security Section */
.security-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.security-feature {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: start;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.security-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.security-text h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.security-text p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.security-text ul {
    list-style: none;
}

.security-text li {
    padding: 0.5rem 0;
    position: relative;
    padding-right: 1.5rem;
}

.security-text li:before {
    content: "🔒";
    position: absolute;
    right: 0;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
