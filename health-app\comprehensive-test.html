<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - صحتي</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #10b981;
        }

        .test-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .test-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-name {
            font-weight: 500;
            color: #374151;
        }

        .test-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .test-status.success {
            background: #d1fae5;
            color: #065f46;
        }

        .test-status.error {
            background: #fee2e2;
            color: #991b1b;
        }

        .test-status.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .test-status.pending {
            background: #e0e7ff;
            color: #3730a3;
        }

        .test-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .test-btn.danger {
            background: #ef4444;
        }

        .test-btn.danger:hover {
            background: #dc2626;
        }

        .test-btn.secondary {
            background: #6b7280;
        }

        .test-btn.secondary:hover {
            background: #4b5563;
        }

        .test-results {
            margin-top: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-log {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-number.success {
            color: #10b981;
        }

        .stat-number.error {
            color: #ef4444;
        }

        .stat-number.warning {
            color: #f59e0b;
        }

        .stat-number.info {
            color: #3b82f6;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #06b6d4);
            transition: width 0.3s ease;
            width: 0%;
        }

        @media (max-width: 768px) {
            .test-container {
                padding: 1rem;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار شامل للتطبيق</h1>
            <p>فحص شامل لجميع مكونات ووظائف تطبيق صحتي</p>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="summary-stats">
            <div class="stat-card">
                <div class="stat-number success" id="passedCount">0</div>
                <div class="stat-label">اختبارات نجحت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number error" id="failedCount">0</div>
                <div class="stat-label">اختبارات فشلت</div>
            </div>
            <div class="stat-card">
                <div class="stat-number warning" id="warningCount">0</div>
                <div class="stat-label">تحذيرات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number info" id="totalCount">0</div>
                <div class="stat-label">إجمالي الاختبارات</div>
            </div>
        </div>

        <!-- شريط التقدم -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- أزرار التحكم -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <button class="test-btn" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
            <button class="test-btn secondary" onclick="clearResults()">
                <i class="fas fa-trash"></i> مسح النتائج
            </button>
            <button class="test-btn danger" onclick="resetApp()">
                <i class="fas fa-redo"></i> إعادة تعيين التطبيق
            </button>
        </div>

        <!-- اختبار المكونات الأساسية -->
        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> المكونات الأساسية</h3>
            <div class="test-grid" id="coreComponentsGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testCoreComponents()">
                <i class="fas fa-check"></i> اختبار المكونات الأساسية
            </button>
        </div>

        <!-- اختبار التخزين -->
        <div class="test-section">
            <h3><i class="fas fa-database"></i> نظام التخزين</h3>
            <div class="test-grid" id="storageTestsGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testStorage()">
                <i class="fas fa-check"></i> اختبار التخزين
            </button>
        </div>

        <!-- اختبار المصادقة -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt"></i> نظام المصادقة</h3>
            <div class="test-grid" id="authTestsGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testAuthentication()">
                <i class="fas fa-check"></i> اختبار المصادقة
            </button>
        </div>

        <!-- اختبار الواجهة -->
        <div class="test-section">
            <h3><i class="fas fa-desktop"></i> واجهة المستخدم</h3>
            <div class="test-grid" id="uiTestsGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testUserInterface()">
                <i class="fas fa-check"></i> اختبار الواجهة
            </button>
        </div>

        <!-- اختبار الميزات المتقدمة -->
        <div class="test-section">
            <h3><i class="fas fa-rocket"></i> الميزات المتقدمة</h3>
            <div class="test-grid" id="advancedFeaturesGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testAdvancedFeatures()">
                <i class="fas fa-check"></i> اختبار الميزات المتقدمة
            </button>
        </div>

        <!-- اختبار الأداء -->
        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> اختبار الأداء</h3>
            <div class="test-grid" id="performanceTestsGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <button class="test-btn" onclick="testPerformance()">
                <i class="fas fa-check"></i> اختبار الأداء
            </button>
        </div>

        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-list"></i> سجل الاختبارات</h3>
            <div class="test-results">
                <div class="test-log" id="testLog">جاهز لبدء الاختبارات...</div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div style="text-align: center; margin-top: 2rem;">
            <a href="index.html" class="test-btn">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <a href="diagnostic-tool.html" class="test-btn secondary">
                <i class="fas fa-tools"></i> أداة التشخيص
            </a>
            <a href="quick-fix.html" class="test-btn secondary">
                <i class="fas fa-wrench"></i> الإصلاح السريع
            </a>
        </div>
    </div>

    <script>
        // متغيرات الاختبار
        let testResults = {
            passed: 0,
            failed: 0,
            warnings: 0,
            total: 0
        };

        let testLog = [];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeTestGrids();
            updateStats();
        });

        // تهيئة شبكات الاختبار
        function initializeTestGrids() {
            // المكونات الأساسية
            const coreComponents = [
                'HealthStorage',
                'HealthAuth', 
                'HealthApp',
                'Chart.js',
                'localStorage'
            ];

            populateTestGrid('coreComponentsGrid', coreComponents);

            // اختبارات التخزين
            const storageTests = [
                'قراءة/كتابة localStorage',
                'حفظ بيانات المستخدم',
                'استرجاع البيانات',
                'إدارة الجلسات',
                'النسخ الاحتياطية'
            ];

            populateTestGrid('storageTestsGrid', storageTests);

            // اختبارات المصادقة
            const authTests = [
                'تسجيل الدخول',
                'تسجيل الخروج',
                'إدارة الجلسات',
                'حماية الصفحات',
                'قائمة المستخدم'
            ];

            populateTestGrid('authTestsGrid', authTests);

            // اختبارات الواجهة
            const uiTests = [
                'التنقل بين الصفحات',
                'الأزرار التفاعلية',
                'القوائم المنسدلة',
                'النماذج',
                'التصميم المتجاوب'
            ];

            populateTestGrid('uiTestsGrid', uiTests);

            // الميزات المتقدمة
            const advancedFeatures = [
                'الإشعارات الذكية',
                'التقارير المتقدمة',
                'نظام الإنجازات',
                'الرسوم البيانية',
                'التحليلات'
            ];

            populateTestGrid('advancedFeaturesGrid', advancedFeatures);

            // اختبارات الأداء
            const performanceTests = [
                'سرعة التحميل',
                'استهلاك الذاكرة',
                'استجابة الواجهة',
                'تحميل الموارد',
                'تحسين الصور'
            ];

            populateTestGrid('performanceTestsGrid', performanceTests);
        }

        // ملء شبكة الاختبار
        function populateTestGrid(gridId, tests) {
            const grid = document.getElementById(gridId);
            grid.innerHTML = '';

            tests.forEach(test => {
                const testItem = document.createElement('div');
                testItem.className = 'test-item';
                testItem.innerHTML = `
                    <span class="test-name">${test}</span>
                    <span class="test-status pending" id="${test.replace(/\s+/g, '_')}">في الانتظار</span>
                `;
                grid.appendChild(testItem);
            });
        }

        // تحديث حالة الاختبار
        function updateTestStatus(testName, status, message = '') {
            const statusElement = document.getElementById(testName.replace(/\s+/g, '_'));
            if (statusElement) {
                statusElement.className = `test-status ${status}`;
                statusElement.textContent = message || getStatusText(status);
            }
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusTexts = {
                success: 'نجح ✓',
                error: 'فشل ✗',
                warning: 'تحذير ⚠',
                pending: 'في الانتظار...'
            };
            return statusTexts[status] || status;
        }

        // إضافة سجل
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('testLog');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('passedCount').textContent = testResults.passed;
            document.getElementById('failedCount').textContent = testResults.failed;
            document.getElementById('warningCount').textContent = testResults.warnings;
            document.getElementById('totalCount').textContent = testResults.total;
            
            // تحديث شريط التقدم
            const progress = testResults.total > 0 ? 
                ((testResults.passed + testResults.failed + testResults.warnings) / testResults.total) * 100 : 0;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            addLog('🚀 بدء تشغيل جميع الاختبارات...');
            
            // إعادة تعيين النتائج
            testResults = { passed: 0, failed: 0, warnings: 0, total: 0 };
            
            try {
                await testCoreComponents();
                await delay(500);
                await testStorage();
                await delay(500);
                await testAuthentication();
                await delay(500);
                await testUserInterface();
                await delay(500);
                await testAdvancedFeatures();
                await delay(500);
                await testPerformance();
                
                addLog('✅ تم الانتهاء من جميع الاختبارات');
                showFinalResults();
                
            } catch (error) {
                addLog(`❌ خطأ في تشغيل الاختبارات: ${error.message}`);
            }
        }

        // تأخير
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // عرض النتائج النهائية
        function showFinalResults() {
            const successRate = testResults.total > 0 ?
                (testResults.passed / testResults.total * 100).toFixed(1) : 0;

            addLog(`📊 النتائج النهائية:`);
            addLog(`✅ نجح: ${testResults.passed}`);
            addLog(`❌ فشل: ${testResults.failed}`);
            addLog(`⚠️ تحذيرات: ${testResults.warnings}`);
            addLog(`📈 معدل النجاح: ${successRate}%`);

            if (testResults.failed === 0) {
                addLog('🎉 تهانينا! جميع الاختبارات نجحت');
            } else {
                addLog('⚠️ يوجد اختبارات فاشلة تحتاج إصلاح');
            }
        }

        // اختبار المكونات الأساسية
        async function testCoreComponents() {
            addLog('🔧 اختبار المكونات الأساسية...');

            const components = [
                { name: 'HealthStorage', check: () => typeof HealthStorage !== 'undefined' },
                { name: 'HealthAuth', check: () => typeof HealthAuth !== 'undefined' },
                { name: 'HealthApp', check: () => typeof HealthApp !== 'undefined' },
                { name: 'Chart.js', check: () => typeof Chart !== 'undefined' },
                { name: 'localStorage', check: () => typeof Storage !== 'undefined' }
            ];

            for (const component of components) {
                testResults.total++;
                try {
                    if (component.check()) {
                        updateTestStatus(component.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${component.name}: متاح`);
                    } else {
                        updateTestStatus(component.name, 'error');
                        testResults.failed++;
                        addLog(`❌ ${component.name}: غير متاح`);
                    }
                } catch (error) {
                    updateTestStatus(component.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${component.name}: خطأ في الاختبار`);
                }
                updateStats();
                await delay(200);
            }
        }

        // اختبار التخزين
        async function testStorage() {
            addLog('💾 اختبار نظام التخزين...');

            const storageTests = [
                {
                    name: 'قراءة/كتابة localStorage',
                    test: () => {
                        localStorage.setItem('test_key', 'test_value');
                        const value = localStorage.getItem('test_key');
                        localStorage.removeItem('test_key');
                        return value === 'test_value';
                    }
                },
                {
                    name: 'حفظ بيانات المستخدم',
                    test: () => {
                        if (typeof HealthStorage === 'undefined') return false;
                        try {
                            const testUser = { name: 'اختبار', email: '<EMAIL>' };
                            HealthStorage.saveUser(testUser);
                            return true;
                        } catch (error) {
                            return false;
                        }
                    }
                },
                {
                    name: 'استرجاع البيانات',
                    test: () => {
                        if (typeof HealthStorage === 'undefined') return false;
                        try {
                            const user = HealthStorage.getUser();
                            return user !== null;
                        } catch (error) {
                            return false;
                        }
                    }
                },
                {
                    name: 'إدارة الجلسات',
                    test: () => {
                        try {
                            const session = localStorage.getItem('health_app_session');
                            return session !== null || true; // يمكن أن تكون فارغة
                        } catch (error) {
                            return false;
                        }
                    }
                },
                {
                    name: 'النسخ الاحتياطية',
                    test: () => {
                        if (typeof HealthStorage === 'undefined') return false;
                        try {
                            return typeof HealthStorage.exportData === 'function';
                        } catch (error) {
                            return false;
                        }
                    }
                }
            ];

            for (const test of storageTests) {
                testResults.total++;
                try {
                    if (test.test()) {
                        updateTestStatus(test.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${test.name}: نجح`);
                    } else {
                        updateTestStatus(test.name, 'warning');
                        testResults.warnings++;
                        addLog(`⚠️ ${test.name}: تحذير`);
                    }
                } catch (error) {
                    updateTestStatus(test.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${test.name}: فشل`);
                }
                updateStats();
                await delay(200);
            }
        }

        // اختبار المصادقة
        async function testAuthentication() {
            addLog('🔐 اختبار نظام المصادقة...');

            const authTests = [
                {
                    name: 'تسجيل الدخول',
                    test: () => typeof HealthAuth !== 'undefined' && typeof HealthAuth.prototype.login === 'function'
                },
                {
                    name: 'تسجيل الخروج',
                    test: () => typeof HealthAuth !== 'undefined' && typeof HealthAuth.prototype.logout === 'function'
                },
                {
                    name: 'إدارة الجلسات',
                    test: () => typeof HealthAuth !== 'undefined' && typeof HealthAuth.prototype.isLoggedIn === 'function'
                },
                {
                    name: 'حماية الصفحات',
                    test: () => typeof AuthGuard !== 'undefined'
                },
                {
                    name: 'قائمة المستخدم',
                    test: () => document.querySelector('.user-menu') !== null
                }
            ];

            for (const test of authTests) {
                testResults.total++;
                try {
                    if (test.test()) {
                        updateTestStatus(test.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${test.name}: نجح`);
                    } else {
                        updateTestStatus(test.name, 'warning');
                        testResults.warnings++;
                        addLog(`⚠️ ${test.name}: تحذير`);
                    }
                } catch (error) {
                    updateTestStatus(test.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${test.name}: فشل`);
                }
                updateStats();
                await delay(200);
            }
        }

        // اختبار واجهة المستخدم
        async function testUserInterface() {
            addLog('🖥️ اختبار واجهة المستخدم...');

            const uiTests = [
                {
                    name: 'التنقل بين الصفحات',
                    test: () => document.querySelectorAll('.nav-link').length > 0
                },
                {
                    name: 'الأزرار التفاعلية',
                    test: () => document.querySelectorAll('button').length > 0
                },
                {
                    name: 'القوائم المنسدلة',
                    test: () => document.querySelector('.user-menu') !== null
                },
                {
                    name: 'النماذج',
                    test: () => document.querySelectorAll('form, input').length > 0
                },
                {
                    name: 'التصميم المتجاوب',
                    test: () => {
                        const viewport = document.querySelector('meta[name="viewport"]');
                        return viewport !== null;
                    }
                }
            ];

            for (const test of uiTests) {
                testResults.total++;
                try {
                    if (test.test()) {
                        updateTestStatus(test.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${test.name}: نجح`);
                    } else {
                        updateTestStatus(test.name, 'warning');
                        testResults.warnings++;
                        addLog(`⚠️ ${test.name}: تحذير`);
                    }
                } catch (error) {
                    updateTestStatus(test.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${test.name}: فشل`);
                }
                updateStats();
                await delay(200);
            }
        }

        // اختبار الميزات المتقدمة
        async function testAdvancedFeatures() {
            addLog('🚀 اختبار الميزات المتقدمة...');

            const advancedTests = [
                {
                    name: 'الإشعارات الذكية',
                    test: () => typeof SmartNotifications !== 'undefined'
                },
                {
                    name: 'التقارير المتقدمة',
                    test: () => typeof AdvancedReports !== 'undefined'
                },
                {
                    name: 'نظام الإنجازات',
                    test: () => typeof AchievementsSystem !== 'undefined'
                },
                {
                    name: 'الرسوم البيانية',
                    test: () => typeof Chart !== 'undefined'
                },
                {
                    name: 'التحليلات',
                    test: () => typeof AdvancedReports !== 'undefined'
                }
            ];

            for (const test of advancedTests) {
                testResults.total++;
                try {
                    if (test.test()) {
                        updateTestStatus(test.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${test.name}: نجح`);
                    } else {
                        updateTestStatus(test.name, 'warning');
                        testResults.warnings++;
                        addLog(`⚠️ ${test.name}: تحذير - الميزة غير محملة`);
                    }
                } catch (error) {
                    updateTestStatus(test.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${test.name}: فشل`);
                }
                updateStats();
                await delay(200);
            }
        }

        // اختبار الأداء
        async function testPerformance() {
            addLog('⚡ اختبار الأداء...');

            const performanceTests = [
                {
                    name: 'سرعة التحميل',
                    test: () => {
                        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                        addLog(`⏱️ وقت التحميل: ${loadTime}ms`);
                        return loadTime < 5000; // أقل من 5 ثوان
                    }
                },
                {
                    name: 'استهلاك الذاكرة',
                    test: () => {
                        if (performance.memory) {
                            const memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
                            addLog(`💾 استخدام الذاكرة: ${memoryUsage.toFixed(2)} MB`);
                            return memoryUsage < 100; // أقل من 100 MB
                        }
                        return true;
                    }
                },
                {
                    name: 'استجابة الواجهة',
                    test: () => {
                        const startTime = performance.now();
                        // محاكاة عملية
                        for (let i = 0; i < 1000; i++) {
                            Math.random();
                        }
                        const endTime = performance.now();
                        const responseTime = endTime - startTime;
                        addLog(`⚡ زمن الاستجابة: ${responseTime.toFixed(2)}ms`);
                        return responseTime < 100;
                    }
                },
                {
                    name: 'تحميل الموارد',
                    test: () => {
                        const resources = performance.getEntriesByType('resource');
                        addLog(`📦 عدد الموارد المحملة: ${resources.length}`);
                        return resources.length > 0;
                    }
                },
                {
                    name: 'تحسين الصور',
                    test: () => {
                        const images = document.querySelectorAll('img');
                        let optimized = 0;
                        images.forEach(img => {
                            if (img.hasAttribute('loading') || img.hasAttribute('lazy')) {
                                optimized++;
                            }
                        });
                        addLog(`🖼️ الصور المحسنة: ${optimized}/${images.length}`);
                        return true; // دائماً نجح لأن هذا اختياري
                    }
                }
            ];

            for (const test of performanceTests) {
                testResults.total++;
                try {
                    if (test.test()) {
                        updateTestStatus(test.name, 'success');
                        testResults.passed++;
                        addLog(`✅ ${test.name}: نجح`);
                    } else {
                        updateTestStatus(test.name, 'warning');
                        testResults.warnings++;
                        addLog(`⚠️ ${test.name}: يحتاج تحسين`);
                    }
                } catch (error) {
                    updateTestStatus(test.name, 'error');
                    testResults.failed++;
                    addLog(`❌ ${test.name}: فشل`);
                }
                updateStats();
                await delay(200);
            }
        }

        // مسح النتائج
        function clearResults() {
            testResults = { passed: 0, failed: 0, warnings: 0, total: 0 };
            testLog = [];
            document.getElementById('testLog').textContent = 'جاهز لبدء الاختبارات...';
            updateStats();

            // إعادة تعيين حالة جميع الاختبارات
            document.querySelectorAll('.test-status').forEach(status => {
                status.className = 'test-status pending';
                status.textContent = 'في الانتظار';
            });

            addLog('🧹 تم مسح جميع النتائج');
        }

        // إعادة تعيين التطبيق
        function resetApp() {
            if (confirm('هل أنت متأكد من إعادة تعيين التطبيق؟ سيتم مسح جميع البيانات.')) {
                localStorage.clear();
                addLog('🔄 تم إعادة تعيين التطبيق');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }
    </script>
</body>
</html>
