<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صحتي - تطبيق تتبع العادات الصحية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="./css/main.css">
    <link rel="stylesheet" href="./css/dashboard.css">
    <link rel="stylesheet" href="./css/components.css">
    
    <style>
        /* إخفاء شاشة التحميل افتراضياً */
        .loading-screen {
            display: none !important;
        }
        
        /* إظهار التطبيق افتراضياً */
        .app-container {
            display: flex !important;
            opacity: 1 !important;
        }
    </style>
</head>
<body>
    <!-- App Container -->
    <div id="app" class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-heartbeat"></i>
                    <span>صحتي</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#sleep" class="nav-link" data-page="sleep">
                            <i class="fas fa-moon"></i>
                            <span>تتبع النوم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#water" class="nav-link" data-page="water">
                            <i class="fas fa-tint"></i>
                            <span>تتبع الماء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#exercise" class="nav-link" data-page="exercise">
                            <i class="fas fa-dumbbell"></i>
                            <span>التمارين</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name">مستخدم تجريبي</span>
                        <span class="user-status">نشط</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
                </div>

                <div class="header-right">
                    <div class="header-actions">
                        <button class="header-btn" id="notificationsBtn">
                            <i class="fas fa-bell"></i>
                            <span class="badge">3</span>
                        </button>

                        <div class="date-display">
                            <span id="currentDate">الأحد، 9 يونيو 2025</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content" id="pageContent">
                <!-- Dashboard Page -->
                <div class="page dashboard-page active" id="dashboardPage">
                    <!-- Quick Stats -->
                    <div class="quick-stats">
                        <div class="stat-card sleep-stat">
                            <div class="stat-icon">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="stat-info">
                                <h3>النوم</h3>
                                <div class="stat-value">
                                    <span>7.5</span>
                                    <small>ساعة</small>
                                </div>
                                <div class="stat-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 85%"></div>
                                    </div>
                                    <span>85%</span>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card water-stat">
                            <div class="stat-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="stat-info">
                                <h3>الماء</h3>
                                <div class="stat-value">
                                    <span>1.8</span>
                                    <small>لتر</small>
                                </div>
                                <div class="stat-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 72%"></div>
                                    </div>
                                    <span>72%</span>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card exercise-stat">
                            <div class="stat-icon">
                                <i class="fas fa-dumbbell"></i>
                            </div>
                            <div class="stat-info">
                                <h3>التمارين</h3>
                                <div class="stat-value">
                                    <span>45</span>
                                    <small>دقيقة</small>
                                </div>
                                <div class="stat-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                    <span>75%</span>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card nutrition-stat">
                            <div class="stat-icon">
                                <i class="fas fa-apple-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3>السعرات</h3>
                                <div class="stat-value">
                                    <span>1650</span>
                                    <small>سعرة</small>
                                </div>
                                <div class="stat-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 83%"></div>
                                    </div>
                                    <span>83%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>إجراءات سريعة</h3>
                        <div class="actions-grid">
                            <button class="action-btn" onclick="alert('إضافة كوب ماء!')">
                                <i class="fas fa-plus"></i>
                                <span>إضافة كوب ماء</span>
                            </button>
                            <button class="action-btn" onclick="alert('تسجيل النوم!')">
                                <i class="fas fa-bed"></i>
                                <span>تسجيل النوم</span>
                            </button>
                            <button class="action-btn" onclick="alert('بدء تمرين!')">
                                <i class="fas fa-play"></i>
                                <span>بدء تمرين</span>
                            </button>
                            <button class="action-btn" onclick="alert('إضافة وجبة!')">
                                <i class="fas fa-utensils"></i>
                                <span>إضافة وجبة</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sleep Page -->
                <div class="page sleep-page" id="sleepPage">
                    <div class="page-header">
                        <h2>تتبع النوم</h2>
                        <p>راقب جودة نومك وحسن عاداتك</p>
                    </div>
                    <div class="coming-soon">
                        <i class="fas fa-moon"></i>
                        <h3>صفحة تتبع النوم</h3>
                        <p>تتبع ساعات نومك وجودة الراحة</p>
                    </div>
                </div>

                <!-- Water Page -->
                <div class="page water-page" id="waterPage">
                    <div class="page-header">
                        <h2>تتبع الماء</h2>
                        <p>حافظ على ترطيب جسمك</p>
                    </div>
                    <div class="coming-soon">
                        <i class="fas fa-tint"></i>
                        <h3>صفحة تتبع الماء</h3>
                        <p>راقب استهلاكك اليومي من الماء</p>
                    </div>
                </div>

                <!-- Exercise Page -->
                <div class="page exercise-page" id="exercisePage">
                    <div class="page-header">
                        <h2>التمارين الرياضية</h2>
                        <p>تتبع نشاطك البدني وتمارينك</p>
                    </div>
                    <div class="coming-soon">
                        <i class="fas fa-dumbbell"></i>
                        <h3>صفحة التمارين</h3>
                        <p>سجل تمارينك والسعرات المحروقة</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // JavaScript بسيط للتنقل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 تم تحميل التطبيق بنجاح!');
            
            // إعداد التنقل
            const navLinks = document.querySelectorAll('.nav-link');
            const pages = document.querySelectorAll('.page');
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetPage = this.dataset.page;
                    
                    // إزالة الفئة النشطة من جميع الروابط والصفحات
                    navLinks.forEach(l => l.classList.remove('active'));
                    pages.forEach(p => p.classList.remove('active'));
                    
                    // تفعيل الرابط والصفحة المحددة
                    this.classList.add('active');
                    document.getElementById(targetPage + 'Page').classList.add('active');
                    
                    // تحديث عنوان الصفحة
                    const titles = {
                        dashboard: 'لوحة التحكم',
                        sleep: 'تتبع النوم',
                        water: 'تتبع الماء',
                        exercise: 'التمارين الرياضية'
                    };
                    
                    document.getElementById('pageTitle').textContent = titles[targetPage] || 'صحتي';
                });
            });
            
            // تبديل الشريط الجانبي
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.getElementById('sidebar');
            
            [sidebarToggle, mobileMenuToggle].forEach(btn => {
                if (btn) {
                    btn.addEventListener('click', function() {
                        sidebar.classList.toggle('open');
                    });
                }
            });
        });
    </script>
</body>
</html>
