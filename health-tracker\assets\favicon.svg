<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="healthGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#healthGradient)" stroke="#ffffff" stroke-width="3" filter="url(#glow)"/>
  
  <!-- Heart Shape -->
  <path d="M50,75 C50,75 20,50 20,35 C20,25 30,20 40,25 C45,15 55,15 60,25 C70,20 80,25 80,35 C80,50 50,75 50,75 Z" 
        fill="url(#heartGradient)" stroke="#ffffff" stroke-width="2" filter="url(#glow)"/>
  
  <!-- Heartbeat Line -->
  <path d="M15,50 L25,50 L30,40 L35,60 L40,30 L45,70 L50,50 L55,45 L60,55 L65,50 L85,50" 
        stroke="#ffffff" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.9">
    <animate attributeName="stroke-dasharray" values="0,200;100,200;0,200" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="stroke-dashoffset" values="0;-100;-200" dur="2s" repeatCount="indefinite"/>
  </path>
  
  <!-- Health Icons -->
  <!-- Water Drop -->
  <circle cx="25" cy="25" r="4" fill="#06b6d4" opacity="0.8">
    <animate attributeName="r" values="3;5;3" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Leaf -->
  <ellipse cx="75" cy="25" rx="3" ry="6" fill="#10b981" opacity="0.8" transform="rotate(45 75 25)">
    <animateTransform attributeName="transform" type="rotate" values="45 75 25;60 75 25;45 75 25" dur="4s" repeatCount="indefinite"/>
  </ellipse>
  
  <!-- Moon -->
  <path d="M20,75 C25,70 25,80 20,75 C15,75 15,75 20,75" fill="#6366f1" opacity="0.8">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="5s" repeatCount="indefinite"/>
  </path>
  
  <!-- Apple -->
  <circle cx="80" cy="75" r="3" fill="#10b981" opacity="0.8">
    <animate attributeName="cy" values="75;73;75" dur="2s" repeatCount="indefinite"/>
  </circle>
  <path d="M80,72 Q82,70 84,72" stroke="#22c55e" stroke-width="1" fill="none" opacity="0.8"/>
  
  <!-- Plus Sign (Health Cross) -->
  <path d="M50,15 L50,25 M45,20 L55,20" stroke="#ffffff" stroke-width="3" stroke-linecap="round" opacity="0.9">
    <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
  </path>
</svg>
