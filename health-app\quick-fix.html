<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح سريع - صحتي</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            direction: rtl;
        }

        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .fix-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 3px solid #10b981;
        }

        .fix-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .fix-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }

        .fix-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            font-family: inherit;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .fix-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .fix-btn.danger {
            background: #ef4444;
        }

        .fix-btn.danger:hover {
            background: #dc2626;
        }

        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 500;
            display: none;
        }

        .result.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .result.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .result.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .status-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }

        .status-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .status-value.success {
            color: #10b981;
        }

        .status-value.error {
            color: #ef4444;
        }

        .status-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1>🔧 إصلاح سريع للمشاكل</h1>
            <p>تشخيص وإصلاح المشاكل الأساسية في التطبيق</p>
        </div>

        <!-- فحص سريع -->
        <div class="fix-section">
            <h3>📊 فحص سريع</h3>
            <button class="fix-btn" onclick="runQuickDiagnostic()">فحص المشاكل الأساسية</button>
            <div id="quickResult" class="result"></div>
        </div>

        <!-- إصلاح المشاكل -->
        <div class="fix-section">
            <h3>🛠️ إصلاح المشاكل</h3>
            <button class="fix-btn" onclick="fixJavaScriptErrors()">إصلاح أخطاء JavaScript</button>
            <button class="fix-btn" onclick="fixStorageIssues()">إصلاح مشاكل التخزين</button>
            <button class="fix-btn" onclick="fixAuthIssues()">إصلاح مشاكل المصادقة</button>
            <button class="fix-btn" onclick="fixUIIssues()">إصلاح مشاكل الواجهة</button>
            <div id="fixResult" class="result"></div>
        </div>

        <!-- إعادة تعيين -->
        <div class="fix-section">
            <h3>🔄 إعادة تعيين</h3>
            <button class="fix-btn danger" onclick="resetApplication()">إعادة تعيين التطبيق</button>
            <button class="fix-btn danger" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="resetResult" class="result"></div>
        </div>

        <!-- روابط سريعة -->
        <div class="fix-section">
            <h3>🔗 روابط سريعة</h3>
            <a href="index.html" class="fix-btn">التطبيق الرئيسي</a>
            <a href="auth.html" class="fix-btn">صفحة تسجيل الدخول</a>
            <a href="diagnostic-tool.html" class="fix-btn">أداة التشخيص الشامل</a>
            <a href="test-auth.html" class="fix-btn">اختبار المصادقة</a>
        </div>
    </div>

    <script>
        // عرض النتيجة
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // فحص سريع
        function runQuickDiagnostic() {
            const issues = [];
            const fixes = [];

            // فحص تحميل المكونات الأساسية
            if (typeof HealthStorage === 'undefined') {
                issues.push('❌ HealthStorage غير محمل');
                fixes.push('تحميل ملف storage.js');
            } else {
                issues.push('✅ HealthStorage محمل');
            }

            if (typeof HealthAuth === 'undefined') {
                issues.push('❌ HealthAuth غير محمل');
                fixes.push('تحميل ملف auth.js');
            } else {
                issues.push('✅ HealthAuth محمل');
            }

            if (typeof Chart === 'undefined') {
                issues.push('⚠️ Chart.js غير محمل');
                fixes.push('تحميل مكتبة Chart.js');
            } else {
                issues.push('✅ Chart.js محمل');
            }

            // فحص localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                issues.push('✅ localStorage يعمل');
            } catch (error) {
                issues.push('❌ localStorage لا يعمل');
                fixes.push('تفعيل localStorage في المتصفح');
            }

            // فحص CSS
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            if (stylesheets.length > 0) {
                issues.push(`✅ ${stylesheets.length} ملف CSS محمل`);
            } else {
                issues.push('❌ لا توجد ملفات CSS محملة');
                fixes.push('تحميل ملفات CSS');
            }

            let resultHtml = '<h4>نتائج الفحص:</h4>';
            resultHtml += issues.join('<br>');

            if (fixes.length > 0) {
                resultHtml += '<h4>الإصلاحات المطلوبة:</h4>';
                resultHtml += fixes.join('<br>');
            }

            const hasErrors = issues.some(issue => issue.includes('❌'));
            const type = hasErrors ? 'error' : 'success';

            showResult('quickResult', resultHtml, type);
        }

        // إصلاح أخطاء JavaScript
        function fixJavaScriptErrors() {
            let fixes = [];

            // إصلاح مشكلة القوس الإضافي في app.js (تم إصلاحها)
            fixes.push('✅ تم إصلاح القوس الإضافي في app.js');

            // إصلاح تضارب الدوال في storage.js (تم إصلاحه)
            fixes.push('✅ تم إصلاح تضارب دوال الأهداف في storage.js');

            // فحص وإصلاح المتغيرات غير المعرفة
            if (typeof window.healthApp === 'undefined') {
                fixes.push('⚠️ متغير healthApp غير معرف - سيتم إنشاؤه عند تحميل التطبيق');
            }

            showResult('fixResult', fixes.join('<br>'), 'success');
        }

        // إصلاح مشاكل التخزين
        function fixStorageIssues() {
            let fixes = [];

            try {
                // اختبار وإصلاح localStorage
                localStorage.setItem('health_app_test', JSON.stringify({ test: true }));
                const testData = JSON.parse(localStorage.getItem('health_app_test'));
                
                if (testData && testData.test) {
                    fixes.push('✅ localStorage يعمل بشكل صحيح');
                    localStorage.removeItem('health_app_test');
                } else {
                    fixes.push('❌ مشكلة في localStorage');
                }

                // فحص البيانات الموجودة
                const existingData = Object.keys(localStorage).filter(key => key.startsWith('health_app_'));
                fixes.push(`ℹ️ يوجد ${existingData.length} مفتاح بيانات محفوظ`);

                showResult('fixResult', fixes.join('<br>'), 'success');
            } catch (error) {
                showResult('fixResult', `❌ خطأ في التخزين: ${error.message}`, 'error');
            }
        }

        // إصلاح مشاكل المصادقة
        function fixAuthIssues() {
            let fixes = [];

            // فحص جلسة المستخدم
            const session = localStorage.getItem('health_app_session');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    const isValid = new Date().getTime() < sessionData.expiresAt;
                    
                    if (isValid) {
                        fixes.push('✅ جلسة المستخدم صالحة');
                    } else {
                        localStorage.removeItem('health_app_session');
                        fixes.push('🔧 تم إزالة جلسة منتهية الصلاحية');
                    }
                } catch (error) {
                    localStorage.removeItem('health_app_session');
                    fixes.push('🔧 تم إزالة جلسة تالفة');
                }
            } else {
                fixes.push('ℹ️ لا توجد جلسة نشطة');
            }

            // فحص بيانات المستخدمين
            const users = localStorage.getItem('health_app_users');
            if (users) {
                try {
                    const usersData = JSON.parse(users);
                    fixes.push(`✅ يوجد ${usersData.length} مستخدم مسجل`);
                } catch (error) {
                    fixes.push('⚠️ بيانات المستخدمين تالفة');
                }
            } else {
                fixes.push('ℹ️ لا يوجد مستخدمين مسجلين');
            }

            showResult('fixResult', fixes.join('<br>'), 'info');
        }

        // إصلاح مشاكل الواجهة
        function fixUIIssues() {
            let fixes = [];

            // فحص تحميل الخطوط
            const computedStyle = window.getComputedStyle(document.body);
            const fontFamily = computedStyle.fontFamily;
            
            if (fontFamily.includes('Cairo')) {
                fixes.push('✅ خط Cairo محمل بنجاح');
            } else {
                fixes.push('⚠️ خط Cairo غير محمل - استخدام الخط الافتراضي');
            }

            // فحص الأيقونات
            const icons = document.querySelectorAll('i.fas, i.far, i.fab');
            if (icons.length > 0) {
                fixes.push('✅ أيقونات FontAwesome محملة');
            } else {
                fixes.push('⚠️ أيقونات FontAwesome غير محملة');
            }

            // فحص التصميم المتجاوب
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                fixes.push('✅ إعدادات التصميم المتجاوب موجودة');
            } else {
                fixes.push('⚠️ إعدادات التصميم المتجاوب مفقودة');
            }

            showResult('fixResult', fixes.join('<br>'), 'success');
        }

        // إعادة تعيين التطبيق
        function resetApplication() {
            if (confirm('هل أنت متأكد من إعادة تعيين التطبيق؟ سيتم الاحتفاظ بالبيانات.')) {
                // إعادة تحميل الصفحة
                location.reload();
                showResult('resetResult', '🔄 تم إعادة تعيين التطبيق', 'success');
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.clear();
                showResult('resetResult', '🗑️ تم مسح جميع البيانات', 'success');
                
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }

        // تشغيل فحص سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runQuickDiagnostic, 500);
        });
    </script>
</body>
</html>
