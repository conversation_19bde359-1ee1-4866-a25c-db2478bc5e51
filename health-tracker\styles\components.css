/* ===== HERO VISUAL COMPONENTS ===== */

/* Phone Mockup */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.phone-mockup {
    width: 300px;
    height: 600px;
    background: linear-gradient(145deg, #f0f0f0, #ffffff);
    border-radius: 40px;
    padding: 20px;
    box-shadow: 
        0 0 0 8px rgba(0, 0, 0, 0.1),
        0 20px 40px rgba(0, 0, 0, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.9);
    position: relative;
    animation: phoneFloat 6s ease-in-out infinite;
}

.phone-mockup::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 6px;
    background: #333;
    border-radius: 3px;
}

.phone-mockup::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border: 2px solid #333;
    border-radius: 50%;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 30px;
    overflow: hidden;
    position: relative;
}

.app-interface {
    padding: 30px 20px 20px;
    height: 100%;
    color: white;
}

.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.app-title {
    font-size: 24px;
    font-weight: 800;
    color: white;
}

.app-date {
    font-size: 14px;
    opacity: 0.8;
}

/* Habit Cards */
.habit-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 30px;
}

.habit-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.habit-card:hover {
    transform: scale(1.05);
}

.habit-card i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.habit-card span {
    font-size: 12px;
    font-weight: 600;
    display: block;
    margin-bottom: 15px;
}

.habit-card.sleep i { color: #6366f1; }
.habit-card.water i { color: #06b6d4; }
.habit-card.exercise i { color: #f59e0b; }
.habit-card.nutrition i { color: #10b981; }

/* Progress Ring */
.progress-ring {
    width: 40px;
    height: 40px;
    position: relative;
    margin: 0 auto;
}

.progress-ring::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        var(--color, #10b981) 0deg,
        var(--color, #10b981) calc(var(--progress, 0%) * 3.6deg),
        rgba(255, 255, 255, 0.2) calc(var(--progress, 0%) * 3.6deg),
        rgba(255, 255, 255, 0.2) 360deg
    );
    border-radius: 50%;
}

.progress-ring::after {
    content: '';
    position: absolute;
    top: 6px;
    left: 6px;
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
}

.habit-card.sleep .progress-ring { --color: #6366f1; }
.habit-card.water .progress-ring { --color: #06b6d4; }
.habit-card.exercise .progress-ring { --color: #f59e0b; }
.habit-card.nutrition .progress-ring { --color: #10b981; }

/* Daily Quote */
.daily-quote {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.daily-quote p {
    font-size: 14px;
    font-style: italic;
    margin: 0;
    line-height: 1.5;
}

/* ===== OVERVIEW SECTION ===== */
.overview {
    padding: var(--spacing-4xl) 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    font-weight: 400;
}

.overview-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.overview-paragraph {
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-xl);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.overview-paragraph:hover {
    transform: translateX(-10px);
}

.overview-paragraph h3 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.overview-paragraph p {
    color: var(--gray-700);
    line-height: 1.8;
    margin: 0;
}

/* Health Pillars */
.health-pillars {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.pillar {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.pillar:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.pillar-sleep {
    border-color: var(--sleep-color);
}

.pillar-sleep:hover {
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
}

.pillar-water {
    border-color: var(--water-color);
}

.pillar-water:hover {
    box-shadow: 0 20px 40px rgba(6, 182, 212, 0.3);
}

.pillar-exercise {
    border-color: var(--exercise-color);
}

.pillar-exercise:hover {
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
}

.pillar-nutrition {
    border-color: var(--nutrition-color);
}

.pillar-nutrition:hover {
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
}

.pillar-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
    position: relative;
}

.pillar-sleep .pillar-icon {
    background: var(--gradient-secondary);
}

.pillar-water .pillar-icon {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.pillar-exercise .pillar-icon {
    background: var(--gradient-warm);
}

.pillar-nutrition .pillar-icon {
    background: var(--gradient-primary);
}

.pillar h4 {
    color: var(--gray-800);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    font-weight: 700;
}

.pillar p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
    line-height: 1.6;
}

/* ===== ANIMATIONS ===== */
@keyframes phoneFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(2deg);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .overview-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .phone-mockup {
        width: 250px;
        height: 500px;
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }
    
    .nav {
        display: none;
    }
    
    .header-actions .btn {
        display: none;
    }
    
    .header-actions .menu-toggle {
        display: flex;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .trust-badges {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .health-pillars {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .phone-mockup {
        width: 200px;
        height: 400px;
        padding: 15px;
    }
    
    .habit-cards {
        gap: 10px;
    }
    
    .habit-card {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 120px 0 60px;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .btn-large {
        width: 100%;
        max-width: 280px;
    }
    
    .overview-paragraph {
        padding: var(--spacing-lg);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: var(--spacing-4xl) 0;
    background: var(--white);
    position: relative;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.feature-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-health);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
    position: relative;
    box-shadow: var(--shadow-health);
}

.feature-card:nth-child(2) .feature-icon {
    background: var(--gradient-secondary);
}

.feature-card:nth-child(3) .feature-icon {
    background: var(--gradient-warm);
}

.feature-card:nth-child(4) .feature-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.feature-card:nth-child(5) .feature-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.feature-card:nth-child(6) .feature-icon {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.feature-card h3 {
    color: var(--gray-800);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.feature-highlight {
    display: inline-block;
    background: rgba(16, 185, 129, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* ===== CTA SECTION ===== */
.cta {
    padding: var(--spacing-4xl) 0;
    background: var(--gradient-health);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.cta-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.cta-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--spacing-xl);
    line-height: 1.2;
    color: var(--white);
}

.cta-text p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-2xl);
    opacity: 0.95;
    color: var(--white);
}

.cta-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.cta-stat {
    text-align: center;
}

.cta-stat .stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--white);
    display: block;
}

.cta-stat .stat-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 500;
}

.cta-actions {
    text-align: center;
}

.cta-btn {
    background: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
    font-size: var(--font-size-lg);
    padding: var(--spacing-lg) var(--spacing-3xl);
    margin-bottom: var(--spacing-lg);
}

.cta-btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.cta-note {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
    color: var(--white);
}

.cta-note i {
    color: #22c55e;
    margin-left: var(--spacing-sm);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-4xl);
    margin-bottom: var(--spacing-3xl);
}

.footer-brand .logo {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.footer-tagline {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    font-family: var(--font-secondary);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-3px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2xl);
}

.footer-column h4 {
    color: var(--white);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column li {
    margin-bottom: var(--spacing-sm);
}

.footer-column a {
    color: var(--gray-400);
    transition: color var(--transition-fast);
}

.footer-column a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-xl);
    text-align: center;
    color: var(--gray-500);
}

.footer-bottom p {
    margin: var(--spacing-sm) 0;
}

/* ===== RESPONSIVE DESIGN FOR NEW SECTIONS ===== */
@media (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-xl);
    }

    .cta-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .cta-text h2 {
        font-size: var(--font-size-3xl);
    }

    .cta-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .social-links {
        justify-content: center;
    }
}
