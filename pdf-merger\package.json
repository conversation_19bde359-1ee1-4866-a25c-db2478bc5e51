{"name": "pdfmerge-pro", "version": "1.0.0", "description": "أداة دمج ملفات PDF الأكثر تطوراً وذكاءً في العالم العربي. معالجة محلية آمنة، ذكاء اصطناعي متقدم، وخصوصية مطلقة.", "main": "index.html", "scripts": {"start": "serve . -p 8080", "dev": "serve . -p 3000", "build": "echo 'No build process needed for static site'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "deploy": "echo 'Configure deployment script'", "serve": "serve .", "preview": "serve . -p 8080"}, "keywords": ["pdf", "merge", "arabic", "عربي", "دمج", "ملفات", "pdf-tools", "productivity", "web-app", "javascript", "client-side", "privacy", "offline"], "author": {"name": "PDFMerge Pro Team", "email": "<EMAIL>", "url": "https://pdfmerge.pro"}, "license": "MIT", "homepage": "https://pdfmerge.pro", "repository": {"type": "git", "url": "https://github.com/pdfmerge-pro/pdfmerge-pro.git"}, "bugs": {"url": "https://github.com/pdfmerge-pro/pdfmerge-pro/issues", "email": "<EMAIL>"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"serve": "^14.2.4"}, "dependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "config": {"port": 8080, "host": "localhost"}, "funding": {"type": "github", "url": "https://github.com/sponsors/pdfmerge-pro"}, "contributors": [{"name": "PDFMerge Pro Team", "email": "<EMAIL>"}], "files": ["index.html", "help.html", "manifest.json", "sw.js", "styles/", "js/", "assets/", "README.md", "LICENSE"], "directories": {"doc": "./docs", "example": "./examples", "test": "./tests"}, "private": false, "publishConfig": {"access": "public"}, "workspaces": [], "type": "module", "exports": {".": "./index.html", "./help": "./help.html", "./manifest": "./manifest.json", "./sw": "./sw.js"}, "imports": {}, "sideEffects": false, "preferGlobal": false, "cpu": ["x64", "arm64"], "os": ["darwin", "linux", "win32"], "bundleDependencies": false, "man": [], "bin": {}, "overrides": {}, "resolutions": {}}