/**
 * PDFMerge Pro - UI Controller
 * متحكم واجهة المستخدم
 */

class UIController {
    constructor() {
        this.elements = {};
        this.draggedElement = null;
        this.isDragging = false;
        this.notifications = [];
        
        this.init();
    }

    /**
     * تهيئة المتحكم
     */
    init() {
        this.cacheElements();
        this.bindEvents();
        this.setupDragAndDrop();
        this.hideLoadingScreen();
    }

    /**
     * تخزين مراجع العناصر
     */
    cacheElements() {
        this.elements = {
            // Loading
            loadingScreen: document.getElementById('loadingScreen'),
            
            // Upload
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            selectFilesBtn: document.getElementById('selectFilesBtn'),
            
            // Files
            filesList: document.getElementById('filesList'),
            filesContainer: document.getElementById('filesContainer'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            addMoreBtn: document.getElementById('addMoreBtn'),
            
            // Merge
            mergeBtn: document.getElementById('mergeBtn'),
            optimizeSize: document.getElementById('optimizeSize'),
            removeDuplicates: document.getElementById('removeDuplicates'),
            compressPDF: document.getElementById('compressPDF'),
            compressionSettings: document.getElementById('compressionSettings'),
            
            // Modal
            processingModal: document.getElementById('processingModal'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            processedFiles: document.getElementById('processedFiles'),
            timeRemaining: document.getElementById('timeRemaining'),
            
            // Hero
            startMergingBtn: document.getElementById('startMergingBtn'),
            watchDemoBtn: document.getElementById('watchDemoBtn')
        };
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // Upload events
        this.elements.selectFilesBtn?.addEventListener('click', () => {
            this.elements.fileInput?.click();
        });

        this.elements.fileInput?.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Hero buttons
        this.elements.startMergingBtn?.addEventListener('click', () => {
            this.scrollToUploadSection();
        });

        this.elements.watchDemoBtn?.addEventListener('click', () => {
            this.showDemo();
        });

        // File management
        this.elements.clearAllBtn?.addEventListener('click', () => {
            this.clearAllFiles();
        });

        this.elements.addMoreBtn?.addEventListener('click', () => {
            this.elements.fileInput?.click();
        });

        // Merge button
        this.elements.mergeBtn?.addEventListener('click', () => {
            this.startMerging();
        });

        // Compression settings toggle
        this.elements.compressPDF?.addEventListener('change', (e) => {
            this.toggleCompressionSettings(e.target.checked);
        });

        // Modal events
        this.elements.processingModal?.addEventListener('click', (e) => {
            if (e.target === this.elements.processingModal) {
                // لا نسمح بإغلاق المودال أثناء المعالجة
            }
        });
    }

    /**
     * إعداد السحب والإفلات
     */
    setupDragAndDrop() {
        const uploadArea = this.elements.uploadArea;
        if (!uploadArea) return;

        // منع السلوك الافتراضي
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // تأثيرات بصرية
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('drag-over');
            }, false);
        });

        // معالجة الإفلات
        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelection(files);
        }, false);
    }

    /**
     * منع السلوك الافتراضي للأحداث
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingScreen() {
        setTimeout(() => {
            if (this.elements.loadingScreen) {
                this.elements.loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    this.elements.loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1000);
    }

    /**
     * التمرير إلى قسم الرفع
     */
    scrollToUploadSection() {
        const uploadSection = document.getElementById('upload-section');
        if (uploadSection) {
            uploadSection.scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * عرض العرض التوضيحي
     */
    showDemo() {
        this.showNotification('العرض التوضيحي قريباً!', 'info');
    }

    /**
     * معالجة اختيار الملفات
     */
    async handleFileSelection(files) {
        if (!files || files.length === 0) return;

        try {
            this.showNotification('جاري تحميل الملفات...', 'info');
            
            const addedFiles = await pdfProcessor.addFiles(files);
            
            if (addedFiles.length > 0) {
                this.updateFilesList();
                this.showFilesList();
                this.updateMergeButton();
                
                this.showNotification(
                    `تم إضافة ${addedFiles.length} ملف بنجاح`, 
                    'success'
                );
            }
            
        } catch (error) {
            this.showNotification('خطأ في تحميل الملفات', 'error');
            handleError(error, 'تحميل الملفات');
        }
    }

    /**
     * تحديث قائمة الملفات
     */
    updateFilesList() {
        const container = this.elements.filesContainer;
        if (!container) return;

        container.innerHTML = '';
        const files = pdfProcessor.getFilesInfo();

        files.forEach((file, index) => {
            const fileElement = this.createFileElement(file, index);
            container.appendChild(fileElement);
        });

        this.setupFileDragAndDrop();
    }

    /**
     * إنشاء عنصر ملف
     */
    createFileElement(file, index) {
        const fileItem = createElement('div', {
            className: 'file-item',
            dataset: { fileId: file.id }
        });

        fileItem.innerHTML = `
            <div class="file-drag-handle">
                <i class="fas fa-grip-vertical"></i>
            </div>
            <div class="file-icon">
                <i class="fas fa-file-pdf"></i>
            </div>
            <div class="file-info">
                <div class="file-name">${sanitizeText(file.name)}</div>
                <div class="file-details">
                    <span>${formatFileSize(file.size)}</span>
                    <span>${file.pageCount} صفحة</span>
                </div>
            </div>
            <div class="file-preview">
                <span>PDF</span>
            </div>
            <div class="file-actions">
                <button class="file-action-btn" title="تدوير">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="file-action-btn delete" title="حذف" data-action="delete">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        // ربط أحداث الأزرار
        const deleteBtn = fileItem.querySelector('[data-action="delete"]');
        deleteBtn?.addEventListener('click', () => {
            this.removeFile(file.id);
        });

        return fileItem;
    }

    /**
     * إعداد سحب وإفلات الملفات
     */
    setupFileDragAndDrop() {
        const fileItems = this.elements.filesContainer?.querySelectorAll('.file-item');
        
        fileItems?.forEach(item => {
            item.draggable = true;
            
            item.addEventListener('dragstart', (e) => {
                this.draggedElement = item;
                item.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
                this.draggedElement = null;
            });

            item.addEventListener('dragover', (e) => {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });

            item.addEventListener('drop', (e) => {
                e.preventDefault();
                if (this.draggedElement && this.draggedElement !== item) {
                    this.reorderFiles(this.draggedElement, item);
                }
            });
        });
    }

    /**
     * إعادة ترتيب الملفات
     */
    reorderFiles(draggedItem, targetItem) {
        const draggedId = draggedItem.dataset.fileId;
        const targetId = targetItem.dataset.fileId;
        
        const files = pdfProcessor.getFilesInfo();
        const targetIndex = files.findIndex(f => f.id === targetId);
        
        pdfProcessor.reorderFile(draggedId, targetIndex);
        this.updateFilesList();
        
        this.showNotification('تم إعادة ترتيب الملفات', 'success');
    }

    /**
     * إزالة ملف
     */
    removeFile(fileId) {
        pdfProcessor.removeFile(fileId);
        this.updateFilesList();
        this.updateMergeButton();
        
        if (pdfProcessor.files.length === 0) {
            this.hideFilesList();
        }
        
        this.showNotification('تم حذف الملف', 'info');
    }

    /**
     * مسح جميع الملفات
     */
    clearAllFiles() {
        pdfProcessor.clearFiles();
        this.hideFilesList();
        this.updateMergeButton();
        this.showNotification('تم مسح جميع الملفات', 'info');
    }

    /**
     * عرض قائمة الملفات
     */
    showFilesList() {
        if (this.elements.filesList) {
            this.elements.filesList.style.display = 'block';
            addClassWithAnimation(this.elements.filesList, 'slide-in-up');
        }
    }

    /**
     * إخفاء قائمة الملفات
     */
    hideFilesList() {
        if (this.elements.filesList) {
            this.elements.filesList.style.display = 'none';
        }
    }

    /**
     * تحديث زر الدمج
     */
    updateMergeButton() {
        const mergeBtn = this.elements.mergeBtn;
        if (!mergeBtn) return;

        const fileCount = pdfProcessor.files.length;
        
        if (fileCount >= 2) {
            mergeBtn.disabled = false;
            mergeBtn.innerHTML = `
                <i class="fas fa-magic"></i>
                دمج ${fileCount} ملفات
            `;
        } else {
            mergeBtn.disabled = true;
            mergeBtn.innerHTML = `
                <i class="fas fa-magic"></i>
                يحتاج ملفين على الأقل
            `;
        }
    }

    /**
     * تبديل إعدادات الضغط
     */
    toggleCompressionSettings(show) {
        const settings = this.elements.compressionSettings;
        if (!settings) return;

        if (show) {
            settings.style.display = 'block';
            addClassWithAnimation(settings, 'slide-in-up');
        } else {
            settings.style.display = 'none';
        }
    }

    /**
     * الحصول على مستوى الضغط المحدد
     */
    getCompressionLevel() {
        const selectedRadio = document.querySelector('input[name="compressionLevel"]:checked');
        return selectedRadio ? selectedRadio.value : 'medium';
    }

    /**
     * بدء عملية الدمج
     */
    async startMerging() {
        if (pdfProcessor.files.length < 2) {
            this.showNotification('يجب اختيار ملفين على الأقل للدمج', 'warning');
            return;
        }

        try {
            this.showProcessingModal();
            
            const options = {
                optimize: this.elements.optimizeSize?.checked || false,
                removeDuplicates: this.elements.removeDuplicates?.checked || false,
                compress: this.elements.compressPDF?.checked || false,
                compressionLevel: this.getCompressionLevel()
            };

            const result = await pdfProcessor.mergeFiles(options);
            
            this.hideProcessingModal();
            this.downloadMergedFile(result);
            this.showMergeSuccess(result);
            
        } catch (error) {
            this.hideProcessingModal();
            this.showNotification('فشل في دمج الملفات', 'error');
            handleError(error, 'دمج الملفات');
        }
    }

    /**
     * عرض مودال المعالجة
     */
    showProcessingModal() {
        const modal = this.elements.processingModal;
        if (modal) {
            modal.classList.add('active');
            this.resetProgress();
        }
    }

    /**
     * إخفاء مودال المعالجة
     */
    hideProcessingModal() {
        const modal = this.elements.processingModal;
        if (modal) {
            modal.classList.remove('active');
        }
    }

    /**
     * إعادة تعيين شريط التقدم
     */
    resetProgress() {
        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = '0%';
        }
        if (this.elements.progressText) {
            this.elements.progressText.textContent = 'بدء المعالجة...';
        }
        if (this.elements.processedFiles) {
            this.elements.processedFiles.textContent = '0';
        }
        if (this.elements.timeRemaining) {
            this.elements.timeRemaining.textContent = 'حساب...';
        }
    }

    /**
     * تحديث شريط التقدم
     */
    updateProgress(progress) {
        if (progress.percentage && this.elements.progressFill) {
            animateProgressBar(this.elements.progressFill, progress.percentage);
        }

        if (progress.type === 'file_start' && this.elements.progressText) {
            this.elements.progressText.textContent = `معالجة ${progress.fileName}...`;
        }

        if (progress.processedPages && this.elements.processedFiles) {
            this.elements.processedFiles.textContent = progress.processedPages;
        }

        // حساب الوقت المتبقي (تقديري)
        if (progress.percentage && this.elements.timeRemaining) {
            const remaining = (100 - progress.percentage) * 0.1; // تقدير بسيط
            this.elements.timeRemaining.textContent = formatTime(remaining);
        }
    }

    /**
     * تحميل الملف المدموج
     */
    downloadMergedFile(result) {
        const blob = new Blob([result.pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        const a = createElement('a', {
            href: url,
            download: result.fileName
        });
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // تنظيف الذاكرة
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 1000);
    }

    /**
     * عرض رسالة نجاح الدمج
     */
    showMergeSuccess(result) {
        const message = `
            تم دمج ${result.originalFiles} ملفات بنجاح!
            الحجم النهائي: ${formatFileSize(result.fileSize)}
            عدد الصفحات: ${result.pageCount}
        `;
        
        this.showNotification(message, 'success');
    }

    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        // سيتم تنفيذ نظام الإشعارات المتقدم لاحقاً
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // إشعار بسيط مؤقت
        if (type === 'error') {
            alert(`خطأ: ${message}`);
        }
    }
}

// إنشاء مثيل المتحكم عند تحميل الصفحة
let uiController;

document.addEventListener('DOMContentLoaded', () => {
    uiController = new UIController();
    
    // ربط أحداث معالج PDF
    pdfProcessor.onProgress = (progress) => {
        uiController.updateProgress(progress);
    };
    
    pdfProcessor.onError = (error, fileName) => {
        uiController.showNotification(
            `خطأ في معالجة ${fileName || 'الملف'}: ${error.message}`, 
            'error'
        );
    };
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.UIController = UIController;
}
