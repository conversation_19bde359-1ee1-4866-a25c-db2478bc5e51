/**
 * Health App - Utility Functions
 * دوال مساعدة للتطبيق الصحي
 */

// ===== DOM UTILITIES =====

/**
 * إنشاء عنصر HTML
 */
function createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    Object.keys(attributes).forEach(key => {
        if (key === 'className') {
            element.className = attributes[key];
        } else if (key === 'dataset') {
            Object.keys(attributes[key]).forEach(dataKey => {
                element.dataset[dataKey] = attributes[key][dataKey];
            });
        } else {
            element.setAttribute(key, attributes[key]);
        }
    });
    
    if (content) {
        element.textContent = content;
    }
    
    return element;
}

/**
 * إضافة مستمع أحداث مع إزالة تلقائية
 */
function addEventListenerOnce(element, event, handler, options = {}) {
    const wrappedHandler = (e) => {
        handler(e);
        element.removeEventListener(event, wrappedHandler, options);
    };
    element.addEventListener(event, wrappedHandler, options);
}

/**
 * البحث عن عنصر مع انتظار
 */
function waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }
        
        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                resolve(element);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        }, timeout);
    });
}

// ===== DATE UTILITIES =====

/**
 * تنسيق التاريخ بالعربية
 */
function formatArabicDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    return date.toLocaleDateString('ar-SA', finalOptions);
}

/**
 * تنسيق الوقت بالعربية
 */
function formatArabicTime(date, options = {}) {
    const defaultOptions = {
        hour: '2-digit',
        minute: '2-digit'
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    return date.toLocaleTimeString('ar-SA', finalOptions);
}

/**
 * حساب الفرق بين تاريخين
 */
function dateDifference(date1, date2, unit = 'days') {
    const diffInMs = Math.abs(date2.getTime() - date1.getTime());
    
    switch (unit) {
        case 'seconds':
            return Math.floor(diffInMs / 1000);
        case 'minutes':
            return Math.floor(diffInMs / (1000 * 60));
        case 'hours':
            return Math.floor(diffInMs / (1000 * 60 * 60));
        case 'days':
            return Math.floor(diffInMs / (1000 * 60 * 60 * 24));
        case 'weeks':
            return Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 7));
        default:
            return diffInMs;
    }
}

/**
 * الحصول على بداية اليوم
 */
function getStartOfDay(date = new Date()) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    return start;
}

/**
 * الحصول على نهاية اليوم
 */
function getEndOfDay(date = new Date()) {
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return end;
}

/**
 * الحصول على بداية الأسبوع
 */
function getStartOfWeek(date = new Date()) {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // السبت كبداية الأسبوع
    start.setDate(diff);
    start.setHours(0, 0, 0, 0);
    return start;
}

// ===== NUMBER UTILITIES =====

/**
 * تنسيق الأرقام بالعربية
 */
function formatArabicNumber(number, options = {}) {
    const defaultOptions = {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    return number.toLocaleString('ar-SA', finalOptions);
}

/**
 * تحويل الأرقام الإنجليزية إلى عربية
 */
function toArabicNumerals(str) {
    const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
    return str.replace(/[0-9]/g, (match) => arabicNumerals[parseInt(match)]);
}

/**
 * تحويل الأرقام العربية إلى إنجليزية
 */
function toEnglishNumerals(str) {
    const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
    return str.replace(/[٠-٩]/g, (match) => arabicNumerals.indexOf(match).toString());
}

/**
 * تقريب الرقم إلى أقرب عدد صحيح
 */
function roundToNearest(number, nearest = 1) {
    return Math.round(number / nearest) * nearest;
}

// ===== HEALTH CALCULATIONS =====

/**
 * حساب مؤشر كتلة الجسم
 */
function calculateBMI(weight, height) {
    const heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
}

/**
 * تصنيف مؤشر كتلة الجسم
 */
function classifyBMI(bmi) {
    if (bmi < 18.5) return { category: 'نقص في الوزن', color: '#3b82f6' };
    if (bmi < 25) return { category: 'وزن طبيعي', color: '#10b981' };
    if (bmi < 30) return { category: 'زيادة في الوزن', color: '#f59e0b' };
    return { category: 'سمنة', color: '#ef4444' };
}

/**
 * حساب السعرات الحرارية المطلوبة يومياً
 */
function calculateDailyCalories(weight, height, age, gender, activityLevel = 'moderate') {
    let bmr;
    
    // معادلة Harris-Benedict المحدثة
    if (gender === 'male') {
        bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    } else {
        bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    }
    
    const activityFactors = {
        sedentary: 1.2,      // قليل الحركة
        light: 1.375,        // نشاط خفيف
        moderate: 1.55,      // نشاط متوسط
        active: 1.725,       // نشاط عالي
        very_active: 1.9     // نشاط عالي جداً
    };
    
    return Math.round(bmr * (activityFactors[activityLevel] || 1.55));
}

/**
 * حساب كمية الماء المطلوبة يومياً
 */
function calculateDailyWater(weight, activityLevel = 'moderate', climate = 'normal') {
    let baseWater = weight * 35; // 35ml لكل كيلوغرام
    
    // إضافة ماء حسب النشاط
    const activityBonus = {
        sedentary: 0,
        light: 250,
        moderate: 500,
        active: 750,
        very_active: 1000
    };
    
    // إضافة ماء حسب المناخ
    const climateBonus = {
        cold: -250,
        normal: 0,
        hot: 500,
        very_hot: 1000
    };
    
    const totalWater = baseWater + 
                      (activityBonus[activityLevel] || 500) + 
                      (climateBonus[climate] || 0);
    
    return Math.round(totalWater);
}

/**
 * حساب معدل ضربات القلب المستهدف
 */
function calculateTargetHeartRate(age, intensity = 'moderate') {
    const maxHeartRate = 220 - age;
    
    const intensityRanges = {
        light: [0.5, 0.6],      // 50-60%
        moderate: [0.6, 0.7],   // 60-70%
        vigorous: [0.7, 0.85],  // 70-85%
        maximum: [0.85, 1.0]    // 85-100%
    };
    
    const range = intensityRanges[intensity] || intensityRanges.moderate;
    
    return {
        min: Math.round(maxHeartRate * range[0]),
        max: Math.round(maxHeartRate * range[1]),
        maxHeartRate: maxHeartRate
    };
}

// ===== PERFORMANCE UTILITIES =====

/**
 * تأخير تنفيذ دالة (Debounce)
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

/**
 * تحديد معدل تنفيذ دالة (Throttle)
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * تنفيذ دالة مع إعادة المحاولة
 */
async function retry(fn, maxAttempts = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            if (attempt === maxAttempts) {
                throw error;
            }
            await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
    }
}

// ===== VALIDATION UTILITIES =====

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 */
function isValidSaudiPhone(phone) {
    const phoneRegex = /^(\+966|966|0)?5[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * التحقق من قوة كلمة المرور
 */
function checkPasswordStrength(password) {
    const checks = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    const score = Object.values(checks).filter(Boolean).length;
    
    let strength = 'ضعيفة';
    if (score >= 4) strength = 'قوية';
    else if (score >= 3) strength = 'متوسطة';
    
    return {
        score,
        strength,
        checks
    };
}

// ===== ANIMATION UTILITIES =====

/**
 * تحريك رقم من قيمة إلى أخرى
 */
function animateNumber(element, start, end, duration = 1000, easing = 'easeOutQuart') {
    const startTime = performance.now();
    const difference = end - start;
    
    const easingFunctions = {
        linear: t => t,
        easeInQuad: t => t * t,
        easeOutQuad: t => t * (2 - t),
        easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        easeOutQuart: t => 1 - (--t) * t * t * t
    };
    
    const easingFunction = easingFunctions[easing] || easingFunctions.easeOutQuart;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = easingFunction(progress);
        const current = start + (difference * easedProgress);
        
        element.textContent = Math.round(current);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * تحريك شريط التقدم
 */
function animateProgressBar(progressBar, percentage, duration = 1000) {
    progressBar.style.transition = `width ${duration}ms ease-out`;
    progressBar.style.width = `${Math.min(percentage, 100)}%`;
}

// ===== EXPORT =====
if (typeof window !== 'undefined') {
    // تصدير جميع الدوال للنافذة العامة
    Object.assign(window, {
        createElement,
        addEventListenerOnce,
        waitForElement,
        formatArabicDate,
        formatArabicTime,
        dateDifference,
        getStartOfDay,
        getEndOfDay,
        getStartOfWeek,
        formatArabicNumber,
        toArabicNumerals,
        toEnglishNumerals,
        roundToNearest,
        calculateBMI,
        classifyBMI,
        calculateDailyCalories,
        calculateDailyWater,
        calculateTargetHeartRate,
        debounce,
        throttle,
        retry,
        isValidEmail,
        isValidSaudiPhone,
        checkPasswordStrength,
        animateNumber,
        animateProgressBar
    });
}
