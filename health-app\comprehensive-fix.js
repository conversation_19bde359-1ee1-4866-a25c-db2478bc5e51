/**
 * Health App - Comprehensive Fix
 * إصلاح شامل لجميع مشاكل التطبيق
 */

class ComprehensiveFix {
    constructor() {
        this.fixes = [];
        this.errors = [];
        this.warnings = [];
        
        this.init();
    }

    /**
     * تهيئة الإصلاح الشامل
     */
    init() {
        console.log('🔧 بدء الإصلاح الشامل للتطبيق');
        
        // تشغيل جميع الإصلاحات
        this.runAllFixes();
    }

    /**
     * تشغيل جميع الإصلاحات
     */
    async runAllFixes() {
        const fixes = [
            { name: 'إصلاح نظام المصادقة', func: this.fixAuthSystem.bind(this) },
            { name: 'إصلاح قائمة المستخدم', func: this.fixUserMenu.bind(this) },
            { name: 'إصلاح التنقل', func: this.fixNavigation.bind(this) },
            { name: 'إصلاح التخزين', func: this.fixStorage.bind(this) },
            { name: 'إصلاح الواجهة', func: this.fixUI.bind(this) },
            { name: 'إصلاح الأداء', func: this.fixPerformance.bind(this) },
            { name: 'إصلاح التوافق', func: this.fixCompatibility.bind(this) }
        ];

        for (const fix of fixes) {
            try {
                console.log(`🔄 ${fix.name}...`);
                await fix.func();
                this.fixes.push(`✅ ${fix.name}`);
                console.log(`✅ تم ${fix.name}`);
            } catch (error) {
                this.errors.push(`❌ ${fix.name}: ${error.message}`);
                console.error(`❌ خطأ في ${fix.name}:`, error);
            }
        }

        // عرض النتائج
        this.showResults();
    }

    /**
     * إصلاح نظام المصادقة
     */
    async fixAuthSystem() {
        // التأكد من تحميل نظام المصادقة
        if (typeof HealthAuth === 'undefined') {
            throw new Error('نظام المصادقة غير محمل');
        }

        // تهيئة نظام المصادقة إذا لم يكن مهيأ
        if (!window.healthApp || !window.healthApp.auth) {
            const auth = new HealthAuth();
            if (window.healthApp) {
                window.healthApp.auth = auth;
            }
        }

        // إصلاح الجلسات المنتهية الصلاحية
        const session = localStorage.getItem('health_app_session');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                if (new Date().getTime() >= sessionData.expiresAt) {
                    localStorage.removeItem('health_app_session');
                    console.log('🔧 تم إزالة جلسة منتهية الصلاحية');
                }
            } catch (error) {
                localStorage.removeItem('health_app_session');
                console.log('🔧 تم إزالة جلسة تالفة');
            }
        }
    }

    /**
     * إصلاح قائمة المستخدم
     */
    async fixUserMenu() {
        const userMenu = document.querySelector('.user-menu');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');

        if (!userMenu || !userMenuBtn) {
            this.warnings.push('⚠️ عناصر قائمة المستخدم غير موجودة');
            return;
        }

        // إزالة مستمعي الأحداث القدامى
        const newUserMenuBtn = userMenuBtn.cloneNode(true);
        userMenuBtn.parentNode.replaceChild(newUserMenuBtn, userMenuBtn);

        // إضافة مستمعي أحداث جدد
        newUserMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenu.classList.toggle('active');
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!userMenu.contains(e.target)) {
                userMenu.classList.remove('active');
            }
        });

        // إعداد روابط القائمة
        if (userDropdown) {
            const dropdownItems = userDropdown.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = item.getAttribute('data-page');
                    
                    if (page) {
                        // التنقل للصفحة
                        if (window.healthApp && window.healthApp.loadPage) {
                            window.healthApp.loadPage(page);
                        } else {
                            // استخدام التنقل البديل
                            this.navigateToPage(page);
                        }
                        userMenu.classList.remove('active');
                    } else if (item.classList.contains('logout-btn')) {
                        // تسجيل الخروج
                        this.handleLogout();
                    }
                });
            });
        }

        // تحديث معلومات المستخدم
        this.updateUserInfo();
    }

    /**
     * تحديث معلومات المستخدم
     */
    updateUserInfo() {
        let user = null;

        // محاولة الحصول على المستخدم من مصادر مختلفة
        if (window.healthApp && window.healthApp.auth) {
            user = window.healthApp.auth.getCurrentUser();
        } else if (typeof AuthGuard !== 'undefined') {
            user = AuthGuard.getCurrentUser();
        } else {
            // محاولة قراءة الجلسة مباشرة
            const session = localStorage.getItem('health_app_session');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    if (new Date().getTime() < sessionData.expiresAt) {
                        user = sessionData.user;
                    }
                } catch (error) {
                    console.warn('خطأ في قراءة بيانات المستخدم:', error);
                }
            }
        }

        if (user) {
            // تحديث اسم المستخدم
            const userNameElements = document.querySelectorAll('.user-name, #userName');
            userNameElements.forEach(element => {
                element.textContent = user.name || 'مستخدم';
            });

            // تحديث صورة المستخدم
            const userAvatarElements = document.querySelectorAll('.user-avatar, #userAvatar');
            userAvatarElements.forEach(element => {
                element.src = this.generateAvatarUrl(user.name || 'مستخدم');
            });

            // إظهار قائمة المستخدم
            const userMenu = document.querySelector('.user-menu');
            if (userMenu) {
                userMenu.style.display = 'block';
            }
        } else {
            // إخفاء قائمة المستخدم
            const userMenu = document.querySelector('.user-menu');
            if (userMenu) {
                userMenu.style.display = 'none';
            }
        }
    }

    /**
     * توليد رابط صورة افتراضية
     */
    generateAvatarUrl(name) {
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
        const color = colors[name.length % colors.length];
        
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color.substring(1)}&color=fff&size=40&font-size=0.6`;
    }

    /**
     * معالجة تسجيل الخروج
     */
    handleLogout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // حفظ البيانات
            if (window.healthApp && window.healthApp.saveData) {
                window.healthApp.saveData();
            }

            // مسح الجلسة
            localStorage.removeItem('health_app_session');
            localStorage.removeItem('health_app_remember');

            // الانتقال لصفحة تسجيل الدخول
            window.location.href = 'auth.html';
        }
    }

    /**
     * إصلاح التنقل
     */
    async fixNavigation() {
        // إصلاح روابط التنقل
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            if (!link.hasAttribute('data-fixed-nav')) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.getAttribute('data-page');
                    if (page) {
                        this.navigateToPage(page);
                    }
                });
                link.setAttribute('data-fixed-nav', 'true');
            }
        });

        // إصلاح الأزرار السريعة
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            if (!btn.hasAttribute('data-fixed-action')) {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const action = btn.textContent.trim();
                    
                    if (action.includes('ماء') || btn.id === 'quickAddBtn') {
                        this.addWaterQuick();
                    }
                });
                btn.setAttribute('data-fixed-action', 'true');
            }
        });
    }

    /**
     * التنقل إلى صفحة
     */
    navigateToPage(page) {
        try {
            // إخفاء جميع الصفحات
            const pages = document.querySelectorAll('.page');
            pages.forEach(p => p.classList.remove('active'));

            // إزالة الفئة النشطة من الروابط
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // إظهار الصفحة المطلوبة
            const targetPage = document.getElementById(`${page}Page`);
            const targetLink = document.querySelector(`[data-page="${page}"]`);

            if (targetPage) {
                targetPage.classList.add('active');
                
                if (targetLink) {
                    targetLink.classList.add('active');
                }

                // تحديث عنوان الصفحة
                this.updatePageTitle(page);
                
                console.log(`✅ تم الانتقال إلى صفحة ${page}`);
            } else {
                console.warn(`⚠️ الصفحة ${page} غير موجودة`);
            }
            
        } catch (error) {
            console.error('خطأ في التنقل:', error);
        }
    }

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle(page) {
        const titles = {
            dashboard: 'لوحة التحكم',
            sleep: 'تتبع النوم',
            water: 'تتبع الماء',
            exercise: 'التمارين الرياضية',
            nutrition: 'التغذية',
            reports: 'التقارير',
            goals: 'الأهداف',
            reminders: 'التذكيرات',
            profile: 'الملف الشخصي',
            settings: 'الإعدادات'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && titles[page]) {
            pageTitle.textContent = titles[page];
        }
    }

    /**
     * إضافة ماء سريع
     */
    addWaterQuick() {
        try {
            if (typeof HealthStorage !== 'undefined') {
                const waterEntry = {
                    id: Date.now(),
                    amount: 250,
                    timestamp: new Date().toISOString(),
                    type: 'glass'
                };
                
                HealthStorage.addWaterEntry(waterEntry);
                
                // عرض إشعار
                this.showToast('تم إضافة كوب ماء (250ml)', 'success');
                
                // تحديث الإحصائيات
                if (window.healthApp && window.healthApp.updateDashboardStats) {
                    window.healthApp.updateDashboardStats();
                }
            }
        } catch (error) {
            console.error('خطأ في إضافة الماء:', error);
            this.showToast('خطأ في إضافة الماء', 'error');
        }
    }

    /**
     * إصلاح التخزين
     */
    async fixStorage() {
        // فحص وإصلاح localStorage
        try {
            localStorage.setItem('health_app_test', 'test');
            localStorage.removeItem('health_app_test');
        } catch (error) {
            throw new Error('localStorage غير متاح');
        }

        // إصلاح البيانات التالفة
        const keys = Object.keys(localStorage).filter(key => key.startsWith('health_app_'));
        keys.forEach(key => {
            try {
                const data = localStorage.getItem(key);
                JSON.parse(data); // اختبار صحة JSON
            } catch (error) {
                console.warn(`إزالة بيانات تالفة: ${key}`);
                localStorage.removeItem(key);
            }
        });
    }

    /**
     * إصلاح الواجهة
     */
    async fixUI() {
        // إصلاح مشاكل CSS
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        if (stylesheets.length === 0) {
            this.warnings.push('⚠️ لا توجد ملفات CSS محملة');
        }

        // إصلاح الخطوط
        const computedStyle = window.getComputedStyle(document.body);
        const fontFamily = computedStyle.fontFamily;
        if (!fontFamily.includes('Cairo')) {
            this.warnings.push('⚠️ خط Cairo غير محمل');
        }

        // إصلاح الأيقونات
        const icons = document.querySelectorAll('i.fas, i.far, i.fab');
        if (icons.length === 0) {
            this.warnings.push('⚠️ أيقونات FontAwesome غير محملة');
        }

        // تحديث التاريخ الحالي
        this.updateCurrentDate();
    }

    /**
     * تحديث التاريخ الحالي
     */
    updateCurrentDate() {
        const currentDateElement = document.getElementById('currentDate');
        if (currentDateElement) {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            currentDateElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    }

    /**
     * إصلاح الأداء
     */
    async fixPerformance() {
        // تحسين تحميل الصور
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
        });

        // تحسين الرسوم البيانية
        if (typeof Chart !== 'undefined') {
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
        }
    }

    /**
     * إصلاح التوافق
     */
    async fixCompatibility() {
        // فحص دعم الميزات الأساسية
        const features = [
            { name: 'localStorage', test: () => typeof Storage !== 'undefined' },
            { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
            { name: 'Flexbox', test: () => CSS.supports('display', 'flex') },
            { name: 'ES6 Classes', test: () => typeof class {} === 'function' }
        ];

        features.forEach(feature => {
            if (!feature.test()) {
                this.warnings.push(`⚠️ ${feature.name} غير مدعوم`);
            }
        });
    }

    /**
     * عرض إشعار Toast
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-message">${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            padding: 16px 20px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-weight: 500;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    /**
     * عرض النتائج
     */
    showResults() {
        console.log('\n🎯 نتائج الإصلاح الشامل:');
        console.log('✅ الإصلاحات المكتملة:', this.fixes.length);
        console.log('❌ الأخطاء:', this.errors.length);
        console.log('⚠️ التحذيرات:', this.warnings.length);

        if (this.fixes.length > 0) {
            console.log('\n✅ الإصلاحات:');
            this.fixes.forEach(fix => console.log(fix));
        }

        if (this.errors.length > 0) {
            console.log('\n❌ الأخطاء:');
            this.errors.forEach(error => console.log(error));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ التحذيرات:');
            this.warnings.forEach(warning => console.log(warning));
        }

        // عرض إشعار للمستخدم
        const totalIssues = this.errors.length + this.warnings.length;
        if (totalIssues === 0) {
            this.showToast('✅ تم إصلاح جميع المشاكل بنجاح!', 'success');
        } else if (this.errors.length === 0) {
            this.showToast(`⚠️ تم الإصلاح مع ${this.warnings.length} تحذير`, 'warning');
        } else {
            this.showToast(`❌ ${this.errors.length} خطأ، ${this.warnings.length} تحذير`, 'error');
        }

        console.log('\n🔧 تم الانتهاء من الإصلاح الشامل');
    }
}

// تشغيل الإصلاح الشامل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار قليل للتأكد من تحميل جميع المكونات
    setTimeout(() => {
        new ComprehensiveFix();
    }, 1000);
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.ComprehensiveFix = ComprehensiveFix;
}
