/**
 * Health App - Nutrition Tracker
 * متتبع التغذية
 */

class NutritionTracker {
    constructor(app) {
        this.app = app;
        this.meals = [];
        this.dailyCalories = 0;
        this.calorieGoal = 2000;
        
        this.init();
    }

    /**
     * تهيئة متتبع التغذية
     */
    init() {
        console.log('🍎 تهيئة متتبع التغذية');
        this.loadNutritionData();
        this.setupNutritionPage();
    }

    /**
     * تحميل بيانات التغذية
     */
    loadNutritionData() {
        const data = this.app.storage.getNutritionData();
        this.meals = data.meals || [];
        this.dailyCalories = data.dailyCalories || 0;
        this.calorieGoal = data.calorieGoal || 2000;
    }

    /**
     * إعداد صفحة التغذية
     */
    setupNutritionPage() {
        const nutritionPage = document.getElementById('nutritionPage');
        if (!nutritionPage) return;

        nutritionPage.innerHTML = `
            <div class="nutrition-container">
                <div class="page-header">
                    <h2>التغذية</h2>
                    <p>راقب نظامك الغذائي والسعرات الحرارية</p>
                </div>
                
                <div class="coming-soon">
                    <i class="fas fa-apple-alt"></i>
                    <h3>قريباً</h3>
                    <p>صفحة التغذية قيد التطوير</p>
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>تتبع السعرات الحرارية</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>قاعدة بيانات الأطعمة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>تحليل العناصر الغذائية</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>خطط غذائية مخصصة</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * حفظ بيانات التغذية
     */
    saveNutritionData() {
        this.app.storage.saveNutritionData({
            meals: this.meals,
            dailyCalories: this.dailyCalories,
            calorieGoal: this.calorieGoal,
            lastUpdated: new Date().toISOString()
        });
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.NutritionTracker = NutritionTracker;
}
