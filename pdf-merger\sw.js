/**
 * PDFMerge Pro - Service Worker
 * خدمة العامل للتخزين المؤقت وتحسين الأداء
 */

const CACHE_NAME = 'pdfmerge-pro-v1.0.0';
const STATIC_CACHE = 'pdfmerge-static-v1';
const DYNAMIC_CACHE = 'pdfmerge-dynamic-v1';

// الملفات المطلوب تخزينها مؤقتاً
const STATIC_FILES = [
    '/',
    '/index.html',
    '/styles/main.css',
    '/styles/components.css',
    '/styles/animations.css',
    '/js/utils.js',
    '/js/pdf-processor.js',
    '/js/ui-controller.js',
    '/js/main.js',
    '/assets/favicon.svg',
    '/manifest.json',
    // External resources
    'https://cdnjs.cloudflare.com/ajax/libs/pdf-lib/1.17.1/pdf-lib.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap'
];

// أحداث Service Worker

// التثبيت
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker: تثبيت...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('📦 Service Worker: تخزين الملفات الثابتة...');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('✅ Service Worker: تم التثبيت بنجاح');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Service Worker: خطأ في التثبيت:', error);
            })
    );
});

// التفعيل
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker: تفعيل...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        // حذف التخزين المؤقت القديم
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Service Worker: حذف تخزين قديم:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker: تم التفعيل بنجاح');
                return self.clients.claim();
            })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', (event) => {
    const request = event.request;
    const url = new URL(request.url);
    
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // تجاهل طلبات POST (رفع الملفات)
    if (request.method !== 'GET') {
        return;
    }
    
    event.respondWith(
        handleRequest(request)
    );
});

/**
 * معالجة الطلبات مع استراتيجية التخزين المؤقت
 */
async function handleRequest(request) {
    const url = new URL(request.url);
    
    try {
        // للملفات الثابتة: Cache First
        if (isStaticFile(request)) {
            return await cacheFirst(request);
        }
        
        // للموارد الخارجية: Stale While Revalidate
        if (isExternalResource(request)) {
            return await staleWhileRevalidate(request);
        }
        
        // للطلبات الأخرى: Network First
        return await networkFirst(request);
        
    } catch (error) {
        console.error('❌ Service Worker: خطأ في معالجة الطلب:', error);
        
        // إرجاع صفحة خطأ مخصصة
        if (request.destination === 'document') {
            return await getOfflinePage();
        }
        
        // للموارد الأخرى، إرجاع استجابة فارغة
        return new Response('', { status: 408, statusText: 'Request Timeout' });
    }
}

/**
 * استراتيجية Cache First
 */
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
        const cache = await caches.open(STATIC_CACHE);
        cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
}

/**
 * استراتيجية Network First
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

/**
 * استراتيجية Stale While Revalidate
 */
async function staleWhileRevalidate(request) {
    const cachedResponse = await caches.match(request);
    
    const fetchPromise = fetch(request).then((networkResponse) => {
        if (networkResponse.ok) {
            const cache = caches.open(DYNAMIC_CACHE);
            cache.then(c => c.put(request, networkResponse.clone()));
        }
        return networkResponse;
    });
    
    return cachedResponse || fetchPromise;
}

/**
 * التحقق من كون الملف ثابت
 */
function isStaticFile(request) {
    const url = new URL(request.url);
    const pathname = url.pathname;
    
    return pathname.endsWith('.css') ||
           pathname.endsWith('.js') ||
           pathname.endsWith('.svg') ||
           pathname.endsWith('.png') ||
           pathname.endsWith('.jpg') ||
           pathname.endsWith('.jpeg') ||
           pathname.endsWith('.webp') ||
           pathname === '/' ||
           pathname === '/index.html' ||
           pathname === '/manifest.json';
}

/**
 * التحقق من كون المورد خارجي
 */
function isExternalResource(request) {
    const url = new URL(request.url);
    return url.origin !== self.location.origin;
}

/**
 * الحصول على صفحة عدم الاتصال
 */
async function getOfflinePage() {
    const offlineHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>PDFMerge Pro - غير متصل</title>
            <style>
                body {
                    font-family: 'Cairo', sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
                .offline-content {
                    max-width: 500px;
                    padding: 2rem;
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }
                h1 {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }
                p {
                    font-size: 1.1rem;
                    margin-bottom: 2rem;
                    opacity: 0.9;
                }
                .btn {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: 2px solid white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    text-decoration: none;
                    font-weight: 600;
                    transition: all 0.3s ease;
                }
                .btn:hover {
                    background: white;
                    color: #667eea;
                }
            </style>
        </head>
        <body>
            <div class="offline-content">
                <div class="offline-icon">📡</div>
                <h1>لا يوجد اتصال بالإنترنت</h1>
                <p>
                    لا تقلق! PDFMerge Pro يعمل محلياً على جهازك.
                    يمكنك الاستمرار في استخدام التطبيق بدون إنترنت.
                </p>
                <a href="/" class="btn">العودة للتطبيق</a>
            </div>
        </body>
        </html>
    `;
    
    return new Response(offlineHTML, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
}

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache().then(() => {
                event.ports[0].postMessage({ type: 'CACHE_CLEARED' });
            });
            break;
            
        default:
            console.log('Service Worker: رسالة غير معروفة:', type);
    }
});

/**
 * حساب حجم التخزين المؤقت
 */
async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
    }
    
    return totalSize;
}

/**
 * مسح التخزين المؤقت
 */
async function clearCache() {
    const cacheNames = await caches.keys();
    
    await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
    
    console.log('🗑️ Service Worker: تم مسح جميع التخزين المؤقت');
}

// تسجيل معلومات Service Worker
console.log('🔧 Service Worker: تم تحميل الملف');
console.log('📦 Cache Name:', CACHE_NAME);
console.log('📁 Static Files:', STATIC_FILES.length, 'files');
