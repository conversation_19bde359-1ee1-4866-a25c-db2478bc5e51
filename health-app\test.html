<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار تطبيق صحتي</h1>
        <div id="test-results"></div>
        <button onclick="runTests()">تشغيل الاختبارات</button>
    </div>

    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>نتائج الاختبار:</h3>';
            
            // اختبار تحميل JavaScript
            const tests = [
                { name: 'تحميل Chart.js', test: () => typeof Chart !== 'undefined' },
                { name: 'تحميل HealthStorage', test: () => typeof HealthStorage !== 'undefined' },
                { name: 'تحميل HealthApp', test: () => typeof HealthApp !== 'undefined' },
                { name: 'DOM جاهز', test: () => document.readyState === 'complete' }
            ];
            
            tests.forEach(test => {
                try {
                    const passed = test.test();
                    results.innerHTML += `<p class="${passed ? 'success' : 'error'}">
                        ${test.name}: ${passed ? '✅ نجح' : '❌ فشل'}
                    </p>`;
                } catch (error) {
                    results.innerHTML += `<p class="error">
                        ${test.name}: ❌ خطأ - ${error.message}
                    </p>`;
                }
            });
        }
        
        // تشغيل الاختبارات تلقائياً
        window.addEventListener('load', runTests);
    </script>
    
    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="./js/storage.js"></script>
    <script src="./js/app.js"></script>
</body>
</html>
