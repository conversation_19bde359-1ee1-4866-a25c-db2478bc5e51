/**
 * Health App - Authentication Guard
 * حماية التطبيق من الوصول غير المصرح به
 */

class AuthGuard {
    constructor() {
        this.auth = null;
        this.redirectUrl = 'auth.html';
        this.protectedPages = ['index.html', 'dashboard.html'];
        
        this.init();
    }

    /**
     * تهيئة نظام الحماية
     */
    init() {
        console.log('🛡️ تهيئة نظام حماية التطبيق');
        
        // تهيئة نظام المصادقة
        this.auth = new HealthAuth();
        
        // التحقق من صحة الوصول
        this.checkAccess();
        
        // مراقبة تغييرات الجلسة
        this.monitorSession();
    }

    /**
     * التحقق من صحة الوصول
     */
    checkAccess() {
        const currentPage = this.getCurrentPage();
        
        // إذا كان المستخدم في صفحة المصادقة
        if (currentPage === 'auth.html') {
            // إذا كان مسجل دخول، انتقل للتطبيق
            if (this.auth.isLoggedIn()) {
                this.redirectToApp();
            }
            return;
        }
        
        // إذا كان في صفحة محمية
        if (this.isProtectedPage(currentPage)) {
            // إذا لم يكن مسجل دخول، انتقل لصفحة المصادقة
            if (!this.auth.isLoggedIn()) {
                this.redirectToAuth();
                return;
            }
            
            // تحديث بيانات المستخدم في الواجهة
            this.updateUserInterface();
        }
    }

    /**
     * الحصول على الصفحة الحالية
     */
    getCurrentPage() {
        const path = window.location.pathname;
        return path.split('/').pop() || 'index.html';
    }

    /**
     * التحقق من كون الصفحة محمية
     */
    isProtectedPage(page) {
        return this.protectedPages.includes(page) || page === '';
    }

    /**
     * الانتقال لصفحة المصادقة
     */
    redirectToAuth() {
        console.log('🔒 إعادة توجيه لصفحة المصادقة');
        
        // حفظ الصفحة الحالية للعودة إليها لاحقاً
        sessionStorage.setItem('health_app_return_url', window.location.href);
        
        // الانتقال لصفحة المصادقة
        window.location.href = this.redirectUrl;
    }

    /**
     * الانتقال للتطبيق الرئيسي
     */
    redirectToApp() {
        console.log('✅ إعادة توجيه للتطبيق الرئيسي');
        
        // التحقق من وجود صفحة للعودة إليها
        const returnUrl = sessionStorage.getItem('health_app_return_url');
        if (returnUrl && !returnUrl.includes('auth.html')) {
            sessionStorage.removeItem('health_app_return_url');
            window.location.href = returnUrl;
        } else {
            window.location.href = 'index.html';
        }
    }

    /**
     * تحديث واجهة المستخدم
     */
    updateUserInterface() {
        const user = this.auth.getCurrentUser();
        if (!user) return;

        // تحديث اسم المستخدم
        const userNameElements = document.querySelectorAll('.user-name, #userName');
        userNameElements.forEach(element => {
            element.textContent = user.name;
        });

        // تحديث البريد الإلكتروني
        const userEmailElements = document.querySelectorAll('.user-email, #userEmail');
        userEmailElements.forEach(element => {
            element.textContent = user.email;
        });

        // تحديث صورة المستخدم (إذا كانت متوفرة)
        const userAvatarElements = document.querySelectorAll('.user-avatar, #userAvatar');
        userAvatarElements.forEach(element => {
            if (user.avatar) {
                element.src = user.avatar;
            } else {
                // استخدام صورة افتراضية
                element.src = this.generateAvatarUrl(user.name);
            }
        });

        // إعداد زر تسجيل الخروج
        this.setupLogoutButton();
    }

    /**
     * إعداد زر تسجيل الخروج
     */
    setupLogoutButton() {
        const logoutButtons = document.querySelectorAll('.logout-btn, #logoutBtn');
        logoutButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        });
    }

    /**
     * معالجة تسجيل الخروج
     */
    handleLogout() {
        // عرض تأكيد
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // حفظ البيانات قبل الخروج
            this.saveUserDataBeforeLogout();
            
            // تسجيل الخروج
            this.auth.logout();
        }
    }

    /**
     * حفظ بيانات المستخدم قبل تسجيل الخروج
     */
    saveUserDataBeforeLogout() {
        try {
            // حفظ نسخة احتياطية من البيانات
            HealthStorage.saveCurrentUserData();
            console.log('✅ تم حفظ البيانات قبل تسجيل الخروج');
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
        }
    }

    /**
     * مراقبة تغييرات الجلسة
     */
    monitorSession() {
        // فحص الجلسة كل دقيقة
        setInterval(() => {
            if (this.isProtectedPage(this.getCurrentPage())) {
                if (!this.auth.isLoggedIn()) {
                    console.log('⚠️ انتهت صلاحية الجلسة');
                    this.showSessionExpiredMessage();
                    this.redirectToAuth();
                }
            }
        }, 60000);

        // مراقبة تغييرات التخزين المحلي
        window.addEventListener('storage', (e) => {
            if (e.key === 'health_app_session' && !e.newValue) {
                // تم حذف الجلسة من نافذة أخرى
                console.log('⚠️ تم تسجيل الخروج من نافذة أخرى');
                this.redirectToAuth();
            }
        });

        // مراقبة إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.saveUserDataBeforeLogout();
        });
    }

    /**
     * عرض رسالة انتهاء الجلسة
     */
    showSessionExpiredMessage() {
        // إنشاء إشعار
        const notification = document.createElement('div');
        notification.className = 'session-expired-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>انتهت صلاحية جلستك. سيتم إعادة توجيهك لصفحة تسجيل الدخول.</span>
            </div>
        `;

        // إضافة الأنماط
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f59e0b;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-weight: 500;
            max-width: 350px;
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * توليد رابط صورة افتراضية
     */
    generateAvatarUrl(name) {
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
        const color = colors[name.length % colors.length];
        
        // استخدام خدمة UI Avatars
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color.substring(1)}&color=fff&size=40&font-size=0.6`;
    }

    /**
     * إعداد الحماية للصفحة
     */
    static protect() {
        return new AuthGuard();
    }

    /**
     * التحقق من صحة المصادقة
     */
    static isAuthenticated() {
        const session = localStorage.getItem('health_app_session');
        if (!session) return false;

        try {
            const sessionData = JSON.parse(session);
            return new Date().getTime() < sessionData.expiresAt;
        } catch (error) {
            return false;
        }
    }

    /**
     * الحصول على المستخدم الحالي
     */
    static getCurrentUser() {
        const session = localStorage.getItem('health_app_session');
        if (!session) return null;

        try {
            const sessionData = JSON.parse(session);
            if (new Date().getTime() < sessionData.expiresAt) {
                return sessionData.user;
            }
        } catch (error) {
            console.error('خطأ في قراءة بيانات المستخدم:', error);
        }

        return null;
    }

    /**
     * تسجيل خروج سريع
     */
    static quickLogout() {
        localStorage.removeItem('health_app_session');
        localStorage.removeItem('health_app_remember');
        window.location.href = 'auth.html';
    }
}

// إعداد الدوال العامة
window.showAuthPage = function() {
    window.location.href = 'auth.html';
};

window.showMainApp = function() {
    window.location.href = 'index.html';
};

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.AuthGuard = AuthGuard;
}

// تشغيل الحماية تلقائياً
document.addEventListener('DOMContentLoaded', () => {
    AuthGuard.protect();
});
