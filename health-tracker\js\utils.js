/**
 * Health Tracker - Utility Functions
 * مجموعة الدوال المساعدة للتطبيق الصحي
 */

// ===== DOM UTILITIES =====

/**
 * إنشاء عنصر HTML مع خصائص
 * @param {string} tag - نوع العنصر
 * @param {Object} attributes - الخصائص
 * @param {string} content - المحتوى النصي
 * @returns {HTMLElement} - العنصر المُنشأ
 */
function createElement(tag, attributes = {}, content = '') {
    const element = document.createElement(tag);
    
    Object.keys(attributes).forEach(key => {
        if (key === 'className') {
            element.className = attributes[key];
        } else if (key === 'dataset') {
            Object.keys(attributes[key]).forEach(dataKey => {
                element.dataset[dataKey] = attributes[key][dataKey];
            });
        } else {
            element.setAttribute(key, attributes[key]);
        }
    });
    
    if (content) {
        element.textContent = content;
    }
    
    return element;
}

/**
 * إضافة مستمع أحداث مع إزالة تلقائية
 * @param {HTMLElement} element - العنصر
 * @param {string} event - نوع الحدث
 * @param {Function} handler - دالة المعالجة
 * @param {Object} options - خيارات إضافية
 */
function addEventListenerOnce(element, event, handler, options = {}) {
    const wrappedHandler = (e) => {
        handler(e);
        element.removeEventListener(event, wrappedHandler, options);
    };
    element.addEventListener(event, wrappedHandler, options);
}

/**
 * إضافة فئة CSS مع حركة
 * @param {HTMLElement} element - العنصر
 * @param {string} className - اسم الفئة
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function addClassWithAnimation(element, className, duration = 300) {
    element.classList.add(className);
    setTimeout(() => {
        element.classList.remove(className);
    }, duration);
}

// ===== ANIMATION UTILITIES =====

/**
 * تحريك رقم من قيمة إلى أخرى
 * @param {HTMLElement} element - العنصر المحتوي على الرقم
 * @param {number} start - القيمة الابتدائية
 * @param {number} end - القيمة النهائية
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function animateNumber(element, start, end, duration = 2000) {
    const startTime = performance.now();
    const difference = end - start;
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = start + (difference * easeOut);
        
        element.textContent = Math.round(current);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * تحريك شريط التقدم
 * @param {HTMLElement} progressBar - عنصر شريط التقدم
 * @param {number} percentage - النسبة المئوية (0-100)
 * @param {number} duration - مدة الحركة بالميلي ثانية
 */
function animateProgressBar(progressBar, percentage, duration = 1000) {
    progressBar.style.transition = `width ${duration}ms ease-out`;
    progressBar.style.width = `${percentage}%`;
}

// ===== SCROLL UTILITIES =====

/**
 * مراقب التمرير للكشف عن العناصر
 */
function createScrollObserver() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // تحريك الأرقام إذا كان العنصر يحتوي على data-target
                const target = entry.target.dataset.target;
                if (target) {
                    animateNumber(entry.target, 0, parseInt(target), 2000);
                }
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    return observer;
}

/**
 * تطبيق تأثيرات التمرير على العناصر
 */
function initScrollAnimations() {
    const observer = createScrollObserver();
    
    // العناصر التي تحتاج تأثيرات تمرير
    const scrollElements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right');
    const numberElements = document.querySelectorAll('[data-target]');
    
    scrollElements.forEach(el => observer.observe(el));
    numberElements.forEach(el => observer.observe(el));
}

// ===== STORAGE UTILITIES =====

/**
 * حفظ البيانات في التخزين المحلي
 * @param {string} key - مفتاح التخزين
 * @param {any} data - البيانات المراد حفظها
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.warn('فشل في حفظ البيانات:', error);
    }
}

/**
 * استرجاع البيانات من التخزين المحلي
 * @param {string} key - مفتاح التخزين
 * @param {any} defaultValue - القيمة الافتراضية
 * @returns {any} - البيانات المسترجعة
 */
function getFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.warn('فشل في استرجاع البيانات:', error);
        return defaultValue;
    }
}

/**
 * حذف البيانات من التخزين المحلي
 * @param {string} key - مفتاح التخزين
 */
function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
    } catch (error) {
        console.warn('فشل في حذف البيانات:', error);
    }
}

// ===== HEALTH UTILITIES =====

/**
 * حساب مؤشر كتلة الجسم
 * @param {number} weight - الوزن بالكيلوغرام
 * @param {number} height - الطول بالمتر
 * @returns {number} - مؤشر كتلة الجسم
 */
function calculateBMI(weight, height) {
    return weight / (height * height);
}

/**
 * تصنيف مؤشر كتلة الجسم
 * @param {number} bmi - مؤشر كتلة الجسم
 * @returns {string} - التصنيف
 */
function classifyBMI(bmi) {
    if (bmi < 18.5) return 'نقص في الوزن';
    if (bmi < 25) return 'وزن طبيعي';
    if (bmi < 30) return 'زيادة في الوزن';
    return 'سمنة';
}

/**
 * حساب السعرات الحرارية المطلوبة يومياً
 * @param {number} weight - الوزن بالكيلوغرام
 * @param {number} height - الطول بالسنتيمتر
 * @param {number} age - العمر بالسنوات
 * @param {string} gender - الجنس ('male' أو 'female')
 * @param {string} activity - مستوى النشاط
 * @returns {number} - السعرات الحرارية المطلوبة
 */
function calculateDailyCalories(weight, height, age, gender, activity = 'moderate') {
    let bmr;
    
    // حساب معدل الأيض الأساسي
    if (gender === 'male') {
        bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    } else {
        bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    }
    
    // ضرب معامل النشاط
    const activityFactors = {
        sedentary: 1.2,
        light: 1.375,
        moderate: 1.55,
        active: 1.725,
        very_active: 1.9
    };
    
    return Math.round(bmr * (activityFactors[activity] || 1.55));
}

/**
 * حساب كمية الماء المطلوبة يومياً
 * @param {number} weight - الوزن بالكيلوغرام
 * @param {string} activity - مستوى النشاط
 * @returns {number} - كمية الماء باللتر
 */
function calculateDailyWater(weight, activity = 'moderate') {
    let baseWater = weight * 0.035; // 35ml لكل كيلوغرام
    
    // إضافة ماء إضافي حسب النشاط
    const activityBonus = {
        sedentary: 0,
        light: 0.5,
        moderate: 1,
        active: 1.5,
        very_active: 2
    };
    
    return Math.round((baseWater + (activityBonus[activity] || 1)) * 10) / 10;
}

// ===== PERFORMANCE UTILITIES =====

/**
 * تأخير تنفيذ دالة (Debounce)
 * @param {Function} func - الدالة المراد تأخيرها
 * @param {number} wait - وقت التأخير بالميلي ثانية
 * @returns {Function} - الدالة المُحسنة
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * تحديد معدل تنفيذ دالة (Throttle)
 * @param {Function} func - الدالة المراد تحديد معدلها
 * @param {number} limit - الحد الأدنى للوقت بين التنفيذات
 * @returns {Function} - الدالة المُحسنة
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== DATE UTILITIES =====

/**
 * تنسيق التاريخ بالعربية
 * @param {Date} date - التاريخ
 * @returns {string} - التاريخ المنسق
 */
function formatArabicDate(date) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    };
    
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * حساب الفرق بين تاريخين بالأيام
 * @param {Date} date1 - التاريخ الأول
 * @param {Date} date2 - التاريخ الثاني
 * @returns {number} - الفرق بالأيام
 */
function daysDifference(date1, date2) {
    const timeDiff = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

// ===== EXPORT FOR MODULE SYSTEMS =====
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createElement,
        addEventListenerOnce,
        addClassWithAnimation,
        animateNumber,
        animateProgressBar,
        createScrollObserver,
        initScrollAnimations,
        saveToLocalStorage,
        getFromLocalStorage,
        removeFromLocalStorage,
        calculateBMI,
        classifyBMI,
        calculateDailyCalories,
        calculateDailyWater,
        debounce,
        throttle,
        formatArabicDate,
        daysDifference
    };
}
