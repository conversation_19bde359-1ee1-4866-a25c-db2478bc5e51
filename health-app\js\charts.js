/**
 * Health App - Charts Management
 * إدارة الرسوم البيانية للتطبيق الصحي
 */

class HealthCharts {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: '#10b981',
            secondary: '#3b82f6',
            sleep: '#6366f1',
            water: '#06b6d4',
            exercise: '#f59e0b',
            nutrition: '#10b981',
            success: '#22c55e',
            warning: '#f59e0b',
            error: '#ef4444'
        };
        
        this.init();
    }

    /**
     * تهيئة الرسوم البيانية
     */
    init() {
        // إعداد Chart.js الافتراضي
        Chart.defaults.font.family = 'Cairo, sans-serif';
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#6b7280';
        Chart.defaults.plugins.legend.rtl = true;
        Chart.defaults.plugins.legend.textDirection = 'rtl';
    }

    /**
     * إنشاء رسم بياني دائري للنظرة العامة اليومية
     */
    createTodayOverviewChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        // تدمير الرسم البياني الموجود إذا كان موجوداً
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chartData = {
            labels: ['النوم', 'الماء', 'التمارين', 'التغذية'],
            datasets: [{
                data: [
                    data.sleep || 0,
                    data.water || 0,
                    data.exercise || 0,
                    data.nutrition || 0
                ],
                backgroundColor: [
                    this.colors.sleep,
                    this.colors.water,
                    this.colors.exercise,
                    this.colors.nutrition
                ],
                borderWidth: 0,
                hoverOffset: 10
            }]
        };

        const config = {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 14,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${percentage}%`;
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    /**
     * إنشاء رسم بياني خطي للتقدم الأسبوعي
     */
    createWeeklyProgressChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chartData = {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [
                {
                    label: 'النوم',
                    data: data.sleep || [0, 0, 0, 0, 0, 0, 0],
                    borderColor: this.colors.sleep,
                    backgroundColor: this.colors.sleep + '20',
                    tension: 0.4,
                    fill: false,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'الماء',
                    data: data.water || [0, 0, 0, 0, 0, 0, 0],
                    borderColor: this.colors.water,
                    backgroundColor: this.colors.water + '20',
                    tension: 0.4,
                    fill: false,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'التمارين',
                    data: data.exercise || [0, 0, 0, 0, 0, 0, 0],
                    borderColor: this.colors.exercise,
                    backgroundColor: this.colors.exercise + '20',
                    tension: 0.4,
                    fill: false,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        };

        const config = {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    /**
     * إنشاء رسم بياني شريطي للمقارنة الشهرية
     */
    createMonthlyComparisonChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chartData = {
            labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
            datasets: [
                {
                    label: 'النوم (ساعات)',
                    data: data.sleep || [0, 0, 0, 0],
                    backgroundColor: this.colors.sleep,
                    borderRadius: 8,
                    borderSkipped: false
                },
                {
                    label: 'الماء (لتر)',
                    data: data.water || [0, 0, 0, 0],
                    backgroundColor: this.colors.water,
                    borderRadius: 8,
                    borderSkipped: false
                },
                {
                    label: 'التمارين (دقيقة)',
                    data: data.exercise || [0, 0, 0, 0],
                    backgroundColor: this.colors.exercise,
                    borderRadius: 8,
                    borderSkipped: false
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    /**
     * إنشاء رسم بياني للسعرات الحرارية
     */
    createCaloriesChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chartData = {
            labels: data.labels || ['الإفطار', 'الغداء', 'العشاء', 'وجبات خفيفة'],
            datasets: [{
                data: data.values || [0, 0, 0, 0],
                backgroundColor: [
                    '#ff6b6b',
                    '#4ecdc4',
                    '#45b7d1',
                    '#96ceb4'
                ],
                borderWidth: 0,
                hoverOffset: 10
            }]
        };

        const config = {
            type: 'pie',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#10b981',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                return `${label}: ${value} سعرة`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    /**
     * إنشاء رسم بياني لجودة النوم
     */
    createSleepQualityChart(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chartData = {
            labels: data.labels || ['الأسبوع الماضي'],
            datasets: [
                {
                    label: 'ساعات النوم',
                    data: data.hours || [0],
                    backgroundColor: this.colors.sleep + '40',
                    borderColor: this.colors.sleep,
                    borderWidth: 2,
                    type: 'bar',
                    yAxisID: 'y'
                },
                {
                    label: 'جودة النوم',
                    data: data.quality || [0],
                    backgroundColor: this.colors.primary,
                    borderColor: this.colors.primary,
                    borderWidth: 3,
                    type: 'line',
                    yAxisID: 'y1',
                    tension: 0.4
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'ساعات النوم'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'جودة النوم (%)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    /**
     * تحديث بيانات الرسم البياني
     */
    updateChart(canvasId, newData) {
        const chart = this.charts[canvasId];
        if (!chart) return false;

        chart.data = newData;
        chart.update('active');
        return true;
    }

    /**
     * تدمير رسم بياني
     */
    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
            return true;
        }
        return false;
    }

    /**
     * تدمير جميع الرسوم البيانية
     */
    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }

    /**
     * تحديث ألوان الرسوم البيانية
     */
    updateColors(newColors) {
        this.colors = { ...this.colors, ...newColors };
        
        // تحديث الرسوم البيانية الموجودة
        Object.values(this.charts).forEach(chart => {
            chart.update();
        });
    }

    /**
     * تصدير الرسم البياني كصورة
     */
    exportChart(canvasId, format = 'png') {
        const chart = this.charts[canvasId];
        if (!chart) return null;

        return chart.toBase64Image(format, 1.0);
    }

    /**
     * الحصول على بيانات تجريبية
     */
    getSampleData() {
        return {
            todayOverview: {
                sleep: 85,
                water: 72,
                exercise: 75,
                nutrition: 83
            },
            weeklyProgress: {
                sleep: [7, 8, 6, 7.5, 8, 7, 8.5],
                water: [2.1, 2.5, 1.8, 2.3, 2.7, 2.2, 2.4],
                exercise: [30, 45, 0, 60, 40, 50, 35]
            },
            monthlyComparison: {
                sleep: [7.2, 7.8, 7.5, 8.1],
                water: [2.1, 2.4, 2.3, 2.6],
                exercise: [180, 220, 200, 250]
            },
            calories: {
                labels: ['الإفطار', 'الغداء', 'العشاء', 'وجبات خفيفة'],
                values: [450, 650, 550, 200]
            },
            sleepQuality: {
                labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
                hours: [7, 8, 6, 7.5, 8, 7, 8.5],
                quality: [75, 85, 60, 80, 90, 75, 85]
            }
        };
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthCharts = HealthCharts;
}
