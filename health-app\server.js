const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3001;

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

const server = http.createServer((req, res) => {
    // Parse URL
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Default to index.html
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    // Build file path
    const filePath = path.join(__dirname, pathname);
    
    // Get file extension
    const ext = path.extname(filePath);
    const mimeType = mimeTypes[ext] || 'text/plain';
    
    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File not found
            res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>404 - الصفحة غير موجودة</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            min-height: 100vh;
                            margin: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-direction: column;
                        }
                        h1 { font-size: 3rem; margin-bottom: 1rem; }
                        p { font-size: 1.2rem; margin-bottom: 2rem; }
                        a { 
                            color: white; 
                            text-decoration: none; 
                            background: rgba(255,255,255,0.2);
                            padding: 1rem 2rem;
                            border-radius: 25px;
                            transition: all 0.3s ease;
                        }
                        a:hover { background: rgba(255,255,255,0.3); }
                    </style>
                </head>
                <body>
                    <h1>404</h1>
                    <p>الصفحة المطلوبة غير موجودة</p>
                    <a href="/">العودة للصفحة الرئيسية</a>
                </body>
                </html>
            `);
            return;
        }
        
        // Read and serve file
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('خطأ في الخادم');
                return;
            }
            
            // Set headers
            res.writeHead(200, { 
                'Content-Type': mimeType + '; charset=utf-8',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
                'Access-Control-Allow-Headers': 'Content-Type'
            });
            res.end(data);
        });
    });
});

server.listen(PORT, () => {
    console.log(`🚀 خادم تطبيق صحتي يعمل على http://localhost:${PORT}`);
    console.log(`📱 يمكنك الآن فتح التطبيق في المتصفح`);
    console.log(`🔧 للإيقاف: اضغط Ctrl+C`);
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ المنفذ ${PORT} مستخدم بالفعل`);
        console.log(`💡 جرب منفذ آخر أو أوقف الخادم الآخر`);
    } else {
        console.error('خطأ في الخادم:', err);
    }
});
