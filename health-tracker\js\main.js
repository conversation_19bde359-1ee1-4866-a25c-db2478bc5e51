/**
 * Health Tracker - Main Application
 * التطبيق الرئيسي لتتبع العادات الصحية
 */

class HealthTrackerApp {
    constructor() {
        this.version = '1.0.0';
        this.isInitialized = false;
        this.user = null;
        this.habits = {
            sleep: { target: 8, current: 0, unit: 'ساعة' },
            water: { target: 2.5, current: 0, unit: 'لتر' },
            exercise: { target: 60, current: 0, unit: 'دقيقة' },
            nutrition: { target: 2000, current: 0, unit: 'سعرة' }
        };
        
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    async init() {
        try {
            console.log(`🌟 Health Tracker v${this.version} - بدء التشغيل`);
            
            // تحميل البيانات المحفوظة
            this.loadUserData();
            
            // تهيئة واجهة المستخدم
            this.initializeUI();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // إخفاء شاشة التحميل
            this.hideLoadingScreen();
            
            // تسجيل Service Worker
            await this.registerServiceWorker();
            
            this.isInitialized = true;
            console.log('✅ تم تشغيل التطبيق بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل التطبيق:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * تحميل بيانات المستخدم
     */
    loadUserData() {
        this.user = getFromLocalStorage('health_user', {
            name: '',
            age: 0,
            weight: 0,
            height: 0,
            gender: '',
            activityLevel: 'moderate',
            joinDate: new Date().toISOString()
        });

        const savedHabits = getFromLocalStorage('health_habits');
        if (savedHabits) {
            this.habits = { ...this.habits, ...savedHabits };
        }
    }

    /**
     * حفظ بيانات المستخدم
     */
    saveUserData() {
        saveToLocalStorage('health_user', this.user);
        saveToLocalStorage('health_habits', this.habits);
    }

    /**
     * تهيئة واجهة المستخدم
     */
    initializeUI() {
        // تحديث حلقات التقدم
        this.updateProgressRings();
        
        // تحديث الإحصائيات
        this.updateStats();
        
        // إضافة تأثيرات بصرية
        this.addVisualEffects();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // أزرار الهيدر
        this.setupHeaderButtons();
        
        // أزرار القسم البطولي
        this.setupHeroButtons();
        
        // معالجة النقر على الروابط
        this.setupNavigationLinks();
        
        // معالجة تغيير حجم النافذة
        this.setupWindowEvents();
        
        // اختصارات لوحة المفاتيح
        this.setupKeyboardShortcuts();
    }

    /**
     * إعداد أزرار الهيدر
     */
    setupHeaderButtons() {
        const loginBtn = document.getElementById('loginBtn');
        const signupBtn = document.getElementById('signupBtn');
        const menuToggle = document.getElementById('menuToggle');

        loginBtn?.addEventListener('click', () => {
            this.showLoginModal();
        });

        signupBtn?.addEventListener('click', () => {
            this.showSignupModal();
        });

        menuToggle?.addEventListener('click', () => {
            this.toggleMobileMenu();
        });
    }

    /**
     * إعداد أزرار القسم البطولي
     */
    setupHeroButtons() {
        const startJourneyBtn = document.getElementById('startJourneyBtn');
        const watchDemoBtn = document.getElementById('watchDemoBtn');

        startJourneyBtn?.addEventListener('click', () => {
            this.startHealthJourney();
        });

        watchDemoBtn?.addEventListener('click', () => {
            this.showDemo();
        });
    }

    /**
     * إعداد روابط التنقل
     */
    setupNavigationLinks() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = link.getAttribute('href');
                this.smoothScrollTo(target);
            });
        });
    }

    /**
     * إعداد أحداث النافذة
     */
    setupWindowEvents() {
        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', debounce(() => {
            this.handleWindowResize();
        }, 250));

        // معالجة التمرير
        window.addEventListener('scroll', throttle(() => {
            this.handleScroll();
        }, 16));
    }

    /**
     * إعداد اختصارات لوحة المفاتيح
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter: بدء الرحلة
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.startHealthJourney();
            }
            
            // Escape: إغلاق المودال
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 2000);
        }
    }

    /**
     * تحديث حلقات التقدم
     */
    updateProgressRings() {
        Object.keys(this.habits).forEach(habitKey => {
            const habit = this.habits[habitKey];
            const percentage = Math.min((habit.current / habit.target) * 100, 100);
            
            const progressRing = document.querySelector(`.habit-card.${habitKey} .progress-ring`);
            if (progressRing) {
                progressRing.style.setProperty('--progress', `${percentage}%`);
            }
        });
    }

    /**
     * تحديث الإحصائيات
     */
    updateStats() {
        // حساب الإحصائيات
        const totalHabits = Object.keys(this.habits).length;
        const completedHabits = Object.values(this.habits).filter(
            habit => habit.current >= habit.target
        ).length;
        
        const completionRate = Math.round((completedHabits / totalHabits) * 100);
        
        // تحديث العرض
        const statsElements = {
            users: document.querySelector('[data-target="50000"]'),
            success: document.querySelector('[data-target="95"]'),
            days: document.querySelector('[data-target="30"]')
        };
        
        // تحديث معدل النجاح الشخصي
        if (statsElements.success) {
            statsElements.success.dataset.target = completionRate;
        }
    }

    /**
     * إضافة تأثيرات بصرية
     */
    addVisualEffects() {
        // إضافة تأثير الكتابة للعنوان
        const heroTitle = document.querySelector('.title-highlight');
        if (heroTitle && healthAnimations) {
            setTimeout(() => {
                healthAnimations.addWaveEffect(heroTitle);
            }, 3000);
        }
    }

    /**
     * بدء الرحلة الصحية
     */
    startHealthJourney() {
        this.showNotification('مرحباً بك في رحلتك الصحية! 🌟', 'success');
        
        // التمرير إلى قسم النظرة العامة
        this.smoothScrollTo('#overview');
        
        // حفظ بداية الرحلة
        if (!this.user.journeyStarted) {
            this.user.journeyStarted = new Date().toISOString();
            this.saveUserData();
        }
    }

    /**
     * عرض العرض التوضيحي
     */
    showDemo() {
        this.showNotification('العرض التوضيحي قريباً! 🎬', 'info');
    }

    /**
     * عرض مودال تسجيل الدخول
     */
    showLoginModal() {
        this.showNotification('مودال تسجيل الدخول قريباً! 🔐', 'info');
    }

    /**
     * عرض مودال التسجيل
     */
    showSignupModal() {
        this.showNotification('مودال التسجيل قريباً! 📝', 'info');
    }

    /**
     * تبديل القائمة المحمولة
     */
    toggleMobileMenu() {
        const nav = document.querySelector('.nav');
        if (nav) {
            nav.classList.toggle('mobile-open');
        }
    }

    /**
     * التمرير السلس إلى عنصر
     */
    smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * معالجة تغيير حجم النافذة
     */
    handleWindowResize() {
        // إعادة حساب الحركات
        if (healthAnimations) {
            healthAnimations.optimizeForPerformance();
        }
    }

    /**
     * معالجة التمرير
     */
    handleScroll() {
        const header = document.querySelector('.header');
        if (header) {
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
    }

    /**
     * إغلاق جميع المودالات
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
    }

    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        // إنشاء إشعار بسيط
        const notification = createElement('div', {
            className: `notification notification-${type}`
        });
        
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // إضافة للصفحة
        document.body.appendChild(notification);
        
        // تحريك الدخول
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // الإغلاق التلقائي
        setTimeout(() => {
            this.removeNotification(notification);
        }, 5000);
        
        // معالجة الإغلاق اليدوي
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn?.addEventListener('click', () => {
            this.removeNotification(notification);
        });
    }

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * إزالة إشعار
     */
    removeNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * تسجيل Service Worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('./sw.js');
                console.log('✅ Service Worker مسجل بنجاح');
            } catch (error) {
                console.warn('⚠️ فشل في تسجيل Service Worker:', error);
            }
        }
    }

    /**
     * معالجة خطأ التهيئة
     */
    handleInitializationError(error) {
        const errorContainer = createElement('div', {
            className: 'initialization-error'
        });
        
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>خطأ في تشغيل التطبيق</h2>
                <p>${error.message}</p>
                <button onclick="location.reload()" class="btn btn-primary">
                    إعادة تحميل الصفحة
                </button>
            </div>
        `;
        
        document.body.appendChild(errorContainer);
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        console.log('🧹 تنظيف موارد التطبيق');
        
        // حفظ البيانات
        this.saveUserData();
        
        // تنظيف الحركات
        if (healthAnimations) {
            healthAnimations.cleanup();
        }
    }
}

// إنشاء مثيل التطبيق
let healthApp;

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    healthApp = new HealthTrackerApp();
});

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (healthApp) {
        healthApp.cleanup();
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthTrackerApp = HealthTrackerApp;
}
