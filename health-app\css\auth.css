/**
 * Health App - Authentication Styles
 * أنماط واجهة المصادقة
 */

/* ===== AUTH BODY ===== */
.auth-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
    position: relative;
}

/* ===== BACKGROUND ANIMATION ===== */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* ===== AUTH CONTAINER ===== */
.auth-container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

/* ===== AUTH LOGO ===== */
.auth-logo {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-icon i {
    font-size: 2.5rem;
    color: white;
}

.auth-logo h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-logo p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* ===== AUTH FORMS ===== */
.auth-forms {
    width: 100%;
    max-width: 500px;
    position: relative;
}

.auth-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: none;
    animation: slideIn 0.5s ease-out;
}

.auth-form.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== FORM HEADER ===== */
.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem;
}

.form-header p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

/* ===== FORM STYLES ===== */
.form {
    width: 100%;
}

.form-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: 12px;
    border: 1px solid var(--gray-200);
}

.form-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--gray-400);
    z-index: 2;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    direction: rtl;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.toggle-password {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.5rem;
    z-index: 3;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--gray-600);
}

/* ===== FORM OPTIONS ===== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-family: inherit;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-full {
    width: 100%;
    margin: 1rem 0;
}

/* ===== FORM FOOTER ===== */
.form-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.form-footer p {
    color: var(--gray-600);
    margin: 0;
}

.form-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.form-footer a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-overlay.active {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    margin: 0;
    font-weight: 500;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-form {
        padding: 2rem 1.5rem;
        margin: 0;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .auth-logo h1 {
        font-size: 2rem;
    }
    
    .logo-icon {
        width: 60px;
        height: 60px;
    }
    
    .logo-icon i {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 0.5rem;
    }
    
    .auth-form {
        padding: 1.5rem 1rem;
        border-radius: 15px;
    }
    
    .form-section {
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* ===== LINKS ===== */
.terms-link,
.privacy-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover,
.privacy-link:hover {
    text-decoration: underline;
}

/* ===== ERROR STATES ===== */
.input-group.error input,
.input-group.error select {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
    color: var(--error-color);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message i {
    font-size: 0.7rem;
}

/* ===== SUCCESS STATES ===== */
.input-group.success input,
.input-group.success select {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.success-message {
    color: var(--success-color);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.success-message i {
    font-size: 0.7rem;
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-left: 4px solid var(--primary-color);
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast-message {
    flex: 1;
    color: var(--gray-800);
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}
