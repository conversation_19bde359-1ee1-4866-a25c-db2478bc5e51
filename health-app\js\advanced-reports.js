/**
 * Health App - Advanced Reports & Analytics
 * نظام التقارير المتقدمة والتحليلات
 */

class AdvancedReports {
    constructor() {
        this.reports = {};
        this.analytics = {};
        this.insights = [];
        this.trends = {};
        
        this.init();
    }

    /**
     * تهيئة نظام التقارير المتقدمة
     */
    init() {
        console.log('📊 تهيئة نظام التقارير المتقدمة');
        
        // تحميل البيانات
        this.loadData();
        
        // تحليل البيانات
        this.analyzeData();
        
        // توليد التقارير
        this.generateReports();
        
        // استخراج الرؤى
        this.extractInsights();
    }

    /**
     * تحميل البيانات
     */
    loadData() {
        try {
            this.data = {
                water: HealthStorage.getWaterData() || [],
                sleep: HealthStorage.getSleepData() || [],
                exercise: HealthStorage.getExerciseData() || [],
                nutrition: HealthStorage.getNutritionData() || [],
                goals: HealthStorage.getGoals() || {}
            };
            
            console.log('📈 تم تحميل البيانات للتحليل');
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
        }
    }

    /**
     * تحليل البيانات
     */
    analyzeData() {
        this.analytics = {
            water: this.analyzeWaterData(),
            sleep: this.analyzeSleepData(),
            exercise: this.analyzeExerciseData(),
            nutrition: this.analyzeNutritionData(),
            overall: this.analyzeOverallHealth()
        };
        
        console.log('🔍 تم تحليل البيانات:', this.analytics);
    }

    /**
     * تحليل بيانات الماء
     */
    analyzeWaterData() {
        const waterData = this.data.water;
        if (waterData.length === 0) return this.getEmptyAnalysis('water');

        const analysis = {
            totalEntries: waterData.length,
            averageDaily: 0,
            bestDay: null,
            worstDay: null,
            streak: 0,
            goalAchievement: 0,
            trends: [],
            patterns: {},
            recommendations: []
        };

        // تجميع البيانات حسب اليوم
        const dailyData = this.groupByDay(waterData, 'amount');
        const dailyTotals = Object.values(dailyData);
        
        // حساب المتوسط اليومي
        analysis.averageDaily = dailyTotals.reduce((sum, total) => sum + total, 0) / dailyTotals.length;
        
        // أفضل وأسوأ يوم
        const maxDaily = Math.max(...dailyTotals);
        const minDaily = Math.min(...dailyTotals);
        analysis.bestDay = { amount: maxDaily, date: this.findDateByAmount(dailyData, maxDaily) };
        analysis.worstDay = { amount: minDaily, date: this.findDateByAmount(dailyData, minDaily) };
        
        // حساب السلسلة الحالية
        analysis.streak = this.calculateWaterStreak(dailyData);
        
        // نسبة تحقيق الهدف
        const waterGoal = (this.data.goals.water || 2.5) * 1000; // تحويل إلى مل
        const achievedDays = dailyTotals.filter(total => total >= waterGoal).length;
        analysis.goalAchievement = (achievedDays / dailyTotals.length) * 100;
        
        // تحليل الاتجاهات
        analysis.trends = this.calculateTrends(dailyTotals);
        
        // تحليل الأنماط (الساعات المفضلة)
        analysis.patterns = this.analyzeWaterPatterns(waterData);
        
        // التوصيات
        analysis.recommendations = this.generateWaterRecommendations(analysis);

        return analysis;
    }

    /**
     * تحليل بيانات النوم
     */
    analyzeSleepData() {
        const sleepData = this.data.sleep;
        if (sleepData.length === 0) return this.getEmptyAnalysis('sleep');

        const analysis = {
            totalEntries: sleepData.length,
            averageDuration: 0,
            averageQuality: 0,
            bestNight: null,
            worstNight: null,
            sleepDebt: 0,
            consistency: 0,
            patterns: {},
            recommendations: []
        };

        // حساب متوسط مدة النوم
        const durations = sleepData.map(entry => entry.duration || 0);
        analysis.averageDuration = durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
        
        // حساب متوسط جودة النوم
        const qualityScores = sleepData.map(entry => this.convertQualityToScore(entry.quality));
        analysis.averageQuality = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
        
        // أفضل وأسوأ ليلة
        const maxDuration = Math.max(...durations);
        const minDuration = Math.min(...durations);
        analysis.bestNight = sleepData.find(entry => entry.duration === maxDuration);
        analysis.worstNight = sleepData.find(entry => entry.duration === minDuration);
        
        // حساب دين النوم
        const sleepGoal = this.data.goals.sleep || 8;
        analysis.sleepDebt = sleepData.reduce((debt, entry) => {
            const deficit = sleepGoal - (entry.duration || 0);
            return debt + (deficit > 0 ? deficit : 0);
        }, 0);
        
        // حساب الاتساق
        analysis.consistency = this.calculateSleepConsistency(sleepData);
        
        // تحليل الأنماط
        analysis.patterns = this.analyzeSleepPatterns(sleepData);
        
        // التوصيات
        analysis.recommendations = this.generateSleepRecommendations(analysis);

        return analysis;
    }

    /**
     * تحليل بيانات التمارين
     */
    analyzeExerciseData() {
        const exerciseData = this.data.exercise;
        if (exerciseData.length === 0) return this.getEmptyAnalysis('exercise');

        const analysis = {
            totalEntries: exerciseData.length,
            totalMinutes: 0,
            averageDaily: 0,
            favoriteTypes: [],
            caloriesBurned: 0,
            streak: 0,
            goalAchievement: 0,
            patterns: {},
            recommendations: []
        };

        // حساب إجمالي الدقائق
        analysis.totalMinutes = exerciseData.reduce((sum, entry) => sum + (entry.duration || 0), 0);
        
        // تجميع البيانات حسب اليوم
        const dailyData = this.groupByDay(exerciseData, 'duration');
        const dailyTotals = Object.values(dailyData);
        
        // حساب المتوسط اليومي
        analysis.averageDaily = dailyTotals.reduce((sum, total) => sum + total, 0) / dailyTotals.length;
        
        // الأنواع المفضلة
        analysis.favoriteTypes = this.getFavoriteExerciseTypes(exerciseData);
        
        // حساب السعرات المحروقة (تقدير)
        analysis.caloriesBurned = this.estimateCaloriesBurned(exerciseData);
        
        // حساب السلسلة
        analysis.streak = this.calculateExerciseStreak(dailyData);
        
        // نسبة تحقيق الهدف
        const exerciseGoal = this.data.goals.exercise || 60;
        const achievedDays = dailyTotals.filter(total => total >= exerciseGoal).length;
        analysis.goalAchievement = (achievedDays / dailyTotals.length) * 100;
        
        // تحليل الأنماط
        analysis.patterns = this.analyzeExercisePatterns(exerciseData);
        
        // التوصيات
        analysis.recommendations = this.generateExerciseRecommendations(analysis);

        return analysis;
    }

    /**
     * تحليل بيانات التغذية
     */
    analyzeNutritionData() {
        const nutritionData = this.data.nutrition;
        if (nutritionData.length === 0) return this.getEmptyAnalysis('nutrition');

        const analysis = {
            totalEntries: nutritionData.length,
            averageCalories: 0,
            macroBreakdown: { carbs: 0, protein: 0, fat: 0 },
            mealPatterns: {},
            nutritionScore: 0,
            recommendations: []
        };

        // حساب متوسط السعرات
        const dailyCalories = this.groupByDay(nutritionData, 'calories');
        const calorieValues = Object.values(dailyCalories);
        analysis.averageCalories = calorieValues.reduce((sum, cal) => sum + cal, 0) / calorieValues.length;
        
        // تحليل المغذيات الكبرى
        analysis.macroBreakdown = this.analyzeMacronutrients(nutritionData);
        
        // أنماط الوجبات
        analysis.mealPatterns = this.analyzeMealPatterns(nutritionData);
        
        // نقاط التغذية
        analysis.nutritionScore = this.calculateNutritionScore(analysis);
        
        // التوصيات
        analysis.recommendations = this.generateNutritionRecommendations(analysis);

        return analysis;
    }

    /**
     * تحليل الصحة العامة
     */
    analyzeOverallHealth() {
        const analysis = {
            healthScore: 0,
            strengths: [],
            weaknesses: [],
            improvements: [],
            riskFactors: [],
            achievements: []
        };

        // حساب نقاط الصحة العامة
        const waterScore = this.calculateWaterScore();
        const sleepScore = this.calculateSleepScore();
        const exerciseScore = this.calculateExerciseScore();
        const nutritionScore = this.calculateNutritionScore();
        
        analysis.healthScore = (waterScore + sleepScore + exerciseScore + nutritionScore) / 4;
        
        // تحديد نقاط القوة والضعف
        const scores = { water: waterScore, sleep: sleepScore, exercise: exerciseScore, nutrition: nutritionScore };
        
        Object.entries(scores).forEach(([category, score]) => {
            if (score >= 80) {
                analysis.strengths.push(this.getCategoryName(category));
            } else if (score < 60) {
                analysis.weaknesses.push(this.getCategoryName(category));
            }
        });
        
        // اقتراحات التحسين
        analysis.improvements = this.generateImprovementSuggestions(scores);
        
        // عوامل الخطر
        analysis.riskFactors = this.identifyRiskFactors();
        
        // الإنجازات
        analysis.achievements = this.identifyAchievements();

        return analysis;
    }

    /**
     * توليد التقارير
     */
    generateReports() {
        this.reports = {
            weekly: this.generateWeeklyReport(),
            monthly: this.generateMonthlyReport(),
            yearly: this.generateYearlyReport(),
            custom: this.generateCustomReport()
        };
        
        console.log('📋 تم توليد التقارير');
    }

    /**
     * توليد التقرير الأسبوعي
     */
    generateWeeklyReport() {
        const endDate = new Date();
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        
        return this.generatePeriodReport(startDate, endDate, 'أسبوعي');
    }

    /**
     * توليد التقرير الشهري
     */
    generateMonthlyReport() {
        const endDate = new Date();
        const startDate = new Date(endDate);
        startDate.setMonth(startDate.getMonth() - 1);
        
        return this.generatePeriodReport(startDate, endDate, 'شهري');
    }

    /**
     * توليد تقرير لفترة محددة
     */
    generatePeriodReport(startDate, endDate, period) {
        const filteredData = this.filterDataByPeriod(startDate, endDate);
        
        return {
            period: period,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            summary: this.generateSummary(filteredData),
            achievements: this.getAchievementsForPeriod(filteredData),
            goals: this.getGoalProgressForPeriod(filteredData),
            recommendations: this.getRecommendationsForPeriod(filteredData)
        };
    }

    /**
     * استخراج الرؤى
     */
    extractInsights() {
        this.insights = [
            ...this.getWaterInsights(),
            ...this.getSleepInsights(),
            ...this.getExerciseInsights(),
            ...this.getNutritionInsights(),
            ...this.getOverallInsights()
        ];
        
        // ترتيب الرؤى حسب الأهمية
        this.insights.sort((a, b) => b.priority - a.priority);
        
        console.log('💡 تم استخراج الرؤى:', this.insights.length);
    }

    /**
     * الحصول على رؤى الماء
     */
    getWaterInsights() {
        const insights = [];
        const waterAnalysis = this.analytics.water;
        
        if (waterAnalysis.goalAchievement < 70) {
            insights.push({
                type: 'water',
                priority: 8,
                title: 'تحسين شرب الماء مطلوب',
                description: `تحقق من هدف الماء ${waterAnalysis.goalAchievement.toFixed(1)}% من الوقت فقط`,
                recommendation: 'حاول شرب كوب ماء كل ساعة',
                icon: 'fas fa-tint'
            });
        }
        
        if (waterAnalysis.streak > 7) {
            insights.push({
                type: 'water',
                priority: 6,
                title: 'سلسلة ممتازة في شرب الماء!',
                description: `حافظت على شرب الماء لمدة ${waterAnalysis.streak} أيام متتالية`,
                recommendation: 'استمر على هذا المنوال الرائع!',
                icon: 'fas fa-trophy'
            });
        }
        
        return insights;
    }

    /**
     * الحصول على رؤى النوم
     */
    getSleepInsights() {
        const insights = [];
        const sleepAnalysis = this.analytics.sleep;
        
        if (sleepAnalysis.averageDuration < 7) {
            insights.push({
                type: 'sleep',
                priority: 9,
                title: 'نقص في ساعات النوم',
                description: `متوسط نومك ${sleepAnalysis.averageDuration.toFixed(1)} ساعة، أقل من المطلوب`,
                recommendation: 'حاول النوم مبكراً بـ 30 دقيقة كل ليلة',
                icon: 'fas fa-moon'
            });
        }
        
        if (sleepAnalysis.sleepDebt > 10) {
            insights.push({
                type: 'sleep',
                priority: 7,
                title: 'دين نوم متراكم',
                description: `لديك دين نوم قدره ${sleepAnalysis.sleepDebt.toFixed(1)} ساعة`,
                recommendation: 'خطط لقضاء عطلة نهاية أسبوع للراحة',
                icon: 'fas fa-bed'
            });
        }
        
        return insights;
    }
