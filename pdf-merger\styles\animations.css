/* ===== ADVANCED ANIMATIONS ===== */

/* Animated Background */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-20px) rotate(270deg);
        opacity: 1;
    }
}

@keyframes particleFloat2 {
    0%, 100% {
        transform: translateX(0px) translateY(0px) scale(1);
        opacity: 0.6;
    }
    33% {
        transform: translateX(30px) translateY(-30px) scale(1.2);
        opacity: 0.9;
    }
    66% {
        transform: translateX(-20px) translateY(-60px) scale(0.8);
        opacity: 0.7;
    }
}

@keyframes waveAnimation {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes geometricRotate {
    0% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        opacity: 0.6;
    }
    100% {
        transform: rotate(360deg) scale(1);
        opacity: 0.3;
    }
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes orbit {
    0% {
        transform: rotate(0deg) translateX(50px) rotate(0deg);
    }
    100% {
        transform: rotate(360deg) translateX(50px) rotate(-360deg);
    }
}

@keyframes morphing {
    0%, 100% {
        border-radius: 50%;
        transform: rotate(0deg);
    }
    25% {
        border-radius: 0%;
        transform: rotate(90deg);
    }
    50% {
        border-radius: 50%;
        transform: rotate(180deg);
    }
    75% {
        border-radius: 0%;
        transform: rotate(270deg);
    }
}

/* Loading Animations */
@keyframes loadingPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes loadingDots {
    0%, 20% {
        color: var(--primary-color);
        transform: scale(1);
    }
    50% {
        color: var(--primary-light);
        transform: scale(1.2);
    }
    80%, 100% {
        color: var(--primary-color);
        transform: scale(1);
    }
}

/* File Upload Animations */
@keyframes fileUploadBounce {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    60% {
        opacity: 1;
        transform: translateY(-10px) scale(1.05);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fileDragHover {
    0%, 100% {
        transform: scale(1);
        box-shadow: var(--shadow-lg);
    }
    50% {
        transform: scale(1.02);
        box-shadow: var(--shadow-xl);
    }
}

/* Progress Bar Animations */
@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
    }
}

@keyframes progressShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Success Animations */
@keyframes successCheckmark {
    0% {
        opacity: 0;
        transform: scale(0) rotate(45deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(45deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(45deg);
    }
}

@keyframes successBounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Button Hover Effects */
@keyframes buttonRipple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes buttonGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.6), 0 0 30px rgba(99, 102, 241, 0.4);
    }
}

/* Modal Animations */
@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes modalBackdropFade {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* Drag and Drop Animations */
@keyframes dragPreview {
    0% {
        opacity: 0.8;
        transform: rotate(0deg) scale(1);
    }
    50% {
        opacity: 0.6;
        transform: rotate(2deg) scale(1.05);
    }
    100% {
        opacity: 0.8;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes dropZoneActive {
    0%, 100% {
        border-color: var(--primary-color);
        background-color: rgba(99, 102, 241, 0.05);
    }
    50% {
        border-color: var(--primary-light);
        background-color: rgba(99, 102, 241, 0.1);
    }
}

/* Notification Animations */
@keyframes notificationSlideIn {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notificationSlideOut {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Floating Elements */
@keyframes floatingElement1 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    25% {
        transform: translateY(-10px) rotate(90deg);
        opacity: 0.2;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.1;
    }
    75% {
        transform: translateY(-10px) rotate(270deg);
        opacity: 0.2;
    }
}

@keyframes floatingElement2 {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.1;
    }
    33% {
        transform: translateY(-15px) translateX(10px) scale(1.1);
        opacity: 0.2;
    }
    66% {
        transform: translateY(-5px) translateX(-10px) scale(0.9);
        opacity: 0.15;
    }
}

/* Text Animations */
@keyframes typewriter {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

@keyframes blinkCursor {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--primary-color);
    }
}

@keyframes textGlow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
        text-shadow: 0 0 20px rgba(99, 102, 241, 0.6), 0 0 30px rgba(99, 102, 241, 0.4);
    }
}

/* ===== ANIMATION CLASSES ===== */

/* Loading States */
.loading-pulse {
    animation: loadingPulse 2s ease-in-out infinite;
}

.loading-dots::after {
    content: '...';
    animation: loadingDots 1.5s ease-in-out infinite;
}

/* File Upload States */
.file-upload-bounce {
    animation: fileUploadBounce 0.6s ease-out;
}

.file-drag-hover {
    animation: fileDragHover 0.3s ease-in-out;
}

/* Progress States */
.progress-glow {
    animation: progressGlow 2s ease-in-out infinite;
}

.progress-shimmer {
    background: linear-gradient(
        90deg,
        var(--primary-color) 0%,
        var(--primary-light) 50%,
        var(--primary-color) 100%
    );
    background-size: 200% 100%;
    animation: progressShimmer 2s ease-in-out infinite;
}

/* Success States */
.success-checkmark {
    animation: successCheckmark 0.6s ease-out;
}

.success-bounce {
    animation: successBounce 1s ease-out;
}

/* Button Effects */
.btn-ripple {
    position: relative;
    overflow: hidden;
}

.btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
    width: 300px;
    height: 300px;
}

.btn-glow:hover {
    animation: buttonGlow 2s ease-in-out infinite;
}

/* Modal States */
.modal-slide-in {
    animation: modalSlideIn 0.3s ease-out;
}

.modal-backdrop-fade {
    animation: modalBackdropFade 0.3s ease-out;
}

/* Drag and Drop States */
.drag-preview {
    animation: dragPreview 0.5s ease-in-out infinite;
}

.drop-zone-active {
    animation: dropZoneActive 1s ease-in-out infinite;
}

/* Notification States */
.notification-slide-in {
    animation: notificationSlideIn 0.3s ease-out;
}

.notification-slide-out {
    animation: notificationSlideOut 0.3s ease-in;
}

/* Floating Elements */
.floating-1 {
    animation: floatingElement1 8s ease-in-out infinite;
}

.floating-2 {
    animation: floatingElement2 6s ease-in-out infinite;
}

/* Text Effects */
.typewriter {
    overflow: hidden;
    border-left: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blinkCursor 0.75s step-end infinite;
}

.text-glow {
    animation: textGlow 3s ease-in-out infinite;
}

/* ===== HOVER EFFECTS ===== */
.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-slide-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-slide-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* ===== ANIMATED BACKGROUND CLASSES ===== */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

.background-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.particle-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 10%;
    animation: particleFloat 8s ease-in-out infinite;
    animation-delay: 0s;
}

.particle-2 {
    width: 15px;
    height: 15px;
    top: 60%;
    left: 80%;
    animation: particleFloat2 6s ease-in-out infinite;
    animation-delay: 2s;
}

.particle-3 {
    width: 25px;
    height: 25px;
    top: 80%;
    left: 20%;
    animation: particleFloat 10s ease-in-out infinite;
    animation-delay: 4s;
}

.particle-4 {
    width: 18px;
    height: 18px;
    top: 30%;
    left: 70%;
    animation: particleFloat2 7s ease-in-out infinite;
    animation-delay: 1s;
}

.particle-5 {
    width: 22px;
    height: 22px;
    top: 50%;
    left: 50%;
    animation: particleFloat 9s ease-in-out infinite;
    animation-delay: 3s;
}

.particle-6 {
    width: 16px;
    height: 16px;
    top: 10%;
    left: 60%;
    animation: particleFloat2 11s ease-in-out infinite;
    animation-delay: 5s;
}

.particle-7 {
    width: 28px;
    height: 28px;
    top: 75%;
    left: 75%;
    animation: particleFloat 12s ease-in-out infinite;
    animation-delay: 1.5s;
}

.particle-8 {
    width: 14px;
    height: 14px;
    top: 40%;
    left: 30%;
    animation: particleFloat2 8s ease-in-out infinite;
    animation-delay: 4.5s;
}

.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.geometric-shape {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.shape-triangle {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(255, 255, 255, 0.1);
    border-radius: 0;
    top: 15%;
    right: 15%;
    animation: geometricRotate 12s linear infinite;
}

.shape-square {
    width: 30px;
    height: 30px;
    top: 70%;
    right: 25%;
    animation: geometricRotate 15s linear infinite reverse;
    animation-delay: 2s;
}

.shape-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: 25%;
    right: 60%;
    animation: geometricRotate 10s linear infinite;
    animation-delay: 4s;
}

.shape-hexagon {
    width: 35px;
    height: 35px;
    top: 60%;
    right: 80%;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: morphing 18s ease-in-out infinite;
    animation-delay: 1s;
}

.shape-diamond {
    width: 25px;
    height: 25px;
    top: 80%;
    right: 40%;
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    animation: geometricRotate 14s linear infinite reverse;
    animation-delay: 3s;
}

/* Twinkling Stars */
.twinkling-stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.star {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 100%;
    background: inherit;
}

.star:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    width: 2px;
    height: 100%;
    background: inherit;
}

.star-1 {
    width: 4px;
    height: 4px;
    top: 15%;
    left: 20%;
    animation-delay: 0s;
}

.star-2 {
    width: 3px;
    height: 3px;
    top: 30%;
    left: 80%;
    animation-delay: 1s;
}

.star-3 {
    width: 5px;
    height: 5px;
    top: 70%;
    left: 15%;
    animation-delay: 2s;
}

.star-4 {
    width: 3px;
    height: 3px;
    top: 85%;
    left: 70%;
    animation-delay: 0.5s;
}

.star-5 {
    width: 4px;
    height: 4px;
    top: 50%;
    left: 90%;
    animation-delay: 1.5s;
}

/* Orbiting Elements */
.orbiting-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.orbit-container {
    position: absolute;
    animation: orbit 20s linear infinite;
}

.orbit-1 {
    top: 20%;
    left: 30%;
    animation-duration: 25s;
}

.orbit-2 {
    top: 70%;
    left: 70%;
    animation-duration: 30s;
    animation-direction: reverse;
}

.orbiting-element {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.wave-animation {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: waveAnimation 8s linear infinite;
    z-index: -1;
}

/* Floating Bubbles */
@keyframes bubbleFloat {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

.floating-bubbles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.bubble {
    position: absolute;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    animation: bubbleFloat linear infinite;
}

.bubble-1 {
    width: 40px;
    height: 40px;
    left: 10%;
    animation-duration: 15s;
    animation-delay: 0s;
}

.bubble-2 {
    width: 60px;
    height: 60px;
    left: 30%;
    animation-duration: 20s;
    animation-delay: 5s;
}

.bubble-3 {
    width: 30px;
    height: 30px;
    left: 60%;
    animation-duration: 18s;
    animation-delay: 10s;
}

.bubble-4 {
    width: 50px;
    height: 50px;
    left: 80%;
    animation-duration: 22s;
    animation-delay: 3s;
}

.bubble-5 {
    width: 35px;
    height: 35px;
    left: 45%;
    animation-duration: 16s;
    animation-delay: 8s;
}

/* Animated Gradient Overlay */
.gradient-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(102, 126, 234, 0.1) 0%,
        transparent 25%,
        transparent 50%,
        rgba(118, 75, 162, 0.1) 75%,
        transparent 100%
    );
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    z-index: -1;
    pointer-events: none;
}

/* Animated Grid */
@keyframes gridMove {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        transform: translateX(100vw);
        opacity: 0;
    }
}

.animated-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    height: 1px;
    width: 200%;
    animation: gridMove linear infinite;
}

.grid-line-1 {
    top: 20%;
    animation-duration: 25s;
    animation-delay: 0s;
}

.grid-line-2 {
    top: 40%;
    animation-duration: 30s;
    animation-delay: 5s;
}

.grid-line-3 {
    top: 60%;
    animation-duration: 35s;
    animation-delay: 10s;
}

.grid-line-4 {
    top: 80%;
    animation-duration: 28s;
    animation-delay: 15s;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-element,
    .animated-background,
    .particle,
    .geometric-shape,
    .wave-animation {
        animation: none !important;
    }

    .animated-background {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .loading-spinner {
        animation: none;
        border: 4px solid var(--gray-200);
        border-top: 4px solid var(--primary-color);
    }
}
