/**
 * Health App - Achievements & Challenges System
 * نظام الإنجازات والتحديات
 */

class AchievementsSystem {
    constructor() {
        this.achievements = [];
        this.challenges = [];
        this.userProgress = {};
        this.points = 0;
        this.level = 1;
        this.badges = [];
        
        this.init();
    }

    /**
     * تهيئة نظام الإنجازات
     */
    init() {
        console.log('🏆 تهيئة نظام الإنجازات والتحديات');
        
        // تحميل البيانات
        this.loadUserProgress();
        
        // تعريف الإنجازات
        this.defineAchievements();
        
        // تعريف التحديات
        this.defineChallenges();
        
        // فحص الإنجازات الجديدة
        this.checkAchievements();
        
        // إعداد التحديات النشطة
        this.setupActiveChallenges();
    }

    /**
     * تحميل تقدم المستخدم
     */
    loadUserProgress() {
        try {
            const saved = localStorage.getItem('health_app_achievements_progress');
            if (saved) {
                const data = JSON.parse(saved);
                this.userProgress = data.progress || {};
                this.points = data.points || 0;
                this.level = data.level || 1;
                this.badges = data.badges || [];
            }
        } catch (error) {
            console.error('خطأ في تحميل تقدم الإنجازات:', error);
        }
    }

    /**
     * حفظ تقدم المستخدم
     */
    saveUserProgress() {
        try {
            const data = {
                progress: this.userProgress,
                points: this.points,
                level: this.level,
                badges: this.badges,
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem('health_app_achievements_progress', JSON.stringify(data));
        } catch (error) {
            console.error('خطأ في حفظ تقدم الإنجازات:', error);
        }
    }

    /**
     * تعريف الإنجازات
     */
    defineAchievements() {
        this.achievements = [
            // إنجازات الماء
            {
                id: 'water_first_glass',
                category: 'water',
                title: 'أول كوب ماء',
                description: 'اشرب أول كوب ماء في التطبيق',
                icon: 'fas fa-tint',
                points: 10,
                condition: (data) => data.water.length >= 1
            },
            {
                id: 'water_daily_goal',
                category: 'water',
                title: 'هدف الماء اليومي',
                description: 'حقق هدف الماء اليومي',
                icon: 'fas fa-trophy',
                points: 25,
                condition: (data) => this.checkDailyWaterGoal(data.water)
            },
            {
                id: 'water_week_streak',
                category: 'water',
                title: 'أسبوع من الترطيب',
                description: 'حقق هدف الماء لمدة 7 أيام متتالية',
                icon: 'fas fa-medal',
                points: 100,
                condition: (data) => this.checkWaterStreak(data.water, 7)
            },
            {
                id: 'water_month_streak',
                category: 'water',
                title: 'شهر من الترطيب المثالي',
                description: 'حقق هدف الماء لمدة 30 يوم متتالية',
                icon: 'fas fa-crown',
                points: 500,
                condition: (data) => this.checkWaterStreak(data.water, 30)
            },

            // إنجازات النوم
            {
                id: 'sleep_first_night',
                category: 'sleep',
                title: 'ليلة نوم أولى',
                description: 'سجل أول ليلة نوم في التطبيق',
                icon: 'fas fa-moon',
                points: 10,
                condition: (data) => data.sleep.length >= 1
            },
            {
                id: 'sleep_perfect_night',
                category: 'sleep',
                title: 'ليلة نوم مثالية',
                description: 'احصل على 8 ساعات نوم أو أكثر',
                icon: 'fas fa-bed',
                points: 30,
                condition: (data) => data.sleep.some(entry => entry.duration >= 8)
            },
            {
                id: 'sleep_week_consistency',
                category: 'sleep',
                title: 'أسبوع من النوم المنتظم',
                description: 'حافظ على نوم منتظم لمدة أسبوع',
                icon: 'fas fa-clock',
                points: 150,
                condition: (data) => this.checkSleepConsistency(data.sleep, 7)
            },

            // إنجازات التمارين
            {
                id: 'exercise_first_workout',
                category: 'exercise',
                title: 'أول تمرين',
                description: 'سجل أول تمرين في التطبيق',
                icon: 'fas fa-dumbbell',
                points: 15,
                condition: (data) => data.exercise.length >= 1
            },
            {
                id: 'exercise_daily_goal',
                category: 'exercise',
                title: 'هدف التمرين اليومي',
                description: 'حقق هدف التمرين اليومي',
                icon: 'fas fa-running',
                points: 40,
                condition: (data) => this.checkDailyExerciseGoal(data.exercise)
            },
            {
                id: 'exercise_week_warrior',
                category: 'exercise',
                title: 'محارب الأسبوع',
                description: 'مارس الرياضة 5 أيام في الأسبوع',
                icon: 'fas fa-fire',
                points: 200,
                condition: (data) => this.checkWeeklyExerciseGoal(data.exercise, 5)
            },

            // إنجازات التغذية
            {
                id: 'nutrition_first_meal',
                category: 'nutrition',
                title: 'أول وجبة',
                description: 'سجل أول وجبة في التطبيق',
                icon: 'fas fa-apple-alt',
                points: 10,
                condition: (data) => data.nutrition.length >= 1
            },
            {
                id: 'nutrition_balanced_day',
                category: 'nutrition',
                title: 'يوم متوازن',
                description: 'حقق توازن غذائي مثالي في يوم واحد',
                icon: 'fas fa-balance-scale',
                points: 50,
                condition: (data) => this.checkBalancedNutrition(data.nutrition)
            },

            // إنجازات عامة
            {
                id: 'first_week',
                category: 'general',
                title: 'الأسبوع الأول',
                description: 'استخدم التطبيق لمدة أسبوع كامل',
                icon: 'fas fa-calendar-week',
                points: 75,
                condition: (data) => this.checkAppUsage(7)
            },
            {
                id: 'perfect_day',
                category: 'general',
                title: 'يوم مثالي',
                description: 'حقق جميع أهدافك في يوم واحد',
                icon: 'fas fa-star',
                points: 100,
                condition: (data) => this.checkPerfectDay(data)
            },
            {
                id: 'level_up_5',
                category: 'general',
                title: 'المستوى الخامس',
                description: 'وصل إلى المستوى 5',
                icon: 'fas fa-level-up-alt',
                points: 0,
                condition: () => this.level >= 5
            }
        ];
    }

    /**
     * تعريف التحديات
     */
    defineChallenges() {
        this.challenges = [
            // تحديات يومية
            {
                id: 'daily_water_challenge',
                type: 'daily',
                title: 'تحدي الماء اليومي',
                description: 'اشرب 8 أكواب ماء اليوم',
                target: 8,
                current: 0,
                reward: 20,
                icon: 'fas fa-tint',
                deadline: this.getEndOfDay(),
                condition: (data) => this.getTodayWaterGlasses(data.water) >= 8
            },
            {
                id: 'daily_steps_challenge',
                type: 'daily',
                title: 'تحدي الخطوات اليومي',
                description: 'امش 10,000 خطوة اليوم',
                target: 10000,
                current: 0,
                reward: 30,
                icon: 'fas fa-walking',
                deadline: this.getEndOfDay(),
                condition: (data) => this.getTodaySteps(data.exercise) >= 10000
            },

            // تحديات أسبوعية
            {
                id: 'weekly_exercise_challenge',
                type: 'weekly',
                title: 'تحدي التمرين الأسبوعي',
                description: 'مارس الرياضة 5 أيام هذا الأسبوع',
                target: 5,
                current: 0,
                reward: 100,
                icon: 'fas fa-dumbbell',
                deadline: this.getEndOfWeek(),
                condition: (data) => this.getWeeklyExerciseDays(data.exercise) >= 5
            },
            {
                id: 'weekly_sleep_challenge',
                type: 'weekly',
                title: 'تحدي النوم الأسبوعي',
                description: 'احصل على 8 ساعات نوم كل ليلة هذا الأسبوع',
                target: 7,
                current: 0,
                reward: 150,
                icon: 'fas fa-moon',
                deadline: this.getEndOfWeek(),
                condition: (data) => this.getWeeklySleepGoals(data.sleep) >= 7
            },

            // تحديات شهرية
            {
                id: 'monthly_consistency_challenge',
                type: 'monthly',
                title: 'تحدي الاتساق الشهري',
                description: 'حقق جميع أهدافك لمدة 20 يوم هذا الشهر',
                target: 20,
                current: 0,
                reward: 500,
                icon: 'fas fa-calendar-alt',
                deadline: this.getEndOfMonth(),
                condition: (data) => this.getMonthlyPerfectDays(data) >= 20
            }
        ];
    }

    /**
     * فحص الإنجازات الجديدة
     */
    checkAchievements() {
        const data = {
            water: HealthStorage.getWaterData() || [],
            sleep: HealthStorage.getSleepData() || [],
            exercise: HealthStorage.getExerciseData() || [],
            nutrition: HealthStorage.getNutritionData() || []
        };

        const newAchievements = [];

        this.achievements.forEach(achievement => {
            // تحقق من عدم حصول المستخدم على هذا الإنجاز مسبقاً
            if (!this.userProgress[achievement.id]) {
                // فحص شرط الإنجاز
                if (achievement.condition(data)) {
                    this.unlockAchievement(achievement);
                    newAchievements.push(achievement);
                }
            }
        });

        // عرض الإنجازات الجديدة
        if (newAchievements.length > 0) {
            this.showAchievementNotifications(newAchievements);
        }
    }

    /**
     * فتح إنجاز جديد
     */
    unlockAchievement(achievement) {
        // تسجيل الإنجاز
        this.userProgress[achievement.id] = {
            unlockedAt: new Date().toISOString(),
            points: achievement.points
        };

        // إضافة النقاط
        this.addPoints(achievement.points);

        // إضافة الشارة
        this.badges.push({
            id: achievement.id,
            title: achievement.title,
            icon: achievement.icon,
            unlockedAt: new Date().toISOString()
        });

        // حفظ التقدم
        this.saveUserProgress();

        console.log(`🏆 إنجاز جديد: ${achievement.title} (+${achievement.points} نقطة)`);
    }

    /**
     * إضافة نقاط
     */
    addPoints(points) {
        this.points += points;
        
        // فحص ترقية المستوى
        const newLevel = this.calculateLevel(this.points);
        if (newLevel > this.level) {
            this.levelUp(newLevel);
        }
    }

    /**
     * حساب المستوى
     */
    calculateLevel(points) {
        // كل 100 نقطة = مستوى جديد
        return Math.floor(points / 100) + 1;
    }

    /**
     * ترقية المستوى
     */
    levelUp(newLevel) {
        const oldLevel = this.level;
        this.level = newLevel;
        
        // عرض إشعار ترقية المستوى
        this.showLevelUpNotification(oldLevel, newLevel);
        
        console.log(`⬆️ ترقية المستوى: ${oldLevel} → ${newLevel}`);
    }

    /**
     * عرض إشعارات الإنجازات
     */
    showAchievementNotifications(achievements) {
        achievements.forEach((achievement, index) => {
            setTimeout(() => {
                this.showAchievementPopup(achievement);
            }, index * 1000); // تأخير ثانية واحدة بين كل إنجاز
        });
    }

    /**
     * عرض نافذة الإنجاز
     */
    showAchievementPopup(achievement) {
        const popup = document.createElement('div');
        popup.className = 'achievement-popup';
        popup.innerHTML = `
            <div class="achievement-content">
                <div class="achievement-header">
                    <i class="${achievement.icon} achievement-icon"></i>
                    <h3>إنجاز جديد!</h3>
                </div>
                <div class="achievement-body">
                    <h4>${achievement.title}</h4>
                    <p>${achievement.description}</p>
                    <div class="achievement-points">+${achievement.points} نقطة</div>
                </div>
                <button class="achievement-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // إضافة الأنماط
        popup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 10000;
            text-align: center;
            min-width: 300px;
            animation: achievementPopIn 0.5s ease-out forwards;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        `;

        // إضافة الأنماط للعناصر الفرعية
        const style = document.createElement('style');
        style.textContent = `
            @keyframes achievementPopIn {
                to { transform: translate(-50%, -50%) scale(1); }
            }
            .achievement-icon {
                font-size: 3rem;
                margin-bottom: 1rem;
                color: #fbbf24;
            }
            .achievement-header h3 {
                margin: 0;
                font-size: 1.5rem;
                font-weight: 700;
            }
            .achievement-body h4 {
                margin: 1rem 0 0.5rem;
                font-size: 1.2rem;
                color: #fbbf24;
            }
            .achievement-body p {
                margin: 0 0 1rem;
                opacity: 0.9;
            }
            .achievement-points {
                background: rgba(255,255,255,0.2);
                padding: 0.5rem 1rem;
                border-radius: 25px;
                font-weight: 600;
                display: inline-block;
            }
            .achievement-close {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                opacity: 0.7;
                transition: opacity 0.3s;
            }
            .achievement-close:hover {
                opacity: 1;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(popup);

        // إزالة النافذة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (popup.parentNode) {
                popup.style.animation = 'achievementPopIn 0.3s ease-in reverse';
                setTimeout(() => {
                    popup.remove();
                }, 300);
            }
        }, 5000);
    }

    /**
     * عرض إشعار ترقية المستوى
     */
    showLevelUpNotification(oldLevel, newLevel) {
        if (window.SmartNotifications) {
            const notifications = new SmartNotifications();
            notifications.showInAppNotification({
                title: `🎉 ترقية المستوى!`,
                body: `تهانينا! وصلت إلى المستوى ${newLevel}`,
                type: 'achievement'
            });
        }
    }

    /**
     * إعداد التحديات النشطة
     */
    setupActiveChallenges() {
        // تحديث تقدم التحديات
        this.updateChallengeProgress();
        
        // فحص التحديات المكتملة
        this.checkCompletedChallenges();
        
        // إنشاء تحديات جديدة إذا لزم الأمر
        this.createNewChallenges();
    }

    /**
     * تحديث تقدم التحديات
     */
    updateChallengeProgress() {
        const data = {
            water: HealthStorage.getWaterData() || [],
            sleep: HealthStorage.getSleepData() || [],
            exercise: HealthStorage.getExerciseData() || [],
            nutrition: HealthStorage.getNutritionData() || []
        };

        this.challenges.forEach(challenge => {
            if (challenge.condition) {
                const isCompleted = challenge.condition(data);
                challenge.completed = isCompleted;
                
                // تحديث التقدم الحالي
                switch (challenge.id) {
                    case 'daily_water_challenge':
                        challenge.current = this.getTodayWaterGlasses(data.water);
                        break;
                    case 'weekly_exercise_challenge':
                        challenge.current = this.getWeeklyExerciseDays(data.exercise);
                        break;
                    // إضافة المزيد من التحديات حسب الحاجة
                }
            }
        });
    }

    /**
     * فحص التحديات المكتملة
     */
    checkCompletedChallenges() {
        this.challenges.forEach(challenge => {
            if (challenge.completed && !challenge.rewarded) {
                this.completeChallenge(challenge);
            }
        });
    }

    /**
     * إكمال تحدي
     */
    completeChallenge(challenge) {
        // إضافة النقاط
        this.addPoints(challenge.reward);
        
        // تسجيل إكمال التحدي
        challenge.rewarded = true;
        challenge.completedAt = new Date().toISOString();
        
        // عرض إشعار
        if (window.SmartNotifications) {
            const notifications = new SmartNotifications();
            notifications.showInAppNotification({
                title: `🎯 تحدي مكتمل!`,
                body: `${challenge.title} - مكافأة: ${challenge.reward} نقطة`,
                type: 'achievement'
            });
        }
        
        console.log(`🎯 تحدي مكتمل: ${challenge.title} (+${challenge.reward} نقطة)`);
    }
