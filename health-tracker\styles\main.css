/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Health-focused Color Palette */
    --primary-color: #10b981;
    --primary-dark: #059669;
    --primary-light: #34d399;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Health Theme Colors */
    --sleep-color: #6366f1;
    --water-color: #06b6d4;
    --exercise-color: #f59e0b;
    --nutrition-color: #10b981;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-health: linear-gradient(135deg, #10b981 0%, #06b6d4 50%, #3b82f6 100%);
    --gradient-warm: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-health: 0 10px 30px rgba(16, 185, 129, 0.3);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Typography */
    --font-primary: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Amiri', Georgia, serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--white);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
    position: relative;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--gray-600);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* ===== LAYOUT ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-xl);
    }
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    line-height: 1.5;
    border: 2px solid transparent;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-health);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
    backdrop-filter: blur(10px);
}

.btn-outline:hover:not(:disabled) {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* ===== ANIMATED BACKGROUND ===== */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
        rgba(16, 185, 129, 0.1) 0%, 
        rgba(6, 182, 212, 0.1) 25%, 
        rgba(59, 130, 246, 0.1) 50%, 
        rgba(99, 102, 241, 0.1) 75%, 
        rgba(16, 185, 129, 0.1) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-slow);
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.loading-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: var(--spacing-xl);
}

.loading-logo i {
    font-size: var(--font-size-4xl);
    animation: heartbeat 1.5s ease-in-out infinite;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(16, 185, 129, 0.1);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
}

.logo i {
    font-size: var(--font-size-2xl);
    animation: heartbeat 2s ease-in-out infinite;
}

.logo-tagline {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    font-weight: 400;
}

.nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-700);
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.menu-toggle span {
    width: 20px;
    height: 2px;
    background: var(--gray-700);
    transition: all var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    padding: 140px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: var(--spacing-xl);
    color: var(--gray-900);
}

.title-line {
    display: block;
    color: var(--gray-700);
}

.title-highlight {
    display: block;
    background: var(--gradient-health);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textGlow 3s ease-in-out infinite;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.8;
    font-weight: 400;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-3xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-3xl);
    flex-wrap: wrap;
}

.hero-trust {
    text-align: center;
}

.hero-trust p {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-lg);
}

.trust-badges {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.trust-badge i {
    color: var(--primary-color);
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: var(--z-tooltip);
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    border-left: 4px solid var(--primary-color);
}

.notification.show {
    transform: translateX(0);
}

.notification.hide {
    transform: translateX(100%);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-error {
    border-left-color: var(--error-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-info {
    border-left-color: var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.notification-content i {
    font-size: var(--font-size-lg);
}

.notification-success .notification-content i {
    color: var(--success-color);
}

.notification-error .notification-content i {
    color: var(--error-color);
}

.notification-warning .notification-content i {
    color: var(--warning-color);
}

.notification-info .notification-content i {
    color: var(--info-color);
}

.notification-close {
    position: absolute;
    top: 10px;
    left: 10px;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.notification-close:hover {
    color: var(--gray-600);
}

/* ===== HEADER SCROLLED STATE ===== */
.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-md);
}

/* ===== MOBILE MENU ===== */
.nav.mobile-open {
    display: flex;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    flex-direction: column;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-dropdown);
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-sm); }
.p-2 { padding: var(--spacing-md); }
.p-3 { padding: var(--spacing-lg); }
.p-4 { padding: var(--spacing-xl); }

.hidden { display: none; }
.visible { display: block; }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

/* ===== ANIMATIONS DISABLED ===== */
.animations-disabled * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* ===== PRINT STYLES ===== */
@media print {
    .header,
    .footer,
    .floating-particles,
    .health-icons,
    .pulse-rings,
    .animated-background {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero,
    .overview,
    .features {
        page-break-inside: avoid;
    }
}
