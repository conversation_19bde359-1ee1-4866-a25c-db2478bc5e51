/**
 * Health Tracker - Advanced Animations Controller
 * متحكم الحركات المتقدمة للتطبيق الصحي
 */

class HealthAnimations {
    constructor() {
        this.isInitialized = false;
        this.animationFrameId = null;
        this.particles = [];
        this.healthIcons = [];
        
        this.init();
    }

    /**
     * تهيئة نظام الحركات
     */
    init() {
        if (this.isInitialized) return;
        
        this.setupParticleSystem();
        this.setupHealthIconsAnimation();
        this.setupProgressRings();
        this.setupScrollAnimations();
        this.setupHoverEffects();
        
        this.isInitialized = true;
        console.log('✨ نظام الحركات الصحية جاهز');
    }

    /**
     * إعداد نظام الجسيمات
     */
    setupParticleSystem() {
        const particlesContainer = document.querySelector('.floating-particles');
        if (!particlesContainer) return;

        // إنشاء جسيمات إضافية ديناميكياً
        for (let i = 0; i < 10; i++) {
            const particle = this.createParticle();
            particlesContainer.appendChild(particle);
            this.particles.push(particle);
        }

        // بدء حركة الجسيمات
        this.animateParticles();
    }

    /**
     * إنشاء جسيم جديد
     */
    createParticle() {
        const particle = createElement('div', {
            className: 'particle dynamic-particle'
        });

        // خصائص عشوائية للجسيم
        const size = Math.random() * 15 + 10; // 10-25px
        const left = Math.random() * 100; // 0-100%
        const animationDuration = Math.random() * 10 + 15; // 15-25s
        const delay = Math.random() * 5; // 0-5s

        particle.style.cssText = `
            width: ${size}px;
            height: ${size}px;
            left: ${left}%;
            animation-duration: ${animationDuration}s;
            animation-delay: ${delay}s;
        `;

        return particle;
    }

    /**
     * تحريك الجسيمات
     */
    animateParticles() {
        this.particles.forEach((particle, index) => {
            // إضافة حركة إضافية للجسيمات
            const floatAnimation = `floatUp ${15 + index * 2}s linear infinite`;
            particle.style.animation = floatAnimation;
        });
    }

    /**
     * إعداد حركة الأيقونات الصحية
     */
    setupHealthIconsAnimation() {
        const healthIcons = document.querySelectorAll('.health-icon');
        
        healthIcons.forEach((icon, index) => {
            // إضافة تأثير hover
            icon.addEventListener('mouseenter', () => {
                icon.style.transform = 'scale(1.5) rotate(360deg)';
                icon.style.color = this.getHealthColor(index);
            });

            icon.addEventListener('mouseleave', () => {
                icon.style.transform = 'scale(1) rotate(0deg)';
                icon.style.color = '';
            });

            // حركة عشوائية
            setInterval(() => {
                this.randomHealthIconMovement(icon);
            }, 3000 + index * 1000);
        });
    }

    /**
     * حركة عشوائية للأيقونات الصحية
     */
    randomHealthIconMovement(icon) {
        const randomX = (Math.random() - 0.5) * 100;
        const randomY = (Math.random() - 0.5) * 100;
        
        icon.style.transform = `translate(${randomX}px, ${randomY}px) scale(1.2)`;
        
        setTimeout(() => {
            icon.style.transform = 'translate(0, 0) scale(1)';
        }, 1000);
    }

    /**
     * الحصول على لون صحي
     */
    getHealthColor(index) {
        const colors = [
            '#ef4444', // أحمر للقلب
            '#10b981', // أخضر للورقة
            '#6366f1', // بنفسجي للقمر
            '#f59e0b', // برتقالي للدمبل
            '#10b981'  // أخضر للتفاحة
        ];
        return colors[index % colors.length];
    }

    /**
     * إعداد حلقات التقدم
     */
    setupProgressRings() {
        const progressRings = document.querySelectorAll('.progress-ring');
        
        progressRings.forEach(ring => {
            this.animateProgressRing(ring);
        });
    }

    /**
     * تحريك حلقة التقدم
     */
    animateProgressRing(ring) {
        const progress = ring.style.getPropertyValue('--progress') || '0%';
        const percentage = parseInt(progress);
        
        // تحريك تدريجي للتقدم
        let currentProgress = 0;
        const targetProgress = percentage;
        const duration = 2000; // 2 ثانية
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOut = 1 - Math.pow(1 - progress, 3);
            currentProgress = targetProgress * easeOut;
            
            ring.style.setProperty('--progress', `${currentProgress}%`);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    /**
     * إعداد حركات التمرير
     */
    setupScrollAnimations() {
        // إضافة فئات التمرير للعناصر
        const elementsToAnimate = [
            { selector: '.overview-paragraph', class: 'scroll-reveal' },
            { selector: '.pillar', class: 'scroll-reveal-left' },
            { selector: '.hero-stats .stat-item', class: 'scroll-reveal-right' }
        ];

        elementsToAnimate.forEach(({ selector, class: className }) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((el, index) => {
                el.classList.add(className);
                el.style.animationDelay = `${index * 0.2}s`;
            });
        });

        // تهيئة مراقب التمرير
        initScrollAnimations();
    }

    /**
     * إعداد تأثيرات الـ hover
     */
    setupHoverEffects() {
        // تأثيرات الأزرار
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            this.addButtonEffects(btn);
        });

        // تأثيرات البطاقات
        const cards = document.querySelectorAll('.pillar, .habit-card');
        cards.forEach(card => {
            this.addCardEffects(card);
        });
    }

    /**
     * إضافة تأثيرات للأزرار
     */
    addButtonEffects(button) {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-3px) scale(1.05)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0) scale(1)';
        });

        // تأثير النقر
        button.addEventListener('mousedown', () => {
            button.style.transform = 'translateY(0) scale(0.98)';
        });

        button.addEventListener('mouseup', () => {
            button.style.transform = 'translateY(-3px) scale(1.05)';
        });
    }

    /**
     * إضافة تأثيرات للبطاقات
     */
    addCardEffects(card) {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) rotateY(5deg)';
            card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) rotateY(0deg)';
            card.style.boxShadow = '';
        });
    }

    /**
     * تحريك الهاتف في القسم البطولي
     */
    animatePhoneMockup() {
        const phone = document.querySelector('.phone-mockup');
        if (!phone) return;

        // حركة تفاعلية مع الماوس
        document.addEventListener('mousemove', (e) => {
            const { clientX, clientY } = e;
            const { innerWidth, innerHeight } = window;
            
            const xRotation = (clientY / innerHeight - 0.5) * 20;
            const yRotation = (clientX / innerWidth - 0.5) * 20;
            
            phone.style.transform = `
                perspective(1000px) 
                rotateX(${xRotation}deg) 
                rotateY(${yRotation}deg)
                translateY(-20px)
            `;
        });

        // إعادة تعيين عند مغادرة الماوس
        document.addEventListener('mouseleave', () => {
            phone.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(0px)';
        });
    }

    /**
     * تحريك الإحصائيات في القسم البطولي
     */
    animateHeroStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        
        statNumbers.forEach(stat => {
            const target = parseInt(stat.dataset.target);
            if (target) {
                // تأخير عشوائي لكل إحصائية
                const delay = Math.random() * 1000;
                setTimeout(() => {
                    animateNumber(stat, 0, target, 3000);
                }, delay);
            }
        });
    }

    /**
     * إضافة تأثيرات الكتابة
     */
    addTypingEffect(element, text, speed = 100) {
        element.textContent = '';
        let i = 0;
        
        const typeWriter = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, speed);
            }
        };
        
        typeWriter();
    }

    /**
     * تأثير الموجة للنص
     */
    addWaveEffect(element) {
        const text = element.textContent;
        element.innerHTML = '';
        
        [...text].forEach((char, index) => {
            const span = createElement('span', {
                style: `animation-delay: ${index * 0.1}s`
            }, char);
            span.classList.add('wave-char');
            element.appendChild(span);
        });
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        
        this.particles = [];
        this.healthIcons = [];
        this.isInitialized = false;
        
        console.log('🧹 تم تنظيف موارد الحركات');
    }

    /**
     * إيقاف/تشغيل الحركات
     */
    toggleAnimations(enabled = true) {
        const body = document.body;
        
        if (enabled) {
            body.classList.remove('animations-disabled');
        } else {
            body.classList.add('animations-disabled');
        }
    }

    /**
     * تحديث الحركات حسب الأداء
     */
    optimizeForPerformance() {
        // تقليل الحركات على الأجهزة الضعيفة
        const isLowPerformance = navigator.hardwareConcurrency < 4 || 
                                navigator.deviceMemory < 4;
        
        if (isLowPerformance) {
            this.particles = this.particles.slice(0, 5); // تقليل عدد الجسيمات
            console.log('⚡ تم تحسين الحركات للأجهزة الضعيفة');
        }
    }
}

// إنشاء مثيل عام للحركات
let healthAnimations;

// تهيئة الحركات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    healthAnimations = new HealthAnimations();
    
    // تحسين الأداء
    healthAnimations.optimizeForPerformance();
    
    // تحريك الهاتف والإحصائيات
    setTimeout(() => {
        healthAnimations.animatePhoneMockup();
        healthAnimations.animateHeroStats();
    }, 1000);
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (healthAnimations) {
        healthAnimations.cleanup();
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthAnimations = HealthAnimations;
    window.healthAnimations = healthAnimations;
}
