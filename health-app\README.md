# 🌟 تطبيق صحتي - Health App

تطبيق شامل لتتبع العادات الصحية باللغة العربية مع واجهة مستخدم عصرية وتفاعلية.

## 🚀 الوظائف المتاحة

### ✅ **المكتملة والجاهزة للاستخدام:**

#### 📊 **لوحة التحكم الرئيسية**
- عرض إحصائيات سريعة للعادات الصحية
- رسوم بيانية تفاعلية للنظرة العامة اليومية
- تتبع التقدم الأسبوعي
- عرض الأنشطة الأخيرة
- نظام الإنجازات والمكافآت
- إجراءات سريعة قابلة للنقر

#### 💧 **تتبع الماء**
- زجاجة ماء تفاعلية تُظهر التقدم
- أزرار إضافة سريعة (250ml, 500ml, 750ml, 1000ml)
- إدخال كمية مخصصة
- سجل يومي مفصل لاستهلاك الماء
- إحصائيات متقدمة (متوسط أسبوعي، أيام متتالية، أفضل يوم)
- إمكانية حذف الإدخالات
- مسح سجل اليوم بالكامل

#### 🌙 **تتبع النوم**
- متتبع نوم مع مؤقت حقيقي
- تسجيل جودة النوم (سيئة، متوسطة، جيدة، ممتازة)
- إدخال يدوي لبيانات النوم
- إحصائيات النوم (الليلة الماضية، متوسط الأسبوع، أيام متتالية)
- تتبع تحقيق الأهداف
- سجل تاريخي للنوم

#### 🏃‍♂️ **التمارين الرياضية**
- صفحة مُعدة للتطوير المستقبلي
- هيكل جاهز لإضافة أنواع التمارين المختلفة
- نظام تتبع السعرات المحروقة
- مؤقت التمارين

#### 🍎 **التغذية**
- صفحة مُعدة للتطوير المستقبلي
- نظام تتبع السعرات الحرارية
- قاعدة بيانات الأطعمة
- تحليل العناصر الغذائية

### 🔧 **النظام التقني:**

#### 💾 **التخزين المحلي**
- حفظ جميع البيانات في المتصفح
- نظام نسخ احتياطي تلقائي
- استرجاع البيانات عند إعادة تحميل الصفحة

#### 🎨 **التصميم والواجهة**
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان صحية مريحة للعين
- أيقونات Font Awesome
- خط Cairo العربي الجميل
- تأثيرات CSS3 سلسة
- دعم كامل للغة العربية (RTL)

#### 📱 **التنقل والتفاعل**
- شريط جانبي تفاعلي
- تنقل سلس بين الصفحات
- إجراءات سريعة من لوحة التحكم
- نظام إشعارات Toast
- أزرار تفاعلية مع تأثيرات بصرية

## 🌐 **كيفية الاستخدام**

### 🖥️ **تشغيل التطبيق:**
1. تأكد من تشغيل الخادم المحلي على المنفذ 3001
2. افتح المتصفح وانتقل إلى: `http://localhost:3001`
3. ستظهر لوحة التحكم الرئيسية مباشرة

### 💧 **استخدام تتبع الماء:**
1. انقر على "تتبع الماء" في الشريط الجانبي
2. استخدم الأزرار السريعة لإضافة كميات محددة
3. أو أدخل كمية مخصصة في الحقل المخصص
4. راقب تقدمك في الزجاجة التفاعلية
5. اعرض سجلك اليومي والإحصائيات

### 🌙 **استخدام تتبع النوم:**
1. انقر على "تتبع النوم" في الشريط الجانبي
2. اضغط "بدء النوم" عند الذهاب للنوم
3. اضغط "الاستيقاظ" عند الاستيقاظ
4. اختر جودة نومك
5. أو استخدم الإدخال اليدوي لتسجيل نوم سابق

### ⚡ **الإجراءات السريعة:**
- **إضافة كوب ماء**: يضيف 250ml فوراً
- **تسجيل النوم**: ينتقل لصفحة النوم
- **بدء تمرين**: ينتقل لصفحة التمارين
- **إضافة وجبة**: ينتقل لصفحة التغذية

## 🧪 **اختبار التطبيق**

يمكنك اختبار جميع الوظائف من خلال صفحة الاختبار:
`http://localhost:3001/test-functionality.html`

### 🔍 **الاختبارات المتاحة:**
- اختبار تحميل المكتبات
- اختبار التخزين المحلي
- اختبار إضافة بيانات الماء
- اختبار تتبع النوم
- اختبار التنقل بين الصفحات
- اختبار الإجراءات السريعة
- عرض حالة التطبيق

## 📁 **هيكل المشروع**

```
health-app/
├── index.html              # الصفحة الرئيسية
├── test-functionality.html # صفحة اختبار الوظائف
├── simple.html            # نسخة مبسطة للاختبار
├── manifest.json          # ملف PWA
├── css/
│   ├── main.css           # الأنماط الأساسية
│   ├── dashboard.css      # أنماط لوحة التحكم
│   └── components.css     # أنماط المكونات
├── js/
│   ├── app.js            # التطبيق الرئيسي
│   ├── storage.js        # إدارة التخزين
│   ├── charts.js         # الرسوم البيانية
│   ├── dashboard.js      # لوحة التحكم
│   ├── water.js          # تتبع الماء
│   ├── sleep.js          # تتبع النوم
│   ├── exercise.js       # التمارين
│   ├── nutrition.js      # التغذية
│   ├── reports.js        # التقارير
│   ├── goals.js          # الأهداف
│   ├── reminders.js      # التذكيرات
│   ├── profile.js        # الملف الشخصي
│   ├── settings.js       # الإعدادات
│   ├── navigation.js     # التنقل
│   └── utils.js          # الأدوات المساعدة
└── assets/               # الصور والأيقونات
```

## 🎯 **الأهداف المحققة**

✅ **تطبيق صحي متكامل**
✅ **واجهة عربية كاملة**
✅ **تصميم عصري ومتجاوب**
✅ **تتبع الماء بالكامل**
✅ **تتبع النوم بالكامل**
✅ **نظام التخزين المحلي**
✅ **لوحة تحكم تفاعلية**
✅ **رسوم بيانية**
✅ **نظام الإشعارات**
✅ **الإجراءات السريعة**

## 🚀 **التطوير المستقبلي**

🔄 **قيد التطوير:**
- تطوير صفحة التمارين الكاملة
- تطوير صفحة التغذية الكاملة
- نظام التذكيرات المتقدم
- تصدير التقارير PDF
- مزامنة البيانات السحابية

---

## 🎉 **التطبيق جاهز للاستخدام!**

**تطبيق صحي شامل ومتطور، مصمم خصيصاً للمستخدمين العرب** ✨

**رابط التطبيق:** `http://localhost:3001`
**رابط الاختبار:** `http://localhost:3001/test-functionality.html`
