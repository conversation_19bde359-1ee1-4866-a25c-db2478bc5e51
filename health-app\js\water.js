/**
 * Health App - Water Tracking
 * تتبع الماء
 */

class WaterTracker {
    constructor(app) {
        this.app = app;
        this.dailyGoal = app.user.goals.water * 1000; // تحويل إلى ml
        this.presetAmounts = [250, 500, 750, 1000]; // ml
        
        this.init();
    }

    /**
     * تهيئة متتبع الماء
     */
    init() {
        this.loadWaterPage();
        this.setupEventHandlers();
    }

    /**
     * تحميل صفحة الماء
     */
    loadWaterPage() {
        const waterPage = document.getElementById('waterPage');
        if (!waterPage) return;

        waterPage.innerHTML = `
            <div class="water-container">
                <!-- Water Progress -->
                <div class="water-progress-section">
                    <div class="progress-card">
                        <div class="water-bottle">
                            <div class="bottle-container">
                                <div class="bottle">
                                    <div class="water-level" id="waterLevel"></div>
                                    <div class="bottle-marks">
                                        <div class="mark mark-25">25%</div>
                                        <div class="mark mark-50">50%</div>
                                        <div class="mark mark-75">75%</div>
                                        <div class="mark mark-100">100%</div>
                                    </div>
                                </div>
                                <div class="bottle-cap"></div>
                            </div>
                        </div>
                        
                        <div class="progress-info">
                            <div class="current-intake">
                                <span class="intake-amount" id="currentIntake">0</span>
                                <span class="intake-unit">ml</span>
                            </div>
                            <div class="goal-info">
                                من <span id="dailyGoalDisplay">${this.dailyGoal}</span> ml
                            </div>
                            <div class="progress-percentage" id="progressPercentage">0%</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Add Buttons -->
                <div class="quick-add-section">
                    <h3>إضافة سريعة</h3>
                    <div class="quick-add-grid">
                        ${this.presetAmounts.map(amount => `
                            <button class="quick-add-btn" data-amount="${amount}">
                                <div class="btn-icon">
                                    <i class="fas fa-tint"></i>
                                </div>
                                <div class="btn-amount">${amount}ml</div>
                                <div class="btn-label">${this.getAmountLabel(amount)}</div>
                            </button>
                        `).join('')}
                    </div>
                </div>

                <!-- Custom Amount -->
                <div class="custom-amount-section">
                    <div class="custom-card">
                        <h3>كمية مخصصة</h3>
                        <div class="custom-input-group">
                            <input type="number" class="form-input" id="customAmount" placeholder="الكمية" min="1" max="2000">
                            <span class="input-unit">ml</span>
                            <button class="btn btn-primary" id="addCustomAmount">
                                <i class="fas fa-plus"></i>
                                إضافة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Water Stats -->
                <div class="water-stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="stat-info">
                                <h4>اليوم</h4>
                                <div class="stat-value" id="todayTotal">0 ml</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="stat-info">
                                <h4>متوسط الأسبوع</h4>
                                <div class="stat-value" id="weeklyAverage">0 ml</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-info">
                                <h4>أيام متتالية</h4>
                                <div class="stat-value" id="waterStreak">0 يوم</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-info">
                                <h4>أفضل يوم</h4>
                                <div class="stat-value" id="bestDay">0 ml</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Log -->
                <div class="water-log-section">
                    <div class="log-card">
                        <div class="card-header">
                            <h3>سجل اليوم</h3>
                            <button class="btn btn-ghost" id="clearTodayLog">
                                <i class="fas fa-trash"></i>
                                مسح
                            </button>
                        </div>
                        <div class="water-log-list" id="todayWaterLog">
                            <!-- Today's water entries will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Water Chart -->
                <div class="water-chart-section">
                    <div class="chart-card">
                        <div class="card-header">
                            <h3>تحليل استهلاك الماء</h3>
                            <select class="form-select" id="chartPeriod">
                                <option value="week">أسبوعي</option>
                                <option value="month">شهري</option>
                            </select>
                        </div>
                        <div class="chart-content">
                            <canvas id="waterChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Reminders -->
                <div class="water-reminders-section">
                    <div class="reminders-card">
                        <div class="card-header">
                            <h3>تذكيرات الماء</h3>
                            <button class="btn btn-outline" id="addReminderBtn">
                                <i class="fas fa-plus"></i>
                                إضافة تذكير
                            </button>
                        </div>
                        <div class="reminders-list" id="waterReminders">
                            <!-- Water reminders will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadWaterData();
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        document.addEventListener('click', (e) => {
            // أزرار الإضافة السريعة
            if (e.target.classList.contains('quick-add-btn') || e.target.closest('.quick-add-btn')) {
                const btn = e.target.closest('.quick-add-btn') || e.target;
                const amount = parseInt(btn.dataset.amount);
                this.addWater(amount);
            }
            
            // إضافة كمية مخصصة
            if (e.target.id === 'addCustomAmount') {
                this.addCustomWater();
            }
            
            // مسح سجل اليوم
            if (e.target.id === 'clearTodayLog' || e.target.closest('#clearTodayLog')) {
                this.clearTodayLog();
            }
            
            // حذف إدخال محدد
            if (e.target.classList.contains('delete-entry')) {
                const entryId = parseInt(e.target.dataset.entryId);
                this.deleteWaterEntry(entryId);
            }
        });

        // Enter key في حقل الكمية المخصصة
        document.addEventListener('keypress', (e) => {
            if (e.target.id === 'customAmount' && e.key === 'Enter') {
                this.addCustomWater();
            }
        });

        // تغيير فترة الرسم البياني
        document.addEventListener('change', (e) => {
            if (e.target.id === 'chartPeriod') {
                this.updateWaterChart(e.target.value);
            }
        });
    }

    /**
     * تحميل بيانات الماء
     */
    loadWaterData() {
        this.updateWaterProgress();
        this.updateWaterStats();
        this.loadTodayLog();
        this.createWaterChart();
        this.loadWaterReminders();
    }

    /**
     * إضافة ماء
     */
    addWater(amount) {
        const waterEntry = {
            id: Date.now(),
            amount: amount,
            timestamp: new Date().toISOString(),
            type: 'water'
        };
        
        this.app.data.water.push(waterEntry);
        this.app.saveData();
        
        // تحديث الواجهة
        this.updateWaterProgress();
        this.updateWaterStats();
        this.loadTodayLog();
        
        // تأثير بصري
        this.animateWaterAdd(amount);
        
        this.app.showToast(`تم إضافة ${amount}ml من الماء`, 'success');
    }

    /**
     * إضافة كمية مخصصة
     */
    addCustomWater() {
        const customAmountInput = document.getElementById('customAmount');
        const amount = parseInt(customAmountInput.value);
        
        if (!amount || amount <= 0 || amount > 2000) {
            this.app.showToast('يرجى إدخال كمية صحيحة (1-2000 ml)', 'error');
            return;
        }
        
        this.addWater(amount);
        customAmountInput.value = '';
    }

    /**
     * تحديث تقدم الماء
     */
    updateWaterProgress() {
        const todayIntake = this.getTodayWaterIntake();
        const percentage = Math.min((todayIntake / this.dailyGoal) * 100, 100);
        
        // تحديث مستوى الماء في الزجاجة
        const waterLevel = document.getElementById('waterLevel');
        if (waterLevel) {
            waterLevel.style.height = `${percentage}%`;
            
            // تغيير اللون حسب التقدم
            if (percentage >= 100) {
                waterLevel.style.background = 'linear-gradient(to top, #10b981, #34d399)';
            } else if (percentage >= 75) {
                waterLevel.style.background = 'linear-gradient(to top, #06b6d4, #0891b2)';
            } else if (percentage >= 50) {
                waterLevel.style.background = 'linear-gradient(to top, #3b82f6, #1d4ed8)';
            } else {
                waterLevel.style.background = 'linear-gradient(to top, #6366f1, #4f46e5)';
            }
        }
        
        // تحديث النصوص
        document.getElementById('currentIntake').textContent = todayIntake;
        document.getElementById('progressPercentage').textContent = `${Math.round(percentage)}%`;
    }

    /**
     * تحديث إحصائيات الماء
     */
    updateWaterStats() {
        const todayIntake = this.getTodayWaterIntake();
        const weeklyAverage = this.getWeeklyWaterAverage();
        const streak = this.calculateWaterStreak();
        const bestDay = this.getBestWaterDay();
        
        document.getElementById('todayTotal').textContent = `${todayIntake} ml`;
        document.getElementById('weeklyAverage').textContent = `${Math.round(weeklyAverage)} ml`;
        document.getElementById('waterStreak').textContent = `${streak} يوم`;
        document.getElementById('bestDay').textContent = `${bestDay} ml`;
    }

    /**
     * تحميل سجل اليوم
     */
    loadTodayLog() {
        const todayEntries = this.getTodayWaterEntries();
        const logContainer = document.getElementById('todayWaterLog');
        
        if (todayEntries.length === 0) {
            logContainer.innerHTML = `
                <div class="empty-log">
                    <i class="fas fa-tint"></i>
                    <p>لم تشرب أي ماء اليوم بعد</p>
                </div>
            `;
            return;
        }
        
        logContainer.innerHTML = todayEntries.map(entry => `
            <div class="water-entry">
                <div class="entry-icon">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="entry-info">
                    <div class="entry-amount">${entry.amount}ml</div>
                    <div class="entry-time">${this.formatTime(entry.timestamp)}</div>
                </div>
                <button class="delete-entry" data-entry-id="${entry.id}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    /**
     * الحصول على استهلاك الماء اليوم
     */
    getTodayWaterIntake() {
        const today = new Date().toDateString();
        return this.app.data.water
            .filter(entry => new Date(entry.timestamp).toDateString() === today)
            .reduce((total, entry) => total + entry.amount, 0);
    }

    /**
     * الحصول على إدخالات الماء اليوم
     */
    getTodayWaterEntries() {
        const today = new Date().toDateString();
        return this.app.data.water
            .filter(entry => new Date(entry.timestamp).toDateString() === today)
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }

    /**
     * حساب متوسط الماء الأسبوعي
     */
    getWeeklyWaterAverage() {
        const weekData = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateString = date.toDateString();
            
            const dayIntake = this.app.data.water
                .filter(entry => new Date(entry.timestamp).toDateString() === dateString)
                .reduce((total, entry) => total + entry.amount, 0);
            
            weekData.push(dayIntake);
        }
        
        return weekData.reduce((sum, day) => sum + day, 0) / weekData.length;
    }

    /**
     * حساب أيام الماء المتتالية
     */
    calculateWaterStreak() {
        let streak = 0;
        const today = new Date();
        
        for (let i = 0; i < 365; i++) {
            const checkDate = new Date(today);
            checkDate.setDate(today.getDate() - i);
            const dateString = checkDate.toDateString();
            
            const dayIntake = this.app.data.water
                .filter(entry => new Date(entry.timestamp).toDateString() === dateString)
                .reduce((total, entry) => total + entry.amount, 0);
            
            if (dayIntake >= this.dailyGoal) {
                streak++;
            } else {
                break;
            }
        }
        
        return streak;
    }

    /**
     * الحصول على أفضل يوم
     */
    getBestWaterDay() {
        const dailyTotals = {};
        
        this.app.data.water.forEach(entry => {
            const date = new Date(entry.timestamp).toDateString();
            dailyTotals[date] = (dailyTotals[date] || 0) + entry.amount;
        });
        
        return Math.max(...Object.values(dailyTotals), 0);
    }

    /**
     * حذف إدخال ماء
     */
    deleteWaterEntry(entryId) {
        this.app.data.water = this.app.data.water.filter(entry => entry.id !== entryId);
        this.app.saveData();
        
        this.updateWaterProgress();
        this.updateWaterStats();
        this.loadTodayLog();
        
        this.app.showToast('تم حذف الإدخال', 'success');
    }

    /**
     * مسح سجل اليوم
     */
    clearTodayLog() {
        if (!confirm('هل أنت متأكد من مسح جميع إدخالات الماء لهذا اليوم؟')) {
            return;
        }
        
        const today = new Date().toDateString();
        this.app.data.water = this.app.data.water.filter(entry => 
            new Date(entry.timestamp).toDateString() !== today
        );
        
        this.app.saveData();
        this.loadWaterData();
        
        this.app.showToast('تم مسح سجل اليوم', 'success');
    }

    /**
     * تحريك إضافة الماء
     */
    animateWaterAdd(amount) {
        const waterLevel = document.getElementById('waterLevel');
        if (waterLevel) {
            waterLevel.style.transition = 'height 0.5s ease-in-out';
        }
    }

    /**
     * الحصول على تسمية الكمية
     */
    getAmountLabel(amount) {
        const labels = {
            250: 'كوب',
            500: 'زجاجة صغيرة',
            750: 'زجاجة كبيرة',
            1000: 'لتر'
        };
        return labels[amount] || 'كمية';
    }

    /**
     * تنسيق الوقت
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * إنشاء رسم بياني للماء
     */
    createWaterChart() {
        if (!this.app.charts) return;
        
        const weeklyData = this.getWeeklyWaterData();
        // سيتم إنشاء الرسم البياني هنا
    }

    /**
     * تحميل تذكيرات الماء
     */
    loadWaterReminders() {
        // سيتم تنفيذ تذكيرات الماء هنا
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.WaterTracker = WaterTracker;
}
