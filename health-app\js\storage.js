/**
 * Health App - Storage Management
 * إدارة التخزين المحلي للتطبيق الصحي
 */

class HealthStorage {
    static STORAGE_KEYS = {
        USER: 'health_app_user',
        SLEEP_DATA: 'health_app_sleep',
        WATER_DATA: 'health_app_water',
        EXERCISE_DATA: 'health_app_exercise',
        NUTRITION_DATA: 'health_app_nutrition',
        GOALS: 'health_app_goals',
        REMINDERS: 'health_app_reminders',
        SETTINGS: 'health_app_settings',
        ACHIEVEMENTS: 'health_app_achievements'
    };

    /**
     * حفظ البيانات في التخزين المحلي
     */
    static saveData(key, data) {
        try {
            const serializedData = JSON.stringify(data);
            localStorage.setItem(key, serializedData);
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    /**
     * استرجاع البيانات من التخزين المحلي
     */
    static getData(key, defaultValue = null) {
        try {
            const serializedData = localStorage.getItem(key);
            if (serializedData === null) {
                return defaultValue;
            }
            return JSON.parse(serializedData);
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return defaultValue;
        }
    }

    /**
     * حذف البيانات من التخزين المحلي
     */
    static removeData(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    static clearAllData() {
        try {
            Object.values(this.STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }

    // ===== USER DATA =====

    /**
     * حفظ بيانات المستخدم
     */
    static saveUser(userData) {
        return this.saveData(this.STORAGE_KEYS.USER, userData);
    }

    /**
     * استرجاع بيانات المستخدم
     */
    static getUser() {
        return this.getData(this.STORAGE_KEYS.USER, {
            name: 'مستخدم جديد',
            age: 25,
            weight: 70,
            height: 170,
            gender: 'male',
            activityLevel: 'moderate',
            goals: {
                sleep: 8,
                water: 2.5,
                exercise: 60,
                calories: 2000
            },
            joinDate: new Date().toISOString()
        });
    }

    // ===== SLEEP DATA =====

    /**
     * حفظ بيانات النوم
     */
    static saveSleepData(sleepData) {
        return this.saveData(this.STORAGE_KEYS.SLEEP_DATA, sleepData);
    }

    /**
     * استرجاع بيانات النوم
     */
    static getSleepData() {
        return this.getData(this.STORAGE_KEYS.SLEEP_DATA, []);
    }

    /**
     * إضافة سجل نوم جديد
     */
    static addSleepEntry(sleepEntry) {
        const sleepData = this.getSleepData();
        sleepEntry.id = Date.now();
        sleepEntry.timestamp = new Date().toISOString();
        sleepData.push(sleepEntry);
        return this.saveSleepData(sleepData);
    }

    /**
     * تحديث سجل نوم
     */
    static updateSleepEntry(entryId, updatedData) {
        const sleepData = this.getSleepData();
        const index = sleepData.findIndex(entry => entry.id === entryId);
        if (index !== -1) {
            sleepData[index] = { ...sleepData[index], ...updatedData };
            return this.saveSleepData(sleepData);
        }
        return false;
    }

    /**
     * حذف سجل نوم
     */
    static deleteSleepEntry(entryId) {
        const sleepData = this.getSleepData();
        const filteredData = sleepData.filter(entry => entry.id !== entryId);
        return this.saveSleepData(filteredData);
    }

    // ===== WATER DATA =====

    /**
     * حفظ بيانات الماء
     */
    static saveWaterData(waterData) {
        return this.saveData(this.STORAGE_KEYS.WATER_DATA, waterData);
    }

    /**
     * استرجاع بيانات الماء
     */
    static getWaterData() {
        return this.getData(this.STORAGE_KEYS.WATER_DATA, []);
    }

    /**
     * إضافة سجل ماء جديد
     */
    static addWaterEntry(waterEntry) {
        const waterData = this.getWaterData();
        waterEntry.id = Date.now();
        waterEntry.timestamp = new Date().toISOString();
        waterData.push(waterEntry);
        return this.saveWaterData(waterData);
    }

    /**
     * الحصول على استهلاك الماء اليومي
     */
    static getTodayWaterIntake() {
        const waterData = this.getWaterData();
        const today = new Date().toDateString();
        
        return waterData
            .filter(entry => new Date(entry.timestamp).toDateString() === today)
            .reduce((total, entry) => total + (entry.amount || 0), 0);
    }

    // ===== EXERCISE DATA =====

    /**
     * حفظ بيانات التمارين
     */
    static saveExerciseData(exerciseData) {
        return this.saveData(this.STORAGE_KEYS.EXERCISE_DATA, exerciseData);
    }

    /**
     * استرجاع بيانات التمارين
     */
    static getExerciseData() {
        return this.getData(this.STORAGE_KEYS.EXERCISE_DATA, []);
    }

    /**
     * إضافة سجل تمرين جديد
     */
    static addExerciseEntry(exerciseEntry) {
        const exerciseData = this.getExerciseData();
        exerciseEntry.id = Date.now();
        exerciseEntry.timestamp = new Date().toISOString();
        exerciseData.push(exerciseEntry);
        return this.saveExerciseData(exerciseData);
    }

    /**
     * الحصول على دقائق التمرين اليومية
     */
    static getTodayExerciseMinutes() {
        const exerciseData = this.getExerciseData();
        const today = new Date().toDateString();
        
        return exerciseData
            .filter(entry => new Date(entry.timestamp).toDateString() === today)
            .reduce((total, entry) => total + (entry.duration || 0), 0);
    }

    // ===== NUTRITION DATA =====

    /**
     * حفظ بيانات التغذية
     */
    static saveNutritionData(nutritionData) {
        return this.saveData(this.STORAGE_KEYS.NUTRITION_DATA, nutritionData);
    }

    /**
     * استرجاع بيانات التغذية
     */
    static getNutritionData() {
        return this.getData(this.STORAGE_KEYS.NUTRITION_DATA, []);
    }

    /**
     * إضافة سجل وجبة جديد
     */
    static addNutritionEntry(nutritionEntry) {
        const nutritionData = this.getNutritionData();
        nutritionEntry.id = Date.now();
        nutritionEntry.timestamp = new Date().toISOString();
        nutritionData.push(nutritionEntry);
        return this.saveNutritionData(nutritionData);
    }

    /**
     * الحصول على السعرات الحرارية اليومية
     */
    static getTodayCalories() {
        const nutritionData = this.getNutritionData();
        const today = new Date().toDateString();
        
        return nutritionData
            .filter(entry => new Date(entry.timestamp).toDateString() === today)
            .reduce((total, entry) => total + (entry.calories || 0), 0);
    }

    // ===== GOALS =====

    /**
     * حفظ الأهداف
     */
    static saveGoals(goals) {
        return this.saveData(this.STORAGE_KEYS.GOALS, goals);
    }

    /**
     * استرجاع الأهداف
     */
    static getGoals() {
        return this.getData(this.STORAGE_KEYS.GOALS, []);
    }

    /**
     * إضافة هدف جديد
     */
    static addGoal(goal) {
        const goals = this.getGoals();
        goal.id = Date.now();
        goal.createdAt = new Date().toISOString();
        goal.status = 'active';
        goals.push(goal);
        return this.saveGoals(goals);
    }

    /**
     * تحديث هدف
     */
    static updateGoal(goalId, updatedData) {
        const goals = this.getGoals();
        const index = goals.findIndex(goal => goal.id === goalId);
        if (index !== -1) {
            goals[index] = { ...goals[index], ...updatedData };
            return this.saveGoals(goals);
        }
        return false;
    }

    // ===== REMINDERS =====

    /**
     * حفظ التذكيرات
     */
    static saveReminders(reminders) {
        return this.saveData(this.STORAGE_KEYS.REMINDERS, reminders);
    }

    /**
     * استرجاع التذكيرات
     */
    static getReminders() {
        return this.getData(this.STORAGE_KEYS.REMINDERS, []);
    }

    /**
     * إضافة تذكير جديد
     */
    static addReminder(reminder) {
        const reminders = this.getReminders();
        reminder.id = Date.now();
        reminder.createdAt = new Date().toISOString();
        reminder.isActive = true;
        reminders.push(reminder);
        return this.saveReminders(reminders);
    }

    /**
     * تحديث تذكير
     */
    static updateReminder(reminderId, updatedData) {
        const reminders = this.getReminders();
        const index = reminders.findIndex(reminder => reminder.id === reminderId);
        if (index !== -1) {
            reminders[index] = { ...reminders[index], ...updatedData };
            return this.saveReminders(reminders);
        }
        return false;
    }

    // ===== SETTINGS =====

    /**
     * حفظ الإعدادات
     */
    static saveSettings(settings) {
        return this.saveData(this.STORAGE_KEYS.SETTINGS, settings);
    }

    /**
     * استرجاع الإعدادات
     */
    static getSettings() {
        return this.getData(this.STORAGE_KEYS.SETTINGS, {
            theme: 'light',
            language: 'ar',
            notifications: {
                water: true,
                sleep: true,
                exercise: true,
                nutrition: true
            },
            units: {
                weight: 'kg',
                height: 'cm',
                water: 'ml',
                distance: 'km'
            },
            privacy: {
                shareData: false,
                analytics: true
            }
        });
    }

    // ===== ACHIEVEMENTS =====

    /**
     * حفظ الإنجازات
     */
    static saveAchievements(achievements) {
        return this.saveData(this.STORAGE_KEYS.ACHIEVEMENTS, achievements);
    }

    /**
     * استرجاع الإنجازات
     */
    static getAchievements() {
        return this.getData(this.STORAGE_KEYS.ACHIEVEMENTS, []);
    }

    /**
     * إضافة إنجاز جديد
     */
    static addAchievement(achievement) {
        const achievements = this.getAchievements();
        achievement.id = Date.now();
        achievement.unlockedAt = new Date().toISOString();
        achievements.push(achievement);
        return this.saveAchievements(achievements);
    }

    // ===== USER-SPECIFIC DATA METHODS =====

    /**
     * الحصول على معرف المستخدم الحالي
     */
    static getCurrentUserId() {
        const session = localStorage.getItem('health_app_session');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                return sessionData.user?.id;
            } catch (error) {
                console.error('خطأ في قراءة معرف المستخدم:', error);
            }
        }
        return null;
    }

    /**
     * حفظ بيانات خاصة بالمستخدم
     */
    static saveUserSpecificData(dataType, data) {
        const userId = this.getCurrentUserId();
        if (!userId) {
            console.warn('لا يوجد مستخدم مسجل دخول');
            return false;
        }

        const key = `health_app_user_${userId}_${dataType}`;
        return this.saveData(key, data);
    }

    /**
     * استرجاع بيانات خاصة بالمستخدم
     */
    static getUserSpecificData(dataType, defaultValue = null) {
        const userId = this.getCurrentUserId();
        if (!userId) {
            console.warn('لا يوجد مستخدم مسجل دخول');
            return defaultValue;
        }

        const key = `health_app_user_${userId}_${dataType}`;
        return this.getData(key, defaultValue);
    }

    /**
     * حفظ بيانات الماء للمستخدم الحالي
     */
    static saveWaterData(data) {
        return this.saveUserSpecificData('water', data);
    }

    /**
     * استرجاع بيانات الماء للمستخدم الحالي
     */
    static getWaterData() {
        return this.getUserSpecificData('water', []);
    }

    /**
     * حفظ بيانات النوم للمستخدم الحالي
     */
    static saveSleepData(data) {
        return this.saveUserSpecificData('sleep', data);
    }

    /**
     * استرجاع بيانات النوم للمستخدم الحالي
     */
    static getSleepData() {
        return this.getUserSpecificData('sleep', []);
    }

    /**
     * حفظ بيانات التمارين للمستخدم الحالي
     */
    static saveExerciseData(data) {
        return this.saveUserSpecificData('exercise', data);
    }

    /**
     * استرجاع بيانات التمارين للمستخدم الحالي
     */
    static getExerciseData() {
        return this.getUserSpecificData('exercise', []);
    }

    /**
     * حفظ بيانات التغذية للمستخدم الحالي
     */
    static saveNutritionData(data) {
        return this.saveUserSpecificData('nutrition', data);
    }

    /**
     * استرجاع بيانات التغذية للمستخدم الحالي
     */
    static getNutritionData() {
        return this.getUserSpecificData('nutrition', []);
    }

    // ===== ADDITIONAL METHODS FOR COMPONENTS =====

    /**
     * حفظ بيانات الأهداف (للمكونات) - استخدام الدوال الموجودة
     */
    static saveGoalsData(data) {
        return this.saveGoals(data);
    }

    /**
     * استرجاع بيانات الأهداف (للمكونات) - استخدام الدوال الموجودة
     */
    static getGoalsData() {
        return this.getGoals();
    }

    /**
     * حفظ بيانات التذكيرات (للمكونات)
     */
    static saveRemindersData(data) {
        return this.saveUserSpecificData('reminders', data);
    }

    /**
     * استرجاع بيانات التذكيرات (للمكونات)
     */
    static getRemindersData() {
        return this.getUserSpecificData('reminders', { reminders: [] });
    }

    /**
     * حفظ بيانات الملف الشخصي (للمكونات)
     */
    static saveProfileData(data) {
        return this.saveUserSpecificData('profile', data);
    }

    /**
     * استرجاع بيانات الملف الشخصي (للمكونات)
     */
    static getProfileData() {
        return this.getUserSpecificData('profile', { profile: null });
    }

    /**
     * حفظ بيانات الإعدادات (للمكونات)
     */
    static saveSettingsData(data) {
        return this.saveUserSpecificData('settings', data);
    }

    /**
     * استرجاع بيانات الإعدادات (للمكونات)
     */
    static getSettingsData() {
        return this.getUserSpecificData('settings', {
            settings: {
                theme: 'light',
                language: 'ar',
                notifications: true,
                waterReminders: true,
                sleepReminders: true,
                exerciseReminders: true
            }
        });
    }

    /**
     * حفظ بيانات المستخدم الحالي
     */
    static saveCurrentUserData() {
        const userId = this.getCurrentUserId();
        if (!userId) return false;

        const userData = {
            water: this.getWaterData(),
            sleep: this.getSleepData(),
            exercise: this.getExerciseData(),
            nutrition: this.getNutritionData(),
            goals: this.getGoalsData(),
            reminders: this.getRemindersData(),
            profile: this.getProfileData(),
            settings: this.getSettingsData(),
            lastBackup: new Date().toISOString()
        };

        return this.saveUserSpecificData('backup', userData);
    }

    /**
     * استعادة بيانات المستخدم
     */
    static restoreUserData() {
        const backup = this.getUserSpecificData('backup');
        if (!backup) return false;

        try {
            if (backup.water) this.saveWaterData(backup.water);
            if (backup.sleep) this.saveSleepData(backup.sleep);
            if (backup.exercise) this.saveExerciseData(backup.exercise);
            if (backup.nutrition) this.saveNutritionData(backup.nutrition);
            if (backup.goals) this.saveGoalsData(backup.goals);
            if (backup.reminders) this.saveRemindersData(backup.reminders);
            if (backup.profile) this.saveProfileData(backup.profile);
            if (backup.settings) this.saveSettingsData(backup.settings);

            return true;
        } catch (error) {
            console.error('خطأ في استعادة البيانات:', error);
            return false;
        }
    }

    // ===== UTILITY METHODS =====

    /**
     * تصدير جميع البيانات
     */
    static exportAllData() {
        const allData = {};
        Object.entries(this.STORAGE_KEYS).forEach(([key, storageKey]) => {
            allData[key] = this.getData(storageKey);
        });
        return allData;
    }

    /**
     * استيراد البيانات
     */
    static importData(data) {
        try {
            Object.entries(data).forEach(([key, value]) => {
                const storageKey = this.STORAGE_KEYS[key];
                if (storageKey) {
                    this.saveData(storageKey, value);
                }
            });
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    /**
     * حساب حجم البيانات المخزنة
     */
    static getStorageSize() {
        let totalSize = 0;
        Object.values(this.STORAGE_KEYS).forEach(key => {
            const data = localStorage.getItem(key);
            if (data) {
                totalSize += new Blob([data]).size;
            }
        });
        return totalSize;
    }

    /**
     * تنظيف البيانات القديمة
     */
    static cleanupOldData(daysToKeep = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        
        // تنظيف بيانات النوم القديمة
        const sleepData = this.getSleepData();
        const filteredSleepData = sleepData.filter(entry => 
            new Date(entry.timestamp) > cutoffDate
        );
        this.saveSleepData(filteredSleepData);
        
        // تنظيف بيانات الماء القديمة
        const waterData = this.getWaterData();
        const filteredWaterData = waterData.filter(entry => 
            new Date(entry.timestamp) > cutoffDate
        );
        this.saveWaterData(filteredWaterData);
        
        // تنظيف بيانات التمارين القديمة
        const exerciseData = this.getExerciseData();
        const filteredExerciseData = exerciseData.filter(entry => 
            new Date(entry.timestamp) > cutoffDate
        );
        this.saveExerciseData(filteredExerciseData);
        
        // تنظيف بيانات التغذية القديمة
        const nutritionData = this.getNutritionData();
        const filteredNutritionData = nutritionData.filter(entry => 
            new Date(entry.timestamp) > cutoffDate
        );
        this.saveNutritionData(filteredNutritionData);
        
        console.log(`تم تنظيف البيانات الأقدم من ${daysToKeep} يوم`);
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthStorage = HealthStorage;
}
