/**
 * Health App - Dashboard Management
 * إدارة لوحة التحكم الرئيسية
 */

class HealthDashboard {
    constructor(app) {
        this.app = app;
        this.charts = {};
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * تهيئة لوحة التحكم
     */
    init() {
        this.setupAutoUpdate();
    }

    /**
     * تحميل لوحة التحكم
     */
    loadDashboard() {
        this.updateStats();
        this.loadCharts();
        this.loadRecentActivities();
        this.loadAchievements();
    }

    /**
     * تحديث الإحصائيات السريعة
     */
    updateStats() {
        const today = new Date().toDateString();
        
        // حساب إحصائيات اليوم
        const todayStats = this.calculateTodayStats();
        
        // تحديث النوم
        this.updateSleepStat(todayStats.sleep);
        
        // تحديث الماء
        this.updateWaterStat(todayStats.water);
        
        // تحديث التمارين
        this.updateExerciseStat(todayStats.exercise);
        
        // تحديث التغذية
        this.updateNutritionStat(todayStats.nutrition);
    }

    /**
     * حساب إحصائيات اليوم
     */
    calculateTodayStats() {
        const today = new Date().toDateString();
        const user = this.app.user;
        
        // حساب النوم
        const sleepData = this.app.data.sleep.filter(entry => 
            new Date(entry.timestamp).toDateString() === today
        );
        const totalSleepHours = sleepData.reduce((total, entry) => 
            total + (entry.duration || 0), 0
        );
        const sleepGoal = user.goals.sleep || 8;
        const sleepPercentage = Math.min((totalSleepHours / sleepGoal) * 100, 100);
        
        // حساب الماء
        const waterData = this.app.data.water.filter(entry => 
            new Date(entry.timestamp).toDateString() === today
        );
        const totalWater = waterData.reduce((total, entry) => 
            total + (entry.amount || 0), 0
        ) / 1000; // تحويل إلى لتر
        const waterGoal = user.goals.water || 2.5;
        const waterPercentage = Math.min((totalWater / waterGoal) * 100, 100);
        
        // حساب التمارين
        const exerciseData = this.app.data.exercise.filter(entry => 
            new Date(entry.timestamp).toDateString() === today
        );
        const totalExercise = exerciseData.reduce((total, entry) => 
            total + (entry.duration || 0), 0
        );
        const exerciseGoal = user.goals.exercise || 60;
        const exercisePercentage = Math.min((totalExercise / exerciseGoal) * 100, 100);
        
        // حساب التغذية
        const nutritionData = this.app.data.nutrition.filter(entry => 
            new Date(entry.timestamp).toDateString() === today
        );
        const totalCalories = nutritionData.reduce((total, entry) => 
            total + (entry.calories || 0), 0
        );
        const caloriesGoal = user.goals.calories || 2000;
        const nutritionPercentage = Math.min((totalCalories / caloriesGoal) * 100, 100);
        
        return {
            sleep: {
                value: totalSleepHours,
                percentage: sleepPercentage,
                goal: sleepGoal
            },
            water: {
                value: totalWater,
                percentage: waterPercentage,
                goal: waterGoal
            },
            exercise: {
                value: totalExercise,
                percentage: exercisePercentage,
                goal: exerciseGoal
            },
            nutrition: {
                value: totalCalories,
                percentage: nutritionPercentage,
                goal: caloriesGoal
            }
        };
    }

    /**
     * تحديث إحصائية النوم
     */
    updateSleepStat(sleepStat) {
        const sleepHoursElement = document.getElementById('sleepHours');
        const sleepProgressElement = document.querySelector('.sleep-stat .progress-fill');
        const sleepPercentageElement = document.querySelector('.sleep-stat .stat-progress span');
        
        if (sleepHoursElement) {
            sleepHoursElement.textContent = sleepStat.value.toFixed(1);
        }
        
        if (sleepProgressElement) {
            sleepProgressElement.style.width = `${sleepStat.percentage}%`;
        }
        
        if (sleepPercentageElement) {
            sleepPercentageElement.textContent = `${Math.round(sleepStat.percentage)}%`;
        }
    }

    /**
     * تحديث إحصائية الماء
     */
    updateWaterStat(waterStat) {
        const waterAmountElement = document.getElementById('waterAmount');
        const waterProgressElement = document.querySelector('.water-stat .progress-fill');
        const waterPercentageElement = document.querySelector('.water-stat .stat-progress span');
        
        if (waterAmountElement) {
            waterAmountElement.textContent = waterStat.value.toFixed(1);
        }
        
        if (waterProgressElement) {
            waterProgressElement.style.width = `${waterStat.percentage}%`;
        }
        
        if (waterPercentageElement) {
            waterPercentageElement.textContent = `${Math.round(waterStat.percentage)}%`;
        }
    }

    /**
     * تحديث إحصائية التمارين
     */
    updateExerciseStat(exerciseStat) {
        const exerciseMinutesElement = document.getElementById('exerciseMinutes');
        const exerciseProgressElement = document.querySelector('.exercise-stat .progress-fill');
        const exercisePercentageElement = document.querySelector('.exercise-stat .stat-progress span');
        
        if (exerciseMinutesElement) {
            exerciseMinutesElement.textContent = Math.round(exerciseStat.value);
        }
        
        if (exerciseProgressElement) {
            exerciseProgressElement.style.width = `${exerciseStat.percentage}%`;
        }
        
        if (exercisePercentageElement) {
            exercisePercentageElement.textContent = `${Math.round(exerciseStat.percentage)}%`;
        }
    }

    /**
     * تحديث إحصائية التغذية
     */
    updateNutritionStat(nutritionStat) {
        const caloriesCountElement = document.getElementById('caloriesCount');
        const nutritionProgressElement = document.querySelector('.nutrition-stat .progress-fill');
        const nutritionPercentageElement = document.querySelector('.nutrition-stat .stat-progress span');
        
        if (caloriesCountElement) {
            caloriesCountElement.textContent = Math.round(nutritionStat.value);
        }
        
        if (nutritionProgressElement) {
            nutritionProgressElement.style.width = `${nutritionStat.percentage}%`;
        }
        
        if (nutritionPercentageElement) {
            nutritionPercentageElement.textContent = `${Math.round(nutritionStat.percentage)}%`;
        }
    }

    /**
     * تحميل الرسوم البيانية
     */
    loadCharts() {
        if (!this.app.charts) return;
        
        // رسم بياني للنظرة العامة اليومية
        const todayStats = this.calculateTodayStats();
        const overviewData = {
            sleep: todayStats.sleep.percentage,
            water: todayStats.water.percentage,
            exercise: todayStats.exercise.percentage,
            nutrition: todayStats.nutrition.percentage
        };
        
        this.charts.todayOverview = this.app.charts.createTodayOverviewChart(
            'todayOverviewChart', 
            overviewData
        );
        
        // رسم بياني للتقدم الأسبوعي
        const weeklyData = this.getWeeklyProgressData();
        this.charts.weeklyProgress = this.app.charts.createWeeklyProgressChart(
            'weeklyProgressChart', 
            weeklyData
        );
    }

    /**
     * الحصول على بيانات التقدم الأسبوعي
     */
    getWeeklyProgressData() {
        const weekData = {
            sleep: [],
            water: [],
            exercise: []
        };
        
        // حساب بيانات آخر 7 أيام
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateString = date.toDateString();
            
            // النوم
            const sleepEntries = this.app.data.sleep.filter(entry => 
                new Date(entry.timestamp).toDateString() === dateString
            );
            const sleepHours = sleepEntries.reduce((total, entry) => 
                total + (entry.duration || 0), 0
            );
            weekData.sleep.push(sleepHours);
            
            // الماء
            const waterEntries = this.app.data.water.filter(entry => 
                new Date(entry.timestamp).toDateString() === dateString
            );
            const waterAmount = waterEntries.reduce((total, entry) => 
                total + (entry.amount || 0), 0
            ) / 1000; // تحويل إلى لتر
            weekData.water.push(waterAmount);
            
            // التمارين
            const exerciseEntries = this.app.data.exercise.filter(entry => 
                new Date(entry.timestamp).toDateString() === dateString
            );
            const exerciseMinutes = exerciseEntries.reduce((total, entry) => 
                total + (entry.duration || 0), 0
            );
            weekData.exercise.push(exerciseMinutes);
        }
        
        return weekData;
    }

    /**
     * تحميل الأنشطة الأخيرة
     */
    loadRecentActivities() {
        const activitiesContainer = document.getElementById('recentActivities');
        if (!activitiesContainer) return;
        
        // جمع جميع الأنشطة الأخيرة
        const allActivities = [];
        
        // إضافة أنشطة النوم
        this.app.data.sleep.slice(-5).forEach(entry => {
            allActivities.push({
                type: 'sleep',
                title: 'تسجيل النوم',
                value: `${entry.duration} ساعة`,
                timestamp: entry.timestamp
            });
        });
        
        // إضافة أنشطة الماء
        this.app.data.water.slice(-5).forEach(entry => {
            allActivities.push({
                type: 'water',
                title: 'شرب الماء',
                value: `${entry.amount}ml`,
                timestamp: entry.timestamp
            });
        });
        
        // إضافة أنشطة التمارين
        this.app.data.exercise.slice(-5).forEach(entry => {
            allActivities.push({
                type: 'exercise',
                title: entry.name || 'تمرين',
                value: `${entry.duration} دقيقة`,
                timestamp: entry.timestamp
            });
        });
        
        // إضافة أنشطة التغذية
        this.app.data.nutrition.slice(-5).forEach(entry => {
            allActivities.push({
                type: 'nutrition',
                title: entry.name || 'وجبة',
                value: `${entry.calories} سعرة`,
                timestamp: entry.timestamp
            });
        });
        
        // ترتيب حسب الوقت
        allActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        // عرض أحدث 10 أنشطة
        const recentActivities = allActivities.slice(0, 10);
        
        activitiesContainer.innerHTML = recentActivities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-details">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
                </div>
                <div class="activity-value">${activity.value}</div>
            </div>
        `).join('');
    }

    /**
     * تحميل الإنجازات
     */
    loadAchievements() {
        const achievementsContainer = document.getElementById('achievementsList');
        if (!achievementsContainer) return;
        
        // إنجازات تجريبية
        const achievements = [
            {
                id: 1,
                title: 'أسبوع مثالي',
                description: 'حقق جميع أهدافك لمدة أسبوع',
                icon: 'trophy',
                unlocked: true
            },
            {
                id: 2,
                title: 'محب الماء',
                description: 'اشرب 3 لتر من الماء يومياً',
                icon: 'tint',
                unlocked: true
            },
            {
                id: 3,
                title: 'رياضي نشط',
                description: 'مارس الرياضة لمدة 30 يوم متتالي',
                icon: 'dumbbell',
                unlocked: false
            },
            {
                id: 4,
                title: 'نوم صحي',
                description: 'احصل على 8 ساعات نوم لمدة أسبوع',
                icon: 'moon',
                unlocked: true
            },
            {
                id: 5,
                title: 'تغذية متوازنة',
                description: 'حافظ على نظام غذائي متوازن',
                icon: 'apple-alt',
                unlocked: false
            },
            {
                id: 6,
                title: 'البداية القوية',
                description: 'أكمل أول أسبوع في التطبيق',
                icon: 'star',
                unlocked: true
            }
        ];
        
        achievementsContainer.innerHTML = achievements.map(achievement => `
            <div class="achievement-item ${achievement.unlocked ? 'unlocked' : ''}">
                <div class="achievement-icon">
                    <i class="fas fa-${achievement.icon}"></i>
                </div>
                <div class="achievement-title">${achievement.title}</div>
                <div class="achievement-description">${achievement.description}</div>
            </div>
        `).join('');
    }

    /**
     * الحصول على أيقونة النشاط
     */
    getActivityIcon(type) {
        const icons = {
            sleep: 'moon',
            water: 'tint',
            exercise: 'dumbbell',
            nutrition: 'apple-alt'
        };
        return icons[type] || 'circle';
    }

    /**
     * تنسيق الوقت
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) {
            return 'الآن';
        } else if (diffInMinutes < 60) {
            return `منذ ${diffInMinutes} دقيقة`;
        } else if (diffInMinutes < 1440) {
            const hours = Math.floor(diffInMinutes / 60);
            return `منذ ${hours} ساعة`;
        } else {
            const days = Math.floor(diffInMinutes / 1440);
            return `منذ ${days} يوم`;
        }
    }

    /**
     * إعداد التحديث التلقائي
     */
    setupAutoUpdate() {
        // تحديث كل 5 دقائق
        this.updateInterval = setInterval(() => {
            if (this.app.currentPage === 'dashboard') {
                this.updateStats();
            }
        }, 5 * 60 * 1000);
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        // تدمير الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });
        
        this.charts = {};
    }
}

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.HealthDashboard = HealthDashboard;
}
