/**
 * PDFMerge Pro - Main Application
 * التطبيق الرئيسي لدمج ملفات PDF
 */

class PDFMergeApp {
    constructor() {
        this.version = '1.0.0';
        this.isInitialized = false;
        this.settings = {
            maxFileSize: 100 * 1024 * 1024, // 100MB
            maxFiles: 50,
            autoOptimize: true,
            theme: 'light',
            language: 'ar'
        };
        
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    async init() {
        try {
            console.log(`🚀 PDFMerge Pro v${this.version} - بدء التشغيل`);
            
            // تحميل الإعدادات المحفوظة
            this.loadSettings();
            
            // التحقق من دعم المتصفح
            this.checkBrowserSupport();
            
            // تهيئة المكونات
            await this.initializeComponents();
            
            // تطبيق الإعدادات
            this.applySettings();
            
            // إعداد معالجات الأحداث العامة
            this.setupGlobalEventHandlers();
            
            // تسجيل الأحداث التحليلية
            this.trackAppStart();
            
            this.isInitialized = true;
            console.log('✅ تم تشغيل التطبيق بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تشغيل التطبيق:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * تحميل الإعدادات المحفوظة
     */
    loadSettings() {
        const savedSettings = getFromLocalStorage('pdfmerge_settings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...savedSettings };
        }
    }

    /**
     * حفظ الإعدادات
     */
    saveSettings() {
        saveToLocalStorage('pdfmerge_settings', this.settings);
    }

    /**
     * التحقق من دعم المتصفح
     */
    checkBrowserSupport() {
        const requiredFeatures = [
            'FileReader',
            'Blob',
            'URL',
            'Promise',
            'fetch'
        ];

        const unsupportedFeatures = requiredFeatures.filter(feature => 
            !(feature in window)
        );

        if (unsupportedFeatures.length > 0) {
            throw new Error(`المتصفح لا يدعم: ${unsupportedFeatures.join(', ')}`);
        }

        // التحقق من PDF-lib
        if (typeof PDFLib === 'undefined') {
            throw new Error('مكتبة PDF-lib غير محملة');
        }

        console.log('✅ المتصفح يدعم جميع الميزات المطلوبة');
    }

    /**
     * تهيئة المكونات
     */
    async initializeComponents() {
        // التحقق من وجود المكونات المطلوبة
        if (typeof PDFProcessor === 'undefined') {
            throw new Error('معالج PDF غير متاح');
        }

        if (typeof UIController === 'undefined') {
            throw new Error('متحكم الواجهة غير متاح');
        }

        // تهيئة نظام الإشعارات
        this.initializeNotificationSystem();
        
        // تهيئة نظام التحليلات
        this.initializeAnalytics();

        // تسجيل Service Worker
        await this.registerServiceWorker();

        console.log('✅ تم تهيئة جميع المكونات');
    }

    /**
     * تهيئة نظام الإشعارات
     */
    initializeNotificationSystem() {
        // إنشاء حاوي الإشعارات
        if (!document.getElementById('notifications-container')) {
            const container = createElement('div', {
                id: 'notifications-container',
                className: 'notifications-container'
            });
            document.body.appendChild(container);
        }
    }

    /**
     * تهيئة نظام التحليلات
     */
    initializeAnalytics() {
        // تسجيل الأحداث المهمة (بدون إرسال بيانات شخصية)
        this.analytics = {
            sessionStart: Date.now(),
            filesProcessed: 0,
            mergesCompleted: 0,
            errors: 0
        };
    }

    /**
     * تطبيق الإعدادات
     */
    applySettings() {
        // تطبيق السمة
        document.documentElement.setAttribute('data-theme', this.settings.theme);
        
        // تطبيق اللغة
        document.documentElement.setAttribute('lang', this.settings.language);
        
        // تطبيق إعدادات المعالج
        if (window.pdfProcessor) {
            // تطبيق الحد الأقصى لحجم الملف
            // سيتم تنفيذ هذا في إصدار لاحق
        }
    }

    /**
     * إعداد معالجات الأحداث العامة
     */
    setupGlobalEventHandlers() {
        // معالجة الأخطاء العامة
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event.error, 'JavaScript Error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError(event.reason, 'Unhandled Promise Rejection');
        });

        // معالجة تغيير حالة الاتصال
        window.addEventListener('online', () => {
            this.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('لا يوجد اتصال بالإنترنت - التطبيق يعمل محلياً', 'warning');
        });

        // معالجة تغيير حجم النافذة
        window.addEventListener('resize', debounce(() => {
            this.handleWindowResize();
        }, 250));

        // معالجة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });

        // منع السحب والإفلات خارج المنطقة المحددة
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
        });
    }

    /**
     * معالجة الأخطاء العامة
     */
    handleGlobalError(error, context) {
        console.error(`خطأ عام في ${context}:`, error);
        
        this.analytics.errors++;
        
        // عرض رسالة خطأ مناسبة للمستخدم
        let userMessage = 'حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.';
        
        if (error.message && error.message.includes('memory')) {
            userMessage = 'نفدت الذاكرة المتاحة. يرجى تقليل حجم الملفات أو عددها.';
        } else if (error.message && error.message.includes('network')) {
            userMessage = 'مشكلة في الشبكة. تأكد من الاتصال بالإنترنت.';
        }
        
        this.showNotification(userMessage, 'error');
    }

    /**
     * معالجة خطأ التهيئة
     */
    handleInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'initialization-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h2>خطأ في تشغيل التطبيق</h2>
                <p>${error.message}</p>
                <button onclick="location.reload()" class="btn btn-primary">
                    إعادة تحميل الصفحة
                </button>
            </div>
        `;
        
        document.body.appendChild(errorContainer);
    }

    /**
     * معالجة تغيير حجم النافذة
     */
    handleWindowResize() {
        // إعادة حساب تخطيط العناصر إذا لزم الأمر
        console.log('تم تغيير حجم النافذة');
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + O: فتح ملفات
        if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
            event.preventDefault();
            document.getElementById('fileInput')?.click();
        }
        
        // Ctrl/Cmd + Enter: بدء الدمج
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            const mergeBtn = document.getElementById('mergeBtn');
            if (mergeBtn && !mergeBtn.disabled) {
                mergeBtn.click();
            }
        }
        
        // Escape: إغلاق المودال
        if (event.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal && !activeModal.classList.contains('no-close')) {
                activeModal.classList.remove('active');
            }
        }
    }

    /**
     * عرض إشعار متقدم
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        if (!container) return;

        const notification = createElement('div', {
            className: `notification notification-${type}`
        });

        const icon = this.getNotificationIcon(type);
        
        notification.innerHTML = `
            <div class="notification-content">
                <i class="${icon}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // إضافة الإشعار
        container.appendChild(notification);
        
        // تحريك الدخول
        setTimeout(() => {
            notification.classList.add('notification-show');
        }, 10);

        // معالجة الإغلاق
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // الإغلاق التلقائي
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * إزالة إشعار
     */
    removeNotification(notification) {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * تسجيل بدء التطبيق
     */
    trackAppStart() {
        console.log('📊 تم تسجيل بدء جلسة جديدة');
        
        // حفظ إحصائيات الاستخدام (محلياً فقط)
        const usage = getFromLocalStorage('pdfmerge_usage', {
            totalSessions: 0,
            totalMerges: 0,
            totalFiles: 0
        });
        
        usage.totalSessions++;
        usage.lastSession = new Date().toISOString();
        
        saveToLocalStorage('pdfmerge_usage', usage);
    }

    /**
     * تسجيل إحصائية
     */
    trackEvent(eventName, data = {}) {
        console.log(`📊 حدث: ${eventName}`, data);
        
        // تحديث الإحصائيات المحلية
        if (eventName === 'file_added') {
            this.analytics.filesProcessed++;
        } else if (eventName === 'merge_completed') {
            this.analytics.mergesCompleted++;
            
            // تحديث الإحصائيات المحفوظة
            const usage = getFromLocalStorage('pdfmerge_usage', {});
            usage.totalMerges = (usage.totalMerges || 0) + 1;
            usage.totalFiles = (usage.totalFiles || 0) + (data.fileCount || 0);
            saveToLocalStorage('pdfmerge_usage', usage);
        }
    }

    /**
     * الحصول على إحصائيات الجلسة
     */
    getSessionStats() {
        return {
            ...this.analytics,
            sessionDuration: Date.now() - this.analytics.sessionStart
        };
    }

    /**
     * تسجيل Service Worker
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('✅ Service Worker: تم التسجيل بنجاح', registration.scope);

                // معالجة التحديثات
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showNotification(
                                'تحديث جديد متاح! أعد تحميل الصفحة للحصول على أحدث إصدار.',
                                'info',
                                0
                            );
                        }
                    });
                });

            } catch (error) {
                console.warn('⚠️ Service Worker: فشل في التسجيل', error);
            }
        } else {
            console.warn('⚠️ Service Worker غير مدعوم في هذا المتصفح');
        }
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        console.log('🧹 تنظيف موارد التطبيق');
        
        // مسح الملفات المؤقتة
        if (window.pdfProcessor) {
            pdfProcessor.clearFiles();
        }
        
        // مسح الإشعارات
        const container = document.getElementById('notifications-container');
        if (container) {
            container.innerHTML = '';
        }
        
        // حفظ الإعدادات
        this.saveSettings();
    }
}

// إنشاء مثيل التطبيق
let app;

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app = new PDFMergeApp();
});

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    if (app) {
        app.cleanup();
    }
});

// تصدير للاستخدام العام
if (typeof window !== 'undefined') {
    window.PDFMergeApp = PDFMergeApp;
}
