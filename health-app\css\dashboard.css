/* ===== DASHBOARD STYLES ===== */

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stat-card.sleep-stat::before {
    background: var(--gradient-sleep);
}

.stat-card.water-stat::before {
    background: var(--gradient-water);
}

.stat-card.exercise-stat::before {
    background: var(--gradient-exercise);
}

.stat-card.nutrition-stat::before {
    background: var(--gradient-nutrition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    background: var(--gradient-primary);
}

.sleep-stat .stat-icon {
    background: var(--gradient-sleep);
}

.water-stat .stat-icon {
    background: var(--gradient-water);
}

.exercise-stat .stat-icon {
    background: var(--gradient-exercise);
}

.nutrition-stat .stat-icon {
    background: var(--gradient-nutrition);
}

.stat-info h3 {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.stat-value {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.stat-value span {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-900);
}

.stat-value small {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.stat-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-progress .progress-bar {
    flex: 1;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.stat-progress .progress-fill {
    height: 100%;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.sleep-stat .progress-fill {
    background: var(--gradient-sleep);
}

.water-stat .progress-fill {
    background: var(--gradient-water);
}

.exercise-stat .progress-fill {
    background: var(--gradient-exercise);
}

.nutrition-stat .progress-fill {
    background: var(--gradient-nutrition);
}

.stat-progress span {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-600);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.card-action {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.card-action:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.card-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: color var(--transition-fast);
}

.card-link:hover {
    color: var(--primary-dark);
}

.card-select {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    cursor: pointer;
}

.card-content {
    padding: var(--spacing-xl);
}

/* Overview Chart */
.overview-chart {
    height: 300px;
    position: relative;
}

/* Activity List */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--gray-100);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
    background: var(--gradient-primary);
}

.activity-icon.sleep {
    background: var(--gradient-sleep);
}

.activity-icon.water {
    background: var(--gradient-water);
}

.activity-icon.exercise {
    background: var(--gradient-exercise);
}

.activity-icon.nutrition {
    background: var(--gradient-nutrition);
}

.activity-details {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.activity-value {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
}

/* Achievements Grid */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);
}

.achievement-item {
    text-align: center;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    background: var(--gray-50);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.achievement-item:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

.achievement-item.unlocked {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid var(--primary-color);
}

.achievement-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
    color: var(--gray-400);
}

.achievement-item.unlocked .achievement-icon {
    color: var(--primary-color);
}

.achievement-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.achievement-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.4;
}

/* Weekly Chart */
.weekly-chart {
    height: 250px;
    position: relative;
}

/* Quick Actions */
.quick-actions {
    margin-top: var(--spacing-2xl);
}

.quick-actions h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--gray-800);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.action-btn {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    color: var(--gray-700);
}

.action-btn:hover {
    border-color: var(--primary-color);
    background: rgba(16, 185, 129, 0.05);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.action-btn i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(16, 185, 129, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn span {
    font-weight: 600;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .quick-stats {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .achievements-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .achievements-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: var(--spacing-lg);
    }
    
    .card-content {
        padding: var(--spacing-lg);
    }
}
